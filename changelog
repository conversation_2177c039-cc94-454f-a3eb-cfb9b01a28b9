# LXND Changelog
## v1.0.0.7 - 2025-07-18
- Fixed admin email management 404 errors
- Added missing email template and account management routes
- Implemented EmailAccount.get_user() method for template compatibility
- Added comprehensive email account management templates
- Enhanced email account limits based on user rank:
  - Regular users: 1 email account (10GB storage)
  - Lux users: 3 email accounts (1TB storage each)
  - Admins: Unlimited email accounts (1TB storage each)
- Updated email dashboard to show account usage and limits
- Added account limit validation for email account creation
- Enhanced Lux dashboard to display email account benefits
- Implemented User.get_storage_used_formatted() method for better storage display
- Updated all dashboard templates to use formatted storage display (B/KB/MB/GB)
- Improved storage visualization across dashboard, files, profile, and Lux pages
- Fixed dashboard storage display showing "0 B / ∞" for normal users
- Implemented advanced license creation system with modals:
  - Custom license key generation (e.g. "key123")
  - Duration settings (days/months/years or unlimited)
  - Bulk creation with custom prefixes (e.g. "BULK-001", "BULK-002")
  - Form validation and error handling
  - Progress notifications for bulk operations
- Added Website Service for free website hosting:
  - Custom subdomain websites (e.g. username.lxnd.cloud)
  - Enhanced storage limits: Regular users (100MB), Lux users (2GB), Admins (10GB)
  - User limits: Regular users (1 website), Lux users (5 websites), Admins (unlimited)
  - Automatic SSL certificate generation using Let's Encrypt
  - SFTP access for file management with unique credentials per website
  - Automatic subdomain routing and file serving
  - Index.html support for homepage creation
  - Website dashboard with storage usage, SSL status, and SFTP details
  - Integrated into main dashboard with quick access
  - SFTP setup scripts and automatic user management
  - Copy-to-clipboard functionality for SFTP credentials
  - Website deletion functionality with complete cleanup (files, SFTP users, SSL certificates)
  - On-demand SSL certificate generation with "Generate SSL" button
  - Admin website management dashboard with statistics and controls
  - Website status toggle and admin deletion capabilities
  - Enhanced SFTP credential display with proper error handling
  - Improved SSL status indicators and generation workflow
  - Fixed SSL certificate generation with Let's Encrypt integration and self-signed fallback
  - Redesigned SSL & SFTP cards in website detail view to match other dashboard cards
  - Implemented proper Flask-based subdomain routing without Nginx dependency
  - Added ACME challenge support for Let's Encrypt verification
  - Enhanced website templates with improved styling and user experience
  - Fixed SFTP permission issues with improved user setup and "Fix Permissions" button
  - Redesigned website detail layout: combined SSL & SFTP into one compact card
  - Restored website deletion functionality with confirmation modal
  - Added detailed SFTP connection instructions for FileZilla, WinSCP, and command line
  - Improved SFTP troubleshooting with automatic permission repair
- Fixed admin email management 404 errors
- Added missing email template and account management routes:
  - /admin/email/template/ID/preview
  - /email/template/ID/edit
  - /admin/email/account/ID
  - /admin/email/template/new
- Implemented EmailAccount.get_user() method for template compatibility
- Added comprehensive email account management templates
- Fixed template rendering issues with undefined user relationships
- Added password reset functionality for email accounts
- Enhanced email account detail views with storage usage and recent messages

## v1.0.0.6 - 2025-07-17 - Lux Premium System

### Added
- **Lux Premium Rank**: New premium user tier with enhanced features and limits
- **License System**: Complete license key system for Lux activation
- **Enhanced Limits**: Lux users get 10GB storage, 50 projects, 1000 short URLs
- **License API**: External API endpoint for license validation
- **Admin License Management**: Full admin interface for creating and managing Lux licenses
- **Automatic Expiration**: Support for temporary and permanent Lux licenses
- **Premium Dashboard**: Dedicated Lux dashboard with usage statistics

### Updated
- **Landing Page**: Redesigned with Lux Premium promotion and new service cards
- **Navigation**: Added premium Lux navigation with crown icon
- **User Limits**: Dynamic limits based on user rank (Regular/Lux/Admin)
- **Dashboard**: Enhanced user dashboard with Lux status and premium features

### Fixed
- **Template Error**: Fixed User model access in Lux license admin template
- **Admin Dashboard**: Completed Lux management integration with statistics

### Legal Compliance
- **Footer**: Added comprehensive footer with service and legal links
- **Cookie Banner**: EU-compliant cookie consent banner with granular controls
- **Impressum**: German-compliant legal notice page
- **Privacy Policy**: GDPR-compliant privacy policy (German/English)
- **Terms of Service**: Comprehensive AGB covering all services
- **Cookie Policy**: Detailed cookie usage and management page

### Dashboard Improvements
- **Complete Theme Overhaul**: All dashboards redesigned with modern gradient headers and rounded corners
- **User Dashboard**: Enhanced with better organization, Lux status integration, and improved text
- **Admin Dashboard**: Completely rebuilt with comprehensive management tools and real-time statistics
- **Files Dashboard**: Fixed drag & drop functionality, CSRF token handling, and file size validation
- **Email Dashboard**: Completely redesigned with glassmorphism design and professional interface
- **Lux Dashboard**: Restored to original design as requested
- **Responsive Design**: All dashboards fully responsive with consistent modern styling
- **Interactive Elements**: Added hover effects, transitions, and better visual feedback
- **Quick Actions**: Enhanced sidebar panels with improved navigation and functionality

### Template Redesigns
- **Profile Page**: Complete redesign with modern glassmorphism design, enhanced user info display, and password change functionality
- **Create Project**: Redesigned with project type selection, privacy settings, and improved form layout
- **Login Page**: Modern authentication interface with password visibility toggle and enhanced security features
- **Register Page**: Complete registration redesign with password strength validation and terms acceptance
- **Email Dashboard**: Fixed URL routing issues and completely redesigned with glassmorphism theme
- **Project Detail**: Complete redesign with license management, statistics, and modern interface
- **API Documentation**: Redesigned with better navigation, code examples, and modern styling
- **Email Compose**: Redesigned with modern glassmorphism header and improved layout
- **Link Shortener**: Updated with new gradient header design and better form styling
- **404 Page**: Complete redesign with modern error page and helpful navigation

### Added
- **Upload Progress Bar**: Added real-time upload progress tracking with speed and ETA display for file uploads
- **Increased Upload Limit**: Raised maximum file upload size from 100MB to 10GB for better file handling
- **Custom Jinja2 Filter**: Added regex_replace filter to handle template rendering issues
- **New Admin Panel Structure**: Complete redesign of admin panel with modern navigation and organized sections
- **Advanced User Management**: New comprehensive user management interface with search, filtering, and bulk actions
- **System Settings Panel**: Real system configuration interface for application settings, security, storage, and email
- **Service Management**: Dedicated service management for Discord bot, email server, database, and system monitoring
- **Analytics Dashboard**: Comprehensive analytics with charts, usage statistics, and system health monitoring
- **Focused Discord Bot Management**: Specialized Discord bot interface focused on actual bot functionality and commands

### Fixed
- **CSRF Token Issues**: Fixed UndefinedError in Files Dashboard and Profile templates - added CSRFProtect initialization to Flask app
- **Regex Replace Filter**: Fixed TemplateRuntimeError for missing regex_replace filter in files dashboard
- **File Upload Feedback**: Fixed issue where nothing happened after file upload completion - added proper success/error notifications, visual feedback, and progress tracking
- **SQLAlchemy Legacy Warnings**: Updated deprecated Query.get() method calls to use Session.get() for SQLAlchemy 2.0 compatibility
- **File Upload Display Issue**: Fixed files not appearing after upload by creating session-based AJAX upload endpoint that doesn't require API token authentication

### Redesigned
- **Complete Admin Panel Overhaul**: Removed all old admin templates and routes, redesigned admin dashboard to match main dashboard style
- **New Admin Structure**: Created focused admin sections - User Management, Project Management, Email Management, and System Management
- **Modern Admin Interface**: Clean, organized admin interface with proper navigation and consistent design patterns
- **Enhanced User Management**: Advanced user search, filtering, role management, and bulk operations
- **Comprehensive Project Management**: Project and license management with detailed views and actions
- **Professional Email Management**: Email account management, server settings, and template management
- **Advanced System Management**: System settings, monitoring, maintenance tools, and log viewing

### Fixed
- **Admin Template Errors**: Fixed Project model relationship errors (project.user -> project.owner) and License model attribute issues
- **System Monitoring**: Added missing system monitoring API endpoints for CPU, memory, disk usage, and system logs
- **Duplicate Routes**: Removed duplicate admin email route and cleaned up route structure
- **Dashboard Variables**: Fixed missing total_storage_mb and other statistics variables in dashboard route
- **Admin Dashboard URLs**: Fixed all BuildError issues - corrected non-existent route references to proper admin routes
- **Admin Dashboard**: Fixed AttributeError - User object has no 'licenses' attribute, corrected statistics calculation
- **Register Template**: Fixed 'confirm_password' field error - corrected to 'password2' field name
- **Profile Template**: Fixed User model method calls - corrected storage calculation methods
- **Email Check API**: Fixed string formatting error in email check result handling
- **Login Template**: Fixed BuildError for 'forgot_password' endpoint - removed non-existent route reference
- **Profile Template**: Fixed 'change_password' URL to correct 'change_account_password' endpoint
- **Email Dashboard Route**: Fixed redirect issue - now shows dashboard instead of going directly to inbox
- **Email Template URLs**: Fixed BuildError for 'create_email_account' - corrected to 'user_create_email_account'
- **File Upload**: Fixed CSRF token missing error and added client-side file size validation
- **413 Error**: Added file size checking before upload to prevent server errors
- **Lux Design**: Restored original Lux dashboard design
- **Bot Stop Function**: Enhanced Discord bot stop functionality with graceful shutdown using stop flags
- **404 Page**: Redesigned with modern glassmorphism design and better navigation

## v1.0.0.5 - 2025-07-17 - Link Shortener Service

### Added
- **Link Shortener Service**: Complete URL shortening service with analytics
- **Custom Short Codes**: Users can create custom short codes for their links
- **Password Protection**: Optional password protection for sensitive links
- **Link Expiration**: Set automatic expiration dates for temporary links
- **Click Analytics**: Track clicks, devices, browsers, and referrers
- **Admin Management**: Full admin interface for managing all shortened URLs
- **Public/Private Links**: Control visibility of links in public listings

### Fixed
- **Form Validation**: Fixed "Not a valid datetime value" error for empty expiration dates
- **Optional Fields**: All optional fields now properly handle empty values
- **Custom Code Validation**: Added format validation for custom short codes
- **Navigation**: Removed icon from shortener navigation link
- **User Dashboard**: Added shortener statistics card to user dashboard

## v1.0.0.4 - 2025-07-17 - Email Reply System & Bug Fixes

### Added
- **Email Reply Functionality**: Users can now reply to emails with proper threading
- **Enhanced Email Display**: Reply buttons added to inbox for received emails
- **Thread Support**: Email threads are now properly tracked with in_reply_to and thread_id fields
- **Reply Templates**: New reply template with quoted original message

### Fixed
- **Fixed Reply Form**: Added missing from_account field to ComposeEmailForm
- **Improved Email Parsing**: Clean email content display instead of raw headers
- **Fixed Email Sorting**: Emails now sorted by timestamp (newest first) instead of type
- **Enhanced Email Processing**: Better handling of multipart emails and HTML content
- **Fixed Compose Form**: Corrected from_account field choices in compose email form
- **Fixed Global Form Error**: Resolved "Choices cannot be None" error in email composition
- **Enhanced CLI Display**: Beautiful ASCII logo and service status table on startup
- **Improved Size Display**: Smart KB/MB/GB formatting for email and storage sizes
- **Fixed SMTP for Email Clients**: Added port 587 submission server for Thunderbird/Outlook
- **Better Storage Display**: Accurate size formatting in inbox and account overview
- **Fixed Storage Calculation**: Storage now shows actual usage (8.4 KB) instead of rounded MB
- **Smart Storage Updates**: Automatic recalculation when emails are sent/received
- **Accurate Size Display**: Real-time storage usage based on actual email sizes

## v1.0.0.3 - 2025-07-16 - MySQL Server Setup

### Added
- **MySQL/MariaDB Server Setup**: Complete MySQL server installation and configuration
- **phpMyAdmin Integration**: Web-based MySQL administration interface on port 8080
- **MySQL Admin Dashboard**: Integrated MySQL administration panel in the main dashboard
- **Database Management**: Automated database and user creation for LXND services
- **MySQL Manager Class**: Python class for MySQL operations with PyMySQL support
- **Database Statistics**: Real-time database statistics and monitoring
- **SQL Query Interface**: Execute SQL queries directly from the admin panel
- **Database Backup System**: Automated database backup creation and download
- **Multi-Database Support**: Separate databases for main app, email, and Discord bot
- **Security Configuration**: Secure MySQL installation with proper user permissions

### Technical Details
- MariaDB 10.5 server installation on Debian 12
- Apache 2.4 with PHP 7.4 for phpMyAdmin
- PyMySQL connector for Python database operations
- Automated table creation for all LXND services
- Port 8080 configuration for phpMyAdmin access
- Firewall configuration for database access

### Database Structure
- `lxnd_main`: Main application database
- `lxnd_email`: Email system database
- `lxnd_discord`: Discord bot database
- `phpmyadmin`: phpMyAdmin configuration database

### Access Information
- phpMyAdmin URL: http://lxnd.cloud:8080
- MySQL Admin Panel: Available in dashboard under "MySQL Database"
- Database credentials stored in: `/root/lxnd/mysql_credentials.json`

### Migration Completed
- **Complete SQLite to MySQL Migration**: All data storage now uses MySQL instead of SQLite
- **Email Server MySQL Integration**: Email server (mailserver.py) now uses MySQL for storing sent/received emails
- **Dashboard Integration**: MySQL administration fully integrated into Flowbite-designed dashboard
- **English Language**: All interface text converted to English
- **Removed Unnecessary Files**: Deleted `sent.html` template (functionality integrated into inbox)
- **Updated Templates**: All admin templates now use Flowbite design system with base.html extension
- **Fixed Email Display**: Emails are now properly stored and displayed in the dashboard
- **Fixed Password Change**: Corrected password change functionality for both account and email passwords
- **Fixed Email Storage**: Corrected is_deleted field to prevent NULL values that caused emails to not appear
- **Fixed Hostname**: Set system hostname to mail.lxnd.cloud for proper email delivery
- **Fixed SQL Queries**: All email server queries now use correct MySQL syntax


## v1.0.0.2 - 2025-07-15 - External Email Support

### Added
- External email sending support with SMTP configuration