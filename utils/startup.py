import os
import sys

def detect_environment():
    """Detect the current environment"""
    
    # Check for common production indicators
    if os.environ.get('FLASK_ENV') == 'production':
        return 'production'
    
    if os.environ.get('FLASK_ENV') == 'development':
        return 'development'
    
    # Check if running in a container or server environment
    if os.path.exists('/.dockerenv') or os.environ.get('CONTAINER'):
        return 'production'
    
    # Check if SSL certificates exist (indicates production)
    if os.path.exists('/etc/letsencrypt/live'):
        return 'production'
    
    # Check if running as root (often indicates server)
    if os.geteuid() == 0:
        return 'production'
    
    # Default to interactive mode
    return 'interactive'

def startup_menu():
    """Show startup menu and get user choice"""
    
    print("\n" + "="*50)
    print("🚀 LXND - All-in-One Platform")
    print("="*50)
    
    # Show SSL certificate status
    from utils.ssl_manager import SSLManager
    ssl_manager = SSLManager('/etc/letsencrypt/live', ['lxnd.cloud', 'test.lxnd.cloud', 'luxend.lxnd.cloud'])
    ssl_manager.print_certificate_info()
    
    print("\n🎛️  Startup Options:")
    print("  1. HTTP only (Port 5000) - Development")
    print("  2. HTTPS only (Port 443) - Production") 
    print("  3. Auto-detect - Smart selection")
    print()
    
    while True:
        try:
            choice = input("Select mode (1-3) [3]: ").strip()
            if not choice:
                choice = '3'
            
            if choice in ['1', '2', '3']:
                return choice
            else:
                print("❌ Invalid choice. Please enter 1, 2, or 3.")
                
        except (KeyboardInterrupt, EOFError):
            print("\n❌ Startup cancelled by user")
            sys.exit(0)

def print_startup_banner():
    """Print startup banner"""
    
    banner = """
    ██╗     ██╗  ██╗███╗   ██╗██████╗ 
    ██║     ╚██╗██╔╝████╗  ██║██╔══██╗
    ██║      ╚███╔╝ ██╔██╗ ██║██║  ██║
    ██║      ██╔██╗ ██║╚██╗██║██║  ██║
    ███████╗██╔╝ ██╗██║ ╚████║██████╔╝
    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═════╝ 
    
    All-in-One Platform for Modern Development
    """
    
    print(banner)

def check_system_requirements():
    """Check system requirements"""
    
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print(f"  ✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check required directories
    required_dirs = ['uploads', 'websites', 'backups', 'templates']
    for directory in required_dirs:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"  ✓ Created directory: {directory}")
            except Exception as e:
                print(f"  ❌ Could not create directory {directory}: {e}")
                return False
        else:
            print(f"  ✓ Directory exists: {directory}")
    
    # Check write permissions
    test_file = 'test_write_permission.tmp'
    try:
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        print("  ✓ Write permissions OK")
    except Exception as e:
        print(f"  ❌ Write permission error: {e}")
        return False
    
    return True

def show_startup_info(mode, host='0.0.0.0', port=5000, ssl=False):
    """Show startup information"""
    
    protocol = 'https' if ssl else 'http'
    
    print("\n" + "="*50)
    print("🎉 LXND Started Successfully!")
    print("="*50)
    print(f"🌐 Mode: {mode}")
    print(f"🔗 URL: {protocol}://{host}:{port}")
    
    if ssl:
        print("🔒 SSL: Enabled")
    else:
        print("⚠️  SSL: Disabled (Development mode)")
    
    print("\n📋 Available Services:")
    print("  • Project Management")
    print("  • File Upload & Sharing") 
    print("  • Email Management")
    print("  • Website Hosting")
    print("  • URL Shortener")
    print("  • Discord Integration")
    
    print("\n🛠️  Admin Panel:")
    print(f"  {protocol}://{host}:{port}/admin")
    
    print("\n📚 API Documentation:")
    print(f"  {protocol}://{host}:{port}/api/docs")
    
    print("\n" + "="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")

def handle_startup_error(error):
    """Handle startup errors gracefully"""
    
    print(f"\n❌ Startup Error: {error}")
    print("\n🔧 Troubleshooting:")
    
    if "Permission denied" in str(error):
        print("  • Check file permissions")
        print("  • Try running with sudo (for port 443)")
        
    elif "Address already in use" in str(error):
        print("  • Another service is using this port")
        print("  • Try a different port or stop the conflicting service")
        
    elif "SSL" in str(error) or "certificate" in str(error):
        print("  • Check SSL certificate paths")
        print("  • Verify certificate validity")
        print("  • Try running in HTTP mode for testing")
        
    else:
        print("  • Check the error message above")
        print("  • Verify system requirements")
        print("  • Check log files for more details")
    
    print("\n🔄 Please fix the issue and try again.")
    sys.exit(1)
