import os
import ssl
from pathlib import Path

class SSLManager:
    """Manage SSL certificates for the application"""
    
    def __init__(self, cert_base_path, domains):
        self.cert_base_path = cert_base_path
        self.domains = domains
        self.ssl_context = None
        
    def get_ssl_context(self):
        """Get SSL context with certificates"""
        if self.ssl_context:
            return self.ssl_context
            
        # Try to load SSL certificates
        cert_info = self._load_certificates()
        
        if not cert_info:
            return None
            
        try:
            # Create SSL context
            context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            context.load_cert_chain(cert_info['cert_path'], cert_info['key_path'])
            
            # Configure security settings
            context.minimum_version = ssl.TLSVersion.TLSv1_2
            context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
            
            self.ssl_context = context
            return context
            
        except Exception as e:
            print(f"❌ Error creating SSL context: {e}")
            return None
    
    def _load_certificates(self):
        """Load SSL certificates from Let's Encrypt or custom location"""
        print("🔒 Loading SSL certificates...")
        
        # Check each domain for certificates
        for domain in self.domains:
            cert_dir = Path(self.cert_base_path) / domain
            cert_path = cert_dir / 'fullchain.pem'
            key_path = cert_dir / 'privkey.pem'
            
            if cert_path.exists() and key_path.exists():
                print(f"  ✓ Let's Encrypt: {domain}")
                return {
                    'cert_path': str(cert_path),
                    'key_path': str(key_path),
                    'domain': domain
                }
        
        # Check for custom certificates in current directory
        custom_cert = Path('ssl/cert.pem')
        custom_key = Path('ssl/key.pem')
        
        if custom_cert.exists() and custom_key.exists():
            print("  ✓ Custom SSL certificate found")
            return {
                'cert_path': str(custom_cert),
                'key_path': str(custom_key),
                'domain': 'custom'
            }
        
        print("  ❌ No SSL certificates found")
        return None
    
    def check_certificate_expiry(self):
        """Check when certificates expire"""
        import datetime
        from cryptography import x509
        from cryptography.hazmat.backends import default_backend
        
        cert_info = self._load_certificates()
        if not cert_info:
            return None
            
        try:
            with open(cert_info['cert_path'], 'rb') as f:
                cert_data = f.read()
                
            cert = x509.load_pem_x509_certificate(cert_data, default_backend())
            expiry_date = cert.not_valid_after
            days_until_expiry = (expiry_date - datetime.datetime.now()).days
            
            return {
                'domain': cert_info['domain'],
                'expiry_date': expiry_date,
                'days_until_expiry': days_until_expiry,
                'expires_soon': days_until_expiry < 30
            }
            
        except Exception as e:
            print(f"Error checking certificate expiry: {e}")
            return None
    
    def list_available_certificates(self):
        """List all available certificates"""
        certificates = []
        
        # Check Let's Encrypt certificates
        if os.path.exists(self.cert_base_path):
            for domain in os.listdir(self.cert_base_path):
                cert_dir = Path(self.cert_base_path) / domain
                cert_path = cert_dir / 'fullchain.pem'
                key_path = cert_dir / 'privkey.pem'
                
                if cert_path.exists() and key_path.exists():
                    certificates.append({
                        'domain': domain,
                        'type': 'letsencrypt',
                        'cert_path': str(cert_path),
                        'key_path': str(key_path)
                    })
        
        # Check custom certificates
        custom_cert = Path('ssl/cert.pem')
        custom_key = Path('ssl/key.pem')
        
        if custom_cert.exists() and custom_key.exists():
            certificates.append({
                'domain': 'custom',
                'type': 'custom',
                'cert_path': str(custom_cert),
                'key_path': str(custom_key)
            })
        
        return certificates
    
    def print_certificate_info(self):
        """Print information about loaded certificates"""
        certificates = self.list_available_certificates()
        
        if not certificates:
            print("❌ No SSL certificates found")
            return
            
        print(f"📋 Loaded {len(certificates)} SSL certificate(s)")
        for cert in certificates:
            cert_type = "Let's Encrypt" if cert['type'] == 'letsencrypt' else "Custom"
            print(f"  🟢 {cert['domain']} ({cert_type})")
            
        # Check expiry for the first certificate
        expiry_info = self.check_certificate_expiry()
        if expiry_info:
            if expiry_info['expires_soon']:
                print(f"⚠️  Certificate for {expiry_info['domain']} expires in {expiry_info['days_until_expiry']} days!")
            else:
                print(f"✅ Certificate valid for {expiry_info['days_until_expiry']} more days")
