from flask_wtf import F<PERSON>kForm
from flask_wtf.file import <PERSON><PERSON><PERSON>, FileRequired, FileAllowed
from wtforms import <PERSON>Field, PasswordField, SubmitField, SelectField, IntegerField, TextAreaField, BooleanField, DateTimeLocalField
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError, Optional, NumberRange
from wtforms.widgets import TextArea

from models.user import User
from models.project import Project
from models.email import EmailTemplate

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[<PERSON>Required(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
    password2 = PasswordField('Repeat Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Register')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Please use a different email address.')

class ProjectForm(FlaskForm):
    name = StringField('Project Name', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Create Project')

    def validate_name(self, name):
        from flask_login import current_user
        project = Project.query.filter_by(name=name.data, user_id=current_user.id).first()
        if project is not None:
            raise ValidationError('You already have a project with this name.')

class FileUploadForm(FlaskForm):
    file = FileField('File', validators=[FileRequired()])
    public = BooleanField('Make file public')
    submit = SubmitField('Upload')

class EmailAccountForm(FlaskForm):
    email_address = StringField('Email Address', validators=[DataRequired(), Email(), Length(max=255)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    display_name = StringField('Display Name', validators=[Optional(), Length(max=100)])
    storage_limit_mb = IntegerField('Storage Limit (MB)', validators=[DataRequired(), NumberRange(min=1, max=102400)], default=10240)
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Create Account')

class EmailTemplateForm(FlaskForm):
    name = StringField('Template Name', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    template_type = SelectField('Template Type', 
                               choices=[('welcome', 'Welcome'), ('notification', 'Notification'), ('custom', 'Custom')],
                               validators=[DataRequired()])
    subject = StringField('Email Subject', validators=[DataRequired(), Length(max=500)])
    body_text = TextAreaField('Plain Text Body', validators=[Optional()])
    body_html = TextAreaField('HTML Body', validators=[Optional()], widget=TextArea())
    is_active = BooleanField('Active Template', default=True)
    submit = SubmitField('Save Template')

    def validate_name(self, name):
        # Check if template name already exists (excluding current template in edit mode)
        template = EmailTemplate.query.filter_by(name=name.data).first()
        if template and (not hasattr(self, '_template_id') or template.id != self._template_id):
            raise ValidationError('Template name already exists.')

class WebsiteForm(FlaskForm):
    subdomain = StringField('Subdomain', validators=[DataRequired(), Length(min=3, max=50)])
    title = StringField('Website Title', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    ssl_enabled = BooleanField('Enable SSL', default=True)
    submit = SubmitField('Create Website')

class ShortenURLForm(FlaskForm):
    original_url = StringField('URL to Shorten', validators=[DataRequired(), Length(max=2000)])
    custom_code = StringField('Custom Short Code (Optional)', validators=[Optional(), Length(min=3, max=10)])
    title = StringField('Title (Optional)', validators=[Optional(), Length(max=200)])
    description = TextAreaField('Description (Optional)', validators=[Optional(), Length(max=500)])
    password = PasswordField('Password Protection (Optional)', validators=[Optional(), Length(min=4)])
    expires_at = DateTimeLocalField('Expiration Date (Optional)', validators=[Optional()])
    submit = SubmitField('Shorten URL')

class DiscordSettingsForm(FlaskForm):
    bot_token = StringField('Bot Token', validators=[DataRequired(), Length(max=255)])
    guild_id = StringField('Server ID', validators=[DataRequired(), Length(max=20)])
    
    # Channels
    log_channel_id = StringField('Log Channel ID', validators=[Optional(), Length(max=20)])
    welcome_channel_id = StringField('Welcome Channel ID', validators=[Optional(), Length(max=20)])
    verification_channel_id = StringField('Verification Channel ID', validators=[Optional(), Length(max=20)])
    
    # Roles
    verified_role_id = StringField('Verified Role ID', validators=[Optional(), Length(max=20)])
    lux_role_id = StringField('Lux Role ID', validators=[Optional(), Length(max=20)])
    admin_role_id = StringField('Admin Role ID', validators=[Optional(), Length(max=20)])
    
    # Features
    verification_enabled = BooleanField('Enable Verification System', default=True)
    ticket_system_enabled = BooleanField('Enable Ticket System', default=True)
    auto_role_enabled = BooleanField('Enable Auto Role Assignment', default=True)
    
    # Messages
    welcome_message = TextAreaField('Welcome Message', validators=[Optional(), Length(max=1000)])
    verification_message = TextAreaField('Verification Message', validators=[Optional(), Length(max=1000)])
    
    submit = SubmitField('Save Settings')
