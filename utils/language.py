import json
import os
from flask import session, request

# Language data storage
_messages = {}

def load_language_files():
    """Load all language files"""
    global _messages
    
    languages_dir = 'languages'
    if not os.path.exists(languages_dir):
        # Create default English messages if no language files exist
        _messages = {
            'en': {
                'base.html': {
                    'site_title': 'LXND',
                    'nav_dashboard': 'Dashboard',
                    'nav_projects': 'Projects',
                    'nav_files': 'Files',
                    'nav_email': 'Email',
                    'nav_websites': 'Websites',
                    'nav_admin': 'Admin',
                    'nav_profile': 'Profile',
                    'nav_logout': 'Logout',
                    'nav_login': 'Login',
                    'nav_register': 'Register'
                },
                'landing.html': {
                    'hero_title': 'Welcome to LXND',
                    'hero_subtitle': 'Your all-in-one platform for project management, file sharing, and more',
                    'btn_get_started': 'Get Started',
                    'btn_login': 'Login',
                    'btn_create_account': 'Create Account',
                    'cta_title': 'Ready to get started?',
                    'cta_subtitle': 'Join thousands of users who trust LXND for their projects'
                },
                'dashboard.html': {
                    'welcome_back': 'Welcome back',
                    'quick_stats': 'Quick Stats',
                    'recent_activity': 'Recent Activity',
                    'storage_usage': 'Storage Usage'
                }
            }
        }
        return
    
    # Load language files from disk
    for filename in os.listdir(languages_dir):
        if filename.endswith('.json'):
            lang_code = filename[:-5]  # Remove .json extension
            try:
                with open(os.path.join(languages_dir, filename), 'r', encoding='utf-8') as f:
                    _messages[lang_code] = json.load(f)
            except Exception as e:
                print(f"Error loading language file {filename}: {e}")

def get_user_language():
    """Get user's preferred language"""
    # Check session first
    if 'language' in session:
        return session['language']
    
    # Check Accept-Language header
    if request and hasattr(request, 'accept_languages'):
        for lang in request.accept_languages:
            if lang[0][:2] in _messages:
                return lang[0][:2]
    
    # Default to English
    return 'en'

def get_messages(lang=None):
    """Get messages for specified language"""
    if lang is None:
        lang = get_user_language()
    
    return _messages.get(lang, _messages.get('en', {}))

def set_language(lang_code):
    """Set user's language preference"""
    if lang_code in _messages:
        session['language'] = lang_code
        return True
    return False

def init_language_support(app):
    """Initialize language support for the app"""
    load_language_files()
    
    @app.context_processor
    def inject_language():
        """Inject language data into all templates"""
        return {
            'messages': get_messages(),
            'current_language': get_user_language(),
            'available_languages': list(_messages.keys())
        }
    
    @app.route('/set_language/<lang_code>')
    def set_language_route(lang_code):
        """Route to change language"""
        from flask import redirect, request, flash
        
        if set_language(lang_code):
            flash(f'Language changed to {lang_code}', 'success')
        else:
            flash('Language not available', 'error')
        
        # Redirect back to referring page or dashboard
        return redirect(request.referrer or '/dashboard')

def create_default_language_files():
    """Create default language files"""
    os.makedirs('languages', exist_ok=True)
    
    # English
    en_messages = {
        'base.html': {
            'site_title': 'LXND',
            'nav_dashboard': 'Dashboard',
            'nav_projects': 'Projects',
            'nav_files': 'Files',
            'nav_email': 'Email',
            'nav_websites': 'Websites',
            'nav_admin': 'Admin',
            'nav_profile': 'Profile',
            'nav_logout': 'Logout',
            'nav_login': 'Login',
            'nav_register': 'Register'
        },
        'landing.html': {
            'hero_title': 'Welcome to LXND',
            'hero_subtitle': 'Your all-in-one platform for project management, file sharing, and more',
            'btn_get_started': 'Get Started',
            'btn_login': 'Login',
            'btn_create_account': 'Create Account',
            'cta_title': 'Ready to get started?',
            'cta_subtitle': 'Join thousands of users who trust LXND for their projects'
        }
    }
    
    # German
    de_messages = {
        'base.html': {
            'site_title': 'LXND',
            'nav_dashboard': 'Dashboard',
            'nav_projects': 'Projekte',
            'nav_files': 'Dateien',
            'nav_email': 'E-Mail',
            'nav_websites': 'Websites',
            'nav_admin': 'Admin',
            'nav_profile': 'Profil',
            'nav_logout': 'Abmelden',
            'nav_login': 'Anmelden',
            'nav_register': 'Registrieren'
        },
        'landing.html': {
            'hero_title': 'Willkommen bei LXND',
            'hero_subtitle': 'Ihre All-in-One-Plattform für Projektmanagement, Dateiaustausch und mehr',
            'btn_get_started': 'Loslegen',
            'btn_login': 'Anmelden',
            'btn_create_account': 'Konto erstellen',
            'cta_title': 'Bereit anzufangen?',
            'cta_subtitle': 'Schließen Sie sich Tausenden von Benutzern an, die LXND für ihre Projekte vertrauen'
        }
    }
    
    # Save language files
    with open('languages/en.json', 'w', encoding='utf-8') as f:
        json.dump(en_messages, f, indent=2, ensure_ascii=False)
    
    with open('languages/de.json', 'w', encoding='utf-8') as f:
        json.dump(de_messages, f, indent=2, ensure_ascii=False)
