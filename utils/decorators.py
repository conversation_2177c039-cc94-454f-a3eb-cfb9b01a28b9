from functools import wraps
from flask import abort, redirect, url_for, flash
from flask_login import current_user

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not current_user.is_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def lux_required(f):
    """Decorator to require Lux subscription"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not current_user.is_lux_active():
            flash('This feature requires an active Lux subscription.', 'error')
            return redirect(url_for('main.pricing'))
        return f(*args, **kwargs)
    return decorated_function

def moderator_required(f):
    """Decorator to require moderator privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not (current_user.is_admin or current_user.is_moderator):
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def api_key_required(f):
    """Decorator to require valid API key"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import request, jsonify
        from models.user import User
        
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        
        if not api_key:
            return jsonify({'error': 'API key required'}), 401
        
        user = User.query.filter_by(api_token=api_key).first()
        if not user:
            return jsonify({'error': 'Invalid API key'}), 401
        
        if user.is_banned:
            return jsonify({'error': 'Account banned'}), 403
        
        # Add user to kwargs for use in the route
        kwargs['api_user'] = user
        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_requests=100, per_seconds=3600):
    """Simple rate limiting decorator"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask import request, jsonify
            import time
            
            # Simple in-memory rate limiting (in production, use Redis)
            if not hasattr(decorated_function, '_rate_limit_data'):
                decorated_function._rate_limit_data = {}
            
            client_ip = request.remote_addr
            current_time = time.time()
            
            # Clean old entries
            decorated_function._rate_limit_data = {
                ip: requests for ip, requests in decorated_function._rate_limit_data.items()
                if any(timestamp > current_time - per_seconds for timestamp in requests)
            }
            
            # Check rate limit
            if client_ip in decorated_function._rate_limit_data:
                recent_requests = [
                    timestamp for timestamp in decorated_function._rate_limit_data[client_ip]
                    if timestamp > current_time - per_seconds
                ]
                if len(recent_requests) >= max_requests:
                    return jsonify({'error': 'Rate limit exceeded'}), 429
                decorated_function._rate_limit_data[client_ip] = recent_requests + [current_time]
            else:
                decorated_function._rate_limit_data[client_ip] = [current_time]
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
