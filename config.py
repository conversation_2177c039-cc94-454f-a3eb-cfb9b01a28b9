import os
import secrets
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Basic Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'mysql+pymysql://lxnd_app:LxndApp2024!@localhost/lxnd_main'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # File Upload Configuration
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024 * 1024  # 16GB max file size
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    
    # Email Configuration
    MAIL_SERVER = 'mail.lxnd.cloud'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = '<EMAIL>'
    
    # Discord Configuration
    DISCORD_CLIENT_ID = os.environ.get('DISCORD_CLIENT_ID') or '1263893025749377024'
    DISCORD_CLIENT_SECRET = os.environ.get('DISCORD_CLIENT_SECRET') or 'YOUR_DISCORD_CLIENT_SECRET'
    DISCORD_REDIRECT_URI = 'https://lxnd.cloud/auth/discord/callback'
    DISCORD_BOT_TOKEN = os.environ.get('DISCORD_BOT_TOKEN')
    
    # SSL Configuration
    SSL_CERT_PATH = '/etc/letsencrypt/live'
    SSL_DOMAINS = ['lxnd.cloud', 'test.lxnd.cloud', 'luxend.lxnd.cloud']
    
    # Security Configuration
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # Application Settings
    LANGUAGES = ['en', 'de']
    DEFAULT_LANGUAGE = 'en'
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = 'memory://'
    
    # Backup Configuration
    BACKUP_FOLDER = '/root/lxnd/backups'
    
    # Website Hosting
    WEBSITES_FOLDER = os.path.join(os.getcwd(), 'websites')
    
    @staticmethod
    def init_app(app):
        """Initialize application with this config"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.BACKUP_FOLDER, exist_ok=True)
        os.makedirs(Config.WEBSITES_FOLDER, exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
