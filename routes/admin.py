from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import os

from database import db
from models.user import User
from models.project import Project, License, LuxLicense
from models.file_upload import FileUpload
from models.email import EmailAccount, EmailMessage, EmailTemplate
from models.website import Website
from models.url_shortener import ShortenedURL
from models.discord_settings import DiscordSettings
from utils.decorators import admin_required
from utils.forms import DiscordSettingsForm
from utils.language import get_messages

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    messages = get_messages()
    
    # System statistics
    stats = {
        'total_users': User.query.count(),
        'total_projects': Project.query.count(),
        'total_licenses': License.query.count(),
        'total_files': FileUpload.query.count(),
        'total_emails': EmailAccount.query.count(),
        'total_websites': Website.query.count(),
        'total_short_urls': ShortenedURL.query.count(),
        'lux_users': User.query.filter_by(is_lux=True).count(),
        'banned_users': User.query.filter_by(is_banned=True).count()
    }
    
    # Recent activity
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    recent_projects = Project.query.order_by(Project.created_at.desc()).limit(5).all()
    recent_files = FileUpload.query.order_by(FileUpload.upload_date.desc()).limit(5).all()
    
    # Storage statistics
    total_storage = sum(file.file_size for file in FileUpload.query.all())
    storage_stats = {
        'total_bytes': total_storage,
        'total_formatted': format_bytes(total_storage),
        'average_per_user': format_bytes(total_storage / max(stats['total_users'], 1))
    }
    
    return render_template('admin/dashboard.html',
                         stats=stats,
                         recent_users=recent_users,
                         recent_projects=recent_projects,
                         recent_files=recent_files,
                         storage_stats=storage_stats,
                         messages=messages)

@admin_bp.route('/users')
@admin_required
def admin_users():
    """User management"""
    messages = get_messages()
    
    # Get users with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    users = User.query.order_by(User.created_at.desc())\
                     .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('admin/users.html', users=users, messages=messages)

@admin_bp.route('/users/<int:user_id>')
@admin_required
def admin_user_detail(user_id):
    """User detail page"""
    user = User.query.get(user_id)
    
    if not user:
        abort(404)
    
    messages = get_messages()
    
    # User statistics
    user_stats = {
        'projects_count': Project.query.filter_by(user_id=user.id).count(),
        'files_count': FileUpload.query.filter_by(user_id=user.id).count(),
        'email_accounts_count': EmailAccount.query.filter_by(user_id=user.id).count(),
        'websites_count': Website.query.filter_by(user_id=user.id).count(),
        'storage_used': user.get_storage_used_formatted(),
        'storage_percent': user.get_storage_percent()
    }
    
    # Recent activity
    recent_files = FileUpload.query.filter_by(user_id=user.id)\
                                  .order_by(FileUpload.upload_date.desc()).limit(5).all()
    recent_projects = Project.query.filter_by(user_id=user.id)\
                                  .order_by(Project.created_at.desc()).limit(5).all()
    
    return render_template('admin/user_detail.html',
                         user=user,
                         user_stats=user_stats,
                         recent_files=recent_files,
                         recent_projects=recent_projects,
                         messages=messages)

@admin_bp.route('/users/<int:user_id>/toggle_ban', methods=['POST'])
@admin_required
def toggle_user_ban(user_id):
    """Toggle user ban status"""
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if user.id == current_user.id:
        return jsonify({'error': 'Cannot ban yourself'}), 400
    
    user.is_banned = not user.is_banned
    db.session.commit()
    
    status = 'banned' if user.is_banned else 'unbanned'
    return jsonify({
        'success': True,
        'status': status,
        'is_banned': user.is_banned
    })

@admin_bp.route('/users/<int:user_id>/toggle_lux', methods=['POST'])
@admin_required
def toggle_user_lux(user_id):
    """Toggle user Lux status"""
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    user.is_lux = not user.is_lux
    
    if user.is_lux:
        # Set permanent Lux if enabling
        user.lux_expires_at = None
    else:
        # Clear expiration if disabling
        user.lux_expires_at = None
    
    db.session.commit()
    
    status = 'enabled' if user.is_lux else 'disabled'
    return jsonify({
        'success': True,
        'status': status,
        'is_lux': user.is_lux
    })

@admin_bp.route('/users/<int:user_id>/update_limits', methods=['POST'])
@admin_required
def update_user_limits(user_id):
    """Update user limits"""
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    # Get form data
    upload_limit_mb = request.form.get('upload_limit_mb', type=int)
    max_projects = request.form.get('max_projects', type=int)
    max_keys_per_project = request.form.get('max_keys_per_project', type=int)
    
    if upload_limit_mb is not None:
        user.upload_limit_mb = upload_limit_mb
    if max_projects is not None:
        user.max_projects = max_projects
    if max_keys_per_project is not None:
        user.max_keys_per_project = max_keys_per_project
    
    db.session.commit()
    
    flash(f'Limits updated for user {user.username}', 'success')
    return redirect(url_for('admin.admin_user_detail', user_id=user_id))

@admin_bp.route('/email')
@admin_required
def admin_email():
    """Email management"""
    messages = get_messages()
    
    # Get all email accounts
    email_accounts = EmailAccount.query.order_by(EmailAccount.created_at.desc()).all()
    
    # Email statistics
    email_stats = {
        'total_accounts': len(email_accounts),
        'active_accounts': len([acc for acc in email_accounts if acc.is_active]),
        'total_messages': EmailMessage.query.count(),
        'total_storage_mb': sum(acc.storage_used_mb for acc in email_accounts)
    }
    
    return render_template('admin/email.html',
                         email_accounts=email_accounts,
                         email_stats=email_stats,
                         messages=messages)

@admin_bp.route('/email/<int:account_id>')
@admin_required
def admin_view_email_account(account_id):
    """View email account details"""
    account = EmailAccount.query.get(account_id)
    
    if not account:
        abort(404)
    
    messages = get_messages()
    
    # Account statistics
    account_stats = {
        'total_messages': len(account.messages),
        'inbox_messages': len([m for m in account.messages if m.folder == 'INBOX']),
        'sent_messages': len([m for m in account.messages if m.folder == 'SENT']),
        'storage_used_percent': (account.storage_used_mb / account.storage_limit_mb * 100) if account.storage_limit_mb > 0 else 0
    }
    
    # Recent messages
    recent_messages = EmailMessage.query.filter_by(account_id=account.id)\
                                       .order_by(EmailMessage.received_at.desc()).limit(10).all()
    
    return render_template('admin/view_email_account.html',
                         account=account,
                         account_stats=account_stats,
                         recent_messages=recent_messages,
                         messages=messages)

@admin_bp.route('/projects')
@admin_required
def admin_projects():
    """Project management"""
    messages = get_messages()
    
    # Get all projects
    projects = Project.query.order_by(Project.created_at.desc()).all()
    
    # Project statistics
    project_stats = {
        'total_projects': len(projects),
        'active_projects': len([p for p in projects if p.is_active]),
        'total_licenses': License.query.count(),
        'active_licenses': License.query.filter_by(is_active=True).count(),
        'used_licenses': License.query.filter(License.used_at.isnot(None)).count()
    }
    
    return render_template('admin/projects.html',
                         projects=projects,
                         project_stats=project_stats,
                         messages=messages)

@admin_bp.route('/lux')
@admin_required
def admin_lux():
    """Lux license management"""
    messages = get_messages()
    
    # Get all Lux licenses
    lux_licenses = LuxLicense.query.order_by(LuxLicense.created_at.desc()).all()
    
    # Lux statistics
    lux_stats = {
        'total_licenses': len(lux_licenses),
        'active_licenses': len([l for l in lux_licenses if l.is_active]),
        'used_licenses': len([l for l in lux_licenses if l.is_used()]),
        'lux_users': User.query.filter_by(is_lux=True).count()
    }
    
    return render_template('admin/lux.html',
                         lux_licenses=lux_licenses,
                         lux_stats=lux_stats,
                         messages=messages)

@admin_bp.route('/discord', methods=['GET', 'POST'])
@admin_required
def admin_discord():
    """Discord bot settings"""
    settings = DiscordSettings.get_settings()
    form = DiscordSettingsForm(obj=settings)
    messages = get_messages()
    
    if form.validate_on_submit():
        # Update settings
        settings.bot_token = form.bot_token.data
        settings.guild_id = form.guild_id.data
        settings.log_channel_id = form.log_channel_id.data
        settings.welcome_channel_id = form.welcome_channel_id.data
        settings.verification_channel_id = form.verification_channel_id.data
        settings.verified_role_id = form.verified_role_id.data
        settings.lux_role_id = form.lux_role_id.data
        settings.admin_role_id = form.admin_role_id.data
        settings.verification_enabled = form.verification_enabled.data
        settings.ticket_system_enabled = form.ticket_system_enabled.data
        settings.auto_role_enabled = form.auto_role_enabled.data
        settings.welcome_message = form.welcome_message.data
        settings.verification_message = form.verification_message.data
        settings.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash('Discord settings updated successfully!', 'success')
        return redirect(url_for('admin.admin_discord'))
    
    return render_template('admin/discord.html',
                         form=form,
                         settings=settings,
                         messages=messages)

@admin_bp.route('/system')
@admin_required
def admin_system():
    """System information"""
    messages = get_messages()
    
    # System information
    system_info = {
        'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
        'flask_version': '2.3.3',  # You can get this dynamically
        'database_size': get_database_size(),
        'upload_folder_size': get_folder_size('uploads'),
        'total_disk_usage': get_folder_size('.'),
        'uptime': get_system_uptime()
    }
    
    return render_template('admin/system.html',
                         system_info=system_info,
                         messages=messages)

@admin_bp.route('/logs')
@admin_required
def admin_logs():
    """View system logs"""
    messages = get_messages()
    
    # Read recent log entries
    log_file = 'app.log'
    logs = []
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                # Get last 100 lines
                logs = lines[-100:] if len(lines) > 100 else lines
                logs.reverse()  # Show newest first
        except Exception as e:
            logs = [f"Error reading log file: {e}"]
    
    return render_template('admin/logs.html',
                         logs=logs,
                         messages=messages)

# Helper functions
def format_bytes(bytes_value):
    """Format bytes to human readable format"""
    if bytes_value < 1024:
        return f"{bytes_value} B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value / 1024:.1f} KB"
    elif bytes_value < 1024 * 1024 * 1024:
        return f"{bytes_value / (1024 * 1024):.1f} MB"
    else:
        return f"{bytes_value / (1024 * 1024 * 1024):.2f} GB"

def get_database_size():
    """Get database file size"""
    db_file = 'lxnd.db'
    if os.path.exists(db_file):
        return format_bytes(os.path.getsize(db_file))
    return "0 B"

def get_folder_size(folder_path):
    """Get total size of folder"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception:
        pass
    return format_bytes(total_size)

def get_system_uptime():
    """Get system uptime (simplified)"""
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            uptime_days = int(uptime_seconds // 86400)
            uptime_hours = int((uptime_seconds % 86400) // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)
            return f"{uptime_days}d {uptime_hours}h {uptime_minutes}m"
    except Exception:
        return "Unknown"
