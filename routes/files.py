from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import secrets
import string
from datetime import datetime

from database import db
from models.file_upload import FileUpload
from utils.forms import FileUploadForm
from utils.decorators import api_key_required
from utils.language import get_messages

files_bp = Blueprint('files', __name__)

@files_bp.route('/dashboard')
@login_required
def files_dashboard():
    """File upload dashboard"""
    form = FileUploadForm()
    messages = get_messages()
    
    # Get user's files
    files = FileUpload.query.filter_by(user_id=current_user.id)\
                           .order_by(FileUpload.upload_date.desc()).all()
    
    return render_template('files_dashboard.html', files=files, form=form, messages=messages)

@files_bp.route('/upload', methods=['POST'])
@login_required
def upload_file():
    """Handle file upload"""
    form = FileUploadForm()
    
    if form.validate_on_submit():
        file = form.file.data
        is_public = form.public.data
        
        if file and file.filename:
            # Check file size
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset to beginning
            
            if not current_user.can_upload(file_size):
                flash('File too large. You have exceeded your storage limit.', 'error')
                return redirect(url_for('files.files_dashboard'))
            
            # Generate secure filename
            original_filename = secure_filename(file.filename)
            file_extension = os.path.splitext(original_filename)[1]
            
            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            random_string = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
            filename = f"{timestamp}_{random_string}{file_extension}"
            
            # Save file
            upload_folder = os.path.join(os.getcwd(), 'uploads')
            os.makedirs(upload_folder, exist_ok=True)
            file_path = os.path.join(upload_folder, filename)
            file.save(file_path)
            
            # Create database record
            file_upload = FileUpload(
                user_id=current_user.id,
                filename=filename,
                original_filename=original_filename,
                file_size=file_size,
                mime_type=file.content_type,
                public=is_public
            )
            
            db.session.add(file_upload)
            db.session.commit()
            
            flash(f'File "{original_filename}" uploaded successfully!', 'success')
        else:
            flash('No file selected', 'error')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return redirect(url_for('files.files_dashboard'))

@files_bp.route('/download/<file_id>')
def download_file(file_id):
    """Download file by ID"""
    file_upload = FileUpload.query.filter_by(file_id=file_id).first()
    
    if not file_upload:
        abort(404)
    
    # Check permissions
    if not file_upload.public and (not current_user.is_authenticated or file_upload.user_id != current_user.id):
        abort(403)
    
    # Increment download count
    file_upload.download_count += 1
    db.session.commit()
    
    # Send file
    file_path = os.path.join(os.getcwd(), 'uploads', file_upload.filename)
    
    if not os.path.exists(file_path):
        abort(404)
    
    return send_file(
        file_path,
        as_attachment=True,
        download_name=file_upload.original_filename,
        mimetype=file_upload.mime_type
    )

@files_bp.route('/delete/<file_id>', methods=['POST'])
@login_required
def delete_file(file_id):
    """Delete file"""
    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=current_user.id).first()
    
    if not file_upload:
        abort(404)
    
    # Delete file from disk
    file_path = os.path.join(os.getcwd(), 'uploads', file_upload.filename)
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # Delete from database
    db.session.delete(file_upload)
    db.session.commit()
    
    flash(f'File "{file_upload.original_filename}" deleted successfully!', 'success')
    return redirect(url_for('files.files_dashboard'))

@files_bp.route('/toggle_public/<file_id>', methods=['POST'])
@login_required
def toggle_public(file_id):
    """Toggle file public/private status"""
    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=current_user.id).first()
    
    if not file_upload:
        return jsonify({'error': 'File not found'}), 404
    
    file_upload.public = not file_upload.public
    db.session.commit()
    
    status = 'public' if file_upload.public else 'private'
    return jsonify({
        'success': True,
        'status': status,
        'message': f'File is now {status}'
    })

@files_bp.route('/info/<file_id>')
def file_info(file_id):
    """Get file information"""
    file_upload = FileUpload.query.filter_by(file_id=file_id).first()
    
    if not file_upload:
        abort(404)
    
    # Check permissions for private files
    if not file_upload.public and (not current_user.is_authenticated or file_upload.user_id != current_user.id):
        abort(403)
    
    return jsonify({
        'file_id': file_upload.file_id,
        'filename': file_upload.original_filename,
        'size': file_upload.file_size,
        'size_formatted': file_upload.get_size_formatted(),
        'mime_type': file_upload.mime_type,
        'upload_date': file_upload.upload_date.isoformat(),
        'download_count': file_upload.download_count,
        'public': file_upload.public,
        'download_url': url_for('files.download_file', file_id=file_upload.file_id, _external=True)
    })

# API Routes
@files_bp.route('/api/upload', methods=['POST'])
@api_key_required
def api_upload_file(api_user):
    """API endpoint for file upload"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    is_public = request.form.get('public', 'false').lower() == 'true'
    
    # Check file size
    file.seek(0, 2)
    file_size = file.tell()
    file.seek(0)
    
    if not api_user.can_upload(file_size):
        return jsonify({'error': 'File too large or storage limit exceeded'}), 413
    
    # Process upload
    original_filename = secure_filename(file.filename)
    file_extension = os.path.splitext(original_filename)[1]
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    random_string = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
    filename = f"{timestamp}_{random_string}{file_extension}"
    
    # Save file
    upload_folder = os.path.join(os.getcwd(), 'uploads')
    os.makedirs(upload_folder, exist_ok=True)
    file_path = os.path.join(upload_folder, filename)
    file.save(file_path)
    
    # Create database record
    file_upload = FileUpload(
        user_id=api_user.id,
        filename=filename,
        original_filename=original_filename,
        file_size=file_size,
        mime_type=file.content_type,
        public=is_public
    )
    
    db.session.add(file_upload)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'file_id': file_upload.file_id,
        'filename': original_filename,
        'size': file_size,
        'download_url': url_for('files.download_file', file_id=file_upload.file_id, _external=True),
        'public': is_public
    })

@files_bp.route('/api/list')
@api_key_required
def api_list_files(api_user):
    """API endpoint to list user's files"""
    files = FileUpload.query.filter_by(user_id=api_user.id)\
                           .order_by(FileUpload.upload_date.desc()).all()
    
    files_data = []
    for file in files:
        files_data.append({
            'file_id': file.file_id,
            'filename': file.original_filename,
            'size': file.file_size,
            'size_formatted': file.get_size_formatted(),
            'mime_type': file.mime_type,
            'upload_date': file.upload_date.isoformat(),
            'download_count': file.download_count,
            'public': file.public,
            'download_url': url_for('files.download_file', file_id=file.file_id, _external=True)
        })
    
    return jsonify({
        'success': True,
        'files': files_data,
        'total_files': len(files_data),
        'storage_used': api_user.get_storage_used_formatted(),
        'storage_limit_mb': api_user.upload_limit_mb
    })

@files_bp.route('/api/delete/<file_id>', methods=['DELETE'])
@api_key_required
def api_delete_file(api_user, file_id):
    """API endpoint to delete file"""
    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=api_user.id).first()
    
    if not file_upload:
        return jsonify({'error': 'File not found'}), 404
    
    # Delete file from disk
    file_path = os.path.join(os.getcwd(), 'uploads', file_upload.filename)
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # Delete from database
    db.session.delete(file_upload)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'File "{file_upload.original_filename}" deleted successfully'
    })
