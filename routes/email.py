from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
import json
import base64
from datetime import datetime

from database import db
from models.email import EmailAccount, EmailMessage, EmailTemplate
from utils.forms import Email<PERSON>ccountForm, EmailTemplateForm
from utils.decorators import admin_required
from utils.language import get_messages

email_bp = Blueprint('email', __name__)

@email_bp.route('/dashboard')
@login_required
def user_email_dashboard():
    """User email dashboard"""
    messages = get_messages()
    
    # Get user's email accounts
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()
    
    # Calculate statistics
    total_messages = 0
    total_storage = 0
    
    for account in email_accounts:
        total_messages += len(account.messages)
        total_storage += account.storage_used_mb
    
    stats = {
        'total_accounts': len(email_accounts),
        'total_messages': total_messages,
        'total_storage_mb': total_storage,
        'total_storage_formatted': f"{total_storage / 1024:.1f} GB" if total_storage > 1024 else f"{total_storage} MB"
    }
    
    return render_template('email/dashboard.html', 
                         email_accounts=email_accounts,
                         stats=stats,
                         messages=messages)

@email_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_email_account():
    """Create new email account"""
    form = EmailAccountForm()
    messages = get_messages()
    
    if form.validate_on_submit():
        # Check if email already exists
        existing_account = EmailAccount.query.filter_by(email_address=form.email_address.data).first()
        if existing_account:
            flash('Email address already exists', 'error')
            return render_template('email/create.html', form=form, messages=messages)
        
        # Create email account
        email_account = EmailAccount(
            user_id=current_user.id,
            email_address=form.email_address.data,
            display_name=form.display_name.data,
            storage_limit_mb=form.storage_limit_mb.data,
            is_active=form.is_active.data
        )
        email_account.set_password(form.password.data)
        
        db.session.add(email_account)
        db.session.commit()
        
        flash(f'Email account "{email_account.email_address}" created successfully!', 'success')
        return redirect(url_for('email.user_email_dashboard'))
    
    return render_template('email/create.html', form=form, messages=messages)

@email_bp.route('/account/<int:account_id>')
@login_required
def email_account_detail(account_id):
    """Email account detail page"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
    
    if not account:
        abort(404)
    
    messages = get_messages()
    
    # Get folders and message counts
    inbox_count = EmailMessage.query.filter_by(account_id=account.id, folder='INBOX').count()
    sent_count = EmailMessage.query.filter_by(account_id=account.id, folder='SENT').count()
    draft_count = EmailMessage.query.filter_by(account_id=account.id, folder='DRAFT').count()
    trash_count = EmailMessage.query.filter_by(account_id=account.id, folder='TRASH').count()
    
    folder_stats = {
        'inbox': inbox_count,
        'sent': sent_count,
        'drafts': draft_count,
        'trash': trash_count
    }
    
    return render_template('email/account_detail.html', 
                         account=account,
                         folder_stats=folder_stats,
                         messages=messages)

@email_bp.route('/account/<int:account_id>/inbox')
@login_required
def inbox(account_id):
    """Email inbox"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
    
    if not account:
        abort(404)
    
    messages = get_messages()
    
    # Get inbox messages
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    email_messages = EmailMessage.query.filter_by(account_id=account.id, folder='INBOX')\
                                      .order_by(EmailMessage.received_at.desc())\
                                      .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('email/inbox.html', 
                         account=account,
                         email_messages=email_messages,
                         messages=messages)

@email_bp.route('/account/<int:account_id>/sent')
@login_required
def sent(account_id):
    """Sent emails"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
    
    if not account:
        abort(404)
    
    messages = get_messages()
    
    # Get sent messages
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    email_messages = EmailMessage.query.filter_by(account_id=account.id, folder='SENT')\
                                      .order_by(EmailMessage.sent_at.desc())\
                                      .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('email/sent.html', 
                         account=account,
                         email_messages=email_messages,
                         messages=messages)

@email_bp.route('/message/<int:message_id>')
@login_required
def view_message(message_id):
    """View email message"""
    message = EmailMessage.query.get(message_id)
    
    if not message:
        abort(404)
    
    # Check if user owns the email account
    if message.account.user_id != current_user.id:
        abort(403)
    
    messages = get_messages()
    
    # Mark as read
    if not message.is_read:
        message.is_read = True
        db.session.commit()
    
    return render_template('email/view_message.html', 
                         message=message,
                         account=message.account,
                         messages=messages)

@email_bp.route('/compose/<int:account_id>')
@login_required
def compose(account_id):
    """Compose new email"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
    
    if not account:
        abort(404)
    
    messages = get_messages()
    
    return render_template('email/compose.html', 
                         account=account,
                         messages=messages)

@email_bp.route('/send/<int:account_id>', methods=['POST'])
@login_required
def send_email(account_id):
    """Send email"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
    
    if not account:
        abort(404)
    
    # Get form data
    to_addresses = request.form.get('to', '').split(',')
    cc_addresses = request.form.get('cc', '').split(',') if request.form.get('cc') else []
    bcc_addresses = request.form.get('bcc', '').split(',') if request.form.get('bcc') else []
    subject = request.form.get('subject', '')
    body_html = request.form.get('body_html', '')
    body_text = request.form.get('body_text', '')
    
    # Clean up addresses
    to_addresses = [addr.strip() for addr in to_addresses if addr.strip()]
    cc_addresses = [addr.strip() for addr in cc_addresses if addr.strip()]
    bcc_addresses = [addr.strip() for addr in bcc_addresses if addr.strip()]
    
    if not to_addresses:
        flash('At least one recipient is required', 'error')
        return redirect(url_for('email.compose', account_id=account_id))
    
    # Create email message record
    email_message = EmailMessage(
        account_id=account.id,
        subject=subject,
        sender=account.email_address,
        body_html=body_html,
        body_text=body_text,
        folder='SENT',
        sent_at=datetime.utcnow()
    )
    
    # Set recipients
    email_message.set_recipients_list(to_addresses)
    if cc_addresses:
        email_message.cc = json.dumps(cc_addresses)
    if bcc_addresses:
        email_message.bcc = json.dumps(bcc_addresses)
    
    db.session.add(email_message)
    db.session.commit()
    
    # TODO: Implement actual email sending via SMTP
    # For now, just save to database
    
    flash('Email sent successfully!', 'success')
    return redirect(url_for('email.sent', account_id=account_id))

@email_bp.route('/message/<int:message_id>/delete', methods=['POST'])
@login_required
def delete_message(message_id):
    """Delete email message"""
    message = EmailMessage.query.get(message_id)
    
    if not message:
        abort(404)
    
    # Check if user owns the email account
    if message.account.user_id != current_user.id:
        abort(403)
    
    account_id = message.account_id
    
    if message.folder == 'TRASH':
        # Permanently delete
        db.session.delete(message)
        flash('Email permanently deleted', 'success')
    else:
        # Move to trash
        message.folder = 'TRASH'
        flash('Email moved to trash', 'success')
    
    db.session.commit()
    
    return redirect(url_for('email.inbox', account_id=account_id))

@email_bp.route('/message/<int:message_id>/star', methods=['POST'])
@login_required
def toggle_star(message_id):
    """Toggle message star status"""
    message = EmailMessage.query.get(message_id)
    
    if not message:
        return jsonify({'error': 'Message not found'}), 404
    
    # Check if user owns the email account
    if message.account.user_id != current_user.id:
        return jsonify({'error': 'Access denied'}), 403
    
    message.is_starred = not message.is_starred
    db.session.commit()
    
    return jsonify({
        'success': True,
        'is_starred': message.is_starred
    })

# Email Templates
@email_bp.route('/templates')
@admin_required
def email_templates():
    """Email templates management"""
    messages = get_messages()
    
    templates = EmailTemplate.query.order_by(EmailTemplate.created_at.desc()).all()
    
    return render_template('admin/email_templates.html', 
                         templates=templates,
                         messages=messages)

@email_bp.route('/templates/create', methods=['GET', 'POST'])
@admin_required
def create_email_template():
    """Create email template"""
    form = EmailTemplateForm()
    messages = get_messages()
    
    # Set template type from query parameter
    template_type = request.args.get('type', 'custom')
    if template_type in ['welcome', 'notification', 'custom']:
        form.template_type.data = template_type
    
    if form.validate_on_submit():
        template = EmailTemplate(
            name=form.name.data,
            description=form.description.data,
            template_type=form.template_type.data,
            subject=form.subject.data,
            body_html=form.body_html.data,
            body_text=form.body_text.data,
            is_active=form.is_active.data
        )
        
        db.session.add(template)
        db.session.commit()
        
        flash(f'Email template "{template.name}" created successfully!', 'success')
        return redirect(url_for('email.email_templates'))
    
    return render_template('admin/create_email_template.html', 
                         form=form, 
                         template=None,
                         messages=messages)

@email_bp.route('/templates/<int:template_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_email_template(template_id):
    """Edit email template"""
    template = EmailTemplate.query.get(template_id)
    
    if not template:
        abort(404)
    
    form = EmailTemplateForm(obj=template)
    form._template_id = template_id  # For validation
    messages = get_messages()
    
    if form.validate_on_submit():
        template.name = form.name.data
        template.description = form.description.data
        template.template_type = form.template_type.data
        template.subject = form.subject.data
        template.body_html = form.body_html.data
        template.body_text = form.body_text.data
        template.is_active = form.is_active.data
        template.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash(f'Email template "{template.name}" updated successfully!', 'success')
        return redirect(url_for('email.email_templates'))
    
    return render_template('admin/edit_email_template.html', 
                         form=form, 
                         template=template,
                         messages=messages)

@email_bp.route('/templates/<int:template_id>/preview', methods=['GET', 'POST'])
@admin_required
def preview_email_template(template_id):
    """Preview email template"""
    template = EmailTemplate.query.get(template_id)
    
    if not template:
        abort(404)
    
    messages = get_messages()
    
    # Sample variables for preview
    sample_vars = {
        'user_name': 'John Doe',
        'user_email': '<EMAIL>',
        'site_name': 'LXND',
        'admin_email': '<EMAIL>',
        'current_date': datetime.now().strftime('%B %d, %Y')
    }
    
    # Handle POST request from editor preview
    if request.method == 'POST':
        # Use form data for preview
        rendered = {
            'subject': request.form.get('subject', ''),
            'body_html': request.form.get('body_html', ''),
            'body_text': request.form.get('body_text', '')
        }
        
        # Replace variables
        for key, value in sample_vars.items():
            placeholder = '{' + key + '}'
            rendered['subject'] = rendered['subject'].replace(placeholder, str(value))
            rendered['body_html'] = rendered['body_html'].replace(placeholder, str(value))
            rendered['body_text'] = rendered['body_text'].replace(placeholder, str(value))
    else:
        # Use template data
        rendered = template.render_template(sample_vars)
    
    return render_template('admin/preview_email_template.html', 
                         template=template,
                         rendered=rendered,
                         sample_vars=sample_vars,
                         messages=messages)

@email_bp.route('/templates/<int:template_id>/delete', methods=['POST'])
@admin_required
def delete_email_template(template_id):
    """Delete email template"""
    template = EmailTemplate.query.get(template_id)
    
    if not template:
        return jsonify({'error': 'Template not found'}), 404
    
    template_name = template.name
    db.session.delete(template)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'Template "{template_name}" deleted successfully'
    })
