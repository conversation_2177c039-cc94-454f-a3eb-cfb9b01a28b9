from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
import secrets
import string
from datetime import datetime

from database import db
from models.project import Project, License, LuxLicense
from models.user import User
from utils.forms import ProjectForm
from utils.decorators import api_key_required, lux_required
from utils.language import get_messages

projects_bp = Blueprint('projects', __name__)

@projects_bp.route('/')
@login_required
def dashboard():
    """Projects dashboard"""
    messages = get_messages()
    
    # Get user's projects
    projects = Project.query.filter_by(user_id=current_user.id)\
                           .order_by(Project.created_at.desc()).all()
    
    # Calculate statistics
    total_licenses = sum(len(project.licenses) for project in projects)
    active_licenses = sum(len([l for l in project.licenses if l.is_active]) for project in projects)
    
    stats = {
        'total_projects': len(projects),
        'total_licenses': total_licenses,
        'active_licenses': active_licenses,
        'can_create_project': current_user.can_create_project()
    }
    
    return render_template('projects/dashboard.html', 
                         projects=projects, 
                         stats=stats,
                         messages=messages)

@projects_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_project():
    """Create new project"""
    if not current_user.can_create_project():
        flash('You have reached the maximum number of projects allowed.', 'error')
        return redirect(url_for('projects.dashboard'))
    
    form = ProjectForm()
    messages = get_messages()
    
    if form.validate_on_submit():
        project = Project(
            name=form.name.data,
            description=form.description.data,
            user_id=current_user.id
        )
        
        db.session.add(project)
        db.session.commit()
        
        flash(f'Project "{project.name}" created successfully!', 'success')
        return redirect(url_for('projects.project_detail', project_id=project.id))
    
    return render_template('projects/create.html', form=form, messages=messages)

@projects_bp.route('/<int:project_id>')
@login_required
def project_detail(project_id):
    """Project detail page"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    messages = get_messages()
    
    # Get project statistics
    total_licenses = len(project.licenses)
    active_licenses = len([l for l in project.licenses if l.is_active])
    used_licenses = len([l for l in project.licenses if l.used_at])
    
    stats = {
        'total_licenses': total_licenses,
        'active_licenses': active_licenses,
        'used_licenses': used_licenses,
        'unused_licenses': active_licenses - used_licenses,
        'can_create_license': current_user.can_create_license(project)
    }
    
    return render_template('projects/detail.html', 
                         project=project, 
                         stats=stats,
                         messages=messages)

@projects_bp.route('/<int:project_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_project(project_id):
    """Edit project"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    form = ProjectForm(obj=project)
    messages = get_messages()
    
    if form.validate_on_submit():
        project.name = form.name.data
        project.description = form.description.data
        
        db.session.commit()
        
        flash(f'Project "{project.name}" updated successfully!', 'success')
        return redirect(url_for('projects.project_detail', project_id=project.id))
    
    return render_template('projects/edit.html', form=form, project=project, messages=messages)

@projects_bp.route('/<int:project_id>/delete', methods=['POST'])
@login_required
def delete_project(project_id):
    """Delete project"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    project_name = project.name
    db.session.delete(project)
    db.session.commit()
    
    flash(f'Project "{project_name}" deleted successfully!', 'success')
    return redirect(url_for('projects.dashboard'))

@projects_bp.route('/<int:project_id>/licenses/create', methods=['POST'])
@login_required
def create_license(project_id):
    """Create new license for project"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    if not current_user.can_create_license(project):
        return jsonify({'error': 'Maximum licenses reached for this project'}), 400
    
    # Generate license key
    license_key = generate_license_key()
    
    license = License(
        license_key=license_key,
        project_id=project.id
    )
    
    db.session.add(license)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'license_key': license_key,
        'license_id': license.id,
        'created_at': license.created_at.isoformat()
    })

@projects_bp.route('/<int:project_id>/licenses/<int:license_id>/toggle', methods=['POST'])
@login_required
def toggle_license(project_id, license_id):
    """Toggle license active status"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    license = License.query.filter_by(id=license_id, project_id=project.id).first()
    
    if not license:
        abort(404)
    
    license.is_active = not license.is_active
    db.session.commit()
    
    status = 'activated' if license.is_active else 'deactivated'
    return jsonify({
        'success': True,
        'status': status,
        'is_active': license.is_active
    })

@projects_bp.route('/<int:project_id>/licenses/<int:license_id>/delete', methods=['POST'])
@login_required
def delete_license(project_id, license_id):
    """Delete license"""
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    
    if not project:
        abort(404)
    
    license = License.query.filter_by(id=license_id, project_id=project.id).first()
    
    if not license:
        abort(404)
    
    db.session.delete(license)
    db.session.commit()
    
    return jsonify({'success': True})

# Lux License Management
@projects_bp.route('/lux')
@lux_required
def lux_dashboard():
    """Lux license management dashboard"""
    messages = get_messages()
    
    # Get Lux licenses
    lux_licenses = LuxLicense.query.order_by(LuxLicense.created_at.desc()).all()
    
    # Statistics
    total_licenses = len(lux_licenses)
    active_licenses = len([l for l in lux_licenses if l.is_active])
    used_licenses = len([l for l in lux_licenses if l.is_used()])
    
    stats = {
        'total_licenses': total_licenses,
        'active_licenses': active_licenses,
        'used_licenses': used_licenses,
        'unused_licenses': active_licenses - used_licenses
    }
    
    return render_template('projects/lux_dashboard.html', 
                         lux_licenses=lux_licenses,
                         stats=stats,
                         messages=messages)

@projects_bp.route('/lux/create', methods=['POST'])
@lux_required
def create_lux_license():
    """Create new Lux license"""
    duration_days = request.form.get('duration_days', 30, type=int)
    notes = request.form.get('notes', '')
    
    lux_license = LuxLicense(
        duration_days=duration_days,
        notes=notes
    )
    
    db.session.add(lux_license)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'license_key': lux_license.license_key,
        'license_id': lux_license.id,
        'duration_days': lux_license.duration_days
    })

# API Routes
@projects_bp.route('/api/verify/<license_key>')
def api_verify_license(license_key):
    """API endpoint to verify license"""
    license = License.query.filter_by(license_key=license_key).first()
    
    if not license:
        return jsonify({'valid': False, 'error': 'License not found'}), 404
    
    if not license.is_active:
        return jsonify({'valid': False, 'error': 'License is inactive'}), 403
    
    # Update usage information
    if not license.used_at:
        license.used_at = datetime.utcnow()
        license.used_by_ip = request.remote_addr
        license.used_by_hwid = request.headers.get('X-Hardware-ID', '')
        db.session.commit()
    
    return jsonify({
        'valid': True,
        'license_key': license.license_key,
        'project_name': license.project.name,
        'created_at': license.created_at.isoformat(),
        'used_at': license.used_at.isoformat() if license.used_at else None
    })

@projects_bp.route('/api/lux/verify/<license_key>', methods=['POST'])
def api_verify_lux_license(license_key):
    """API endpoint to verify and activate Lux license"""
    lux_license = LuxLicense.query.filter_by(license_key=license_key).first()
    
    if not lux_license:
        return jsonify({'valid': False, 'error': 'License not found'}), 404
    
    if not lux_license.is_active:
        return jsonify({'valid': False, 'error': 'License is inactive'}), 403
    
    if lux_license.is_used():
        return jsonify({'valid': False, 'error': 'License already used'}), 409
    
    # Get user information from request
    user_id = request.json.get('user_id') if request.is_json else None
    
    if user_id:
        user = User.query.get(user_id)
        if user:
            # Activate Lux for user
            success, message = lux_license.use_license(user_id, request.remote_addr)
            
            if success:
                # Update user's Lux status
                user.is_lux = True
                if lux_license.duration_days > 0:
                    from datetime import timedelta
                    user.lux_expires_at = datetime.utcnow() + timedelta(days=lux_license.duration_days)
                else:
                    user.lux_expires_at = None  # Permanent
                
                db.session.commit()
                
                return jsonify({
                    'valid': True,
                    'activated': True,
                    'duration_days': lux_license.duration_days,
                    'expires_at': user.lux_expires_at.isoformat() if user.lux_expires_at else None
                })
            else:
                return jsonify({'valid': False, 'error': message}), 400
    
    return jsonify({'valid': False, 'error': 'User ID required'}), 400

def generate_license_key():
    """Generate a unique license key"""
    while True:
        # Generate a random key with format: XXXX-XXXX-XXXX-XXXX
        key_parts = []
        for _ in range(4):
            part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
            key_parts.append(part)

        license_key = '-'.join(key_parts)

        # Check if key already exists
        if not License.query.filter_by(license_key=license_key).first():
            return license_key
