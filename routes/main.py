from flask import Blueprint, render_template, redirect, url_for, request, jsonify
from flask_login import login_required, current_user

from database import db
from models.user import User
from models.project import Project
from models.file_upload import FileUpload
from models.email import EmailAccount
from models.website import Website
from utils.language import get_messages

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Landing page"""
    messages = get_messages()
    
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    return render_template('landing.html', messages=messages)

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """User dashboard"""
    messages = get_messages()
    
    # Get user statistics
    projects_count = Project.query.filter_by(user_id=current_user.id).count()
    files_count = FileUpload.query.filter_by(user_id=current_user.id).count()
    email_accounts_count = EmailAccount.query.filter_by(user_id=current_user.id).count()
    websites_count = Website.query.filter_by(user_id=current_user.id).count()
    
    # Get recent files
    recent_files = FileUpload.query.filter_by(user_id=current_user.id)\
                                  .order_by(FileUpload.upload_date.desc())\
                                  .limit(5).all()
    
    # Get recent projects
    recent_projects = Project.query.filter_by(user_id=current_user.id)\
                                  .order_by(Project.created_at.desc())\
                                  .limit(5).all()
    
    stats = {
        'projects': projects_count,
        'files': files_count,
        'email_accounts': email_accounts_count,
        'websites': websites_count,
        'storage_used': current_user.get_storage_used_formatted(),
        'storage_percent': current_user.get_storage_percent()
    }
    
    return render_template('dashboard.html', 
                         messages=messages,
                         stats=stats,
                         recent_files=recent_files,
                         recent_projects=recent_projects)

@main_bp.route('/api/user/stats')
@login_required
def user_stats():
    """Get user statistics as JSON"""
    stats = {
        'projects_count': Project.query.filter_by(user_id=current_user.id).count(),
        'files_count': FileUpload.query.filter_by(user_id=current_user.id).count(),
        'email_accounts_count': EmailAccount.query.filter_by(user_id=current_user.id).count(),
        'websites_count': Website.query.filter_by(user_id=current_user.id).count(),
        'storage_used_mb': current_user.get_used_storage_mb(),
        'storage_limit_mb': current_user.upload_limit_mb,
        'storage_percent': current_user.get_storage_percent(),
        'is_lux': current_user.is_lux_active(),
        'lux_status': current_user.get_lux_status()
    }
    
    return jsonify(stats)

@main_bp.route('/features')
def features():
    """Features page"""
    messages = get_messages()
    return render_template('features.html', messages=messages)

@main_bp.route('/pricing')
def pricing():
    """Pricing page"""
    messages = get_messages()
    return render_template('pricing.html', messages=messages)

@main_bp.route('/api-docs')
def api_docs():
    """API documentation page"""
    messages = get_messages()
    return render_template('api_docs.html', messages=messages)

@main_bp.route('/contact')
def contact():
    """Contact page"""
    messages = get_messages()
    return render_template('contact.html', messages=messages)

@main_bp.route('/about')
def about():
    """About page"""
    messages = get_messages()
    return render_template('about.html', messages=messages)

@main_bp.route('/impressum')
def impressum():
    """Impressum (Legal Notice)"""
    messages = get_messages()
    return render_template('legal/impressum.html', messages=messages)

@main_bp.route('/datenschutz')
def datenschutz():
    """Datenschutz (Privacy Policy)"""
    messages = get_messages()
    return render_template('legal/datenschutz.html', messages=messages)

@main_bp.route('/agb')
def agb():
    """AGB (Terms of Service)"""
    messages = get_messages()
    return render_template('legal/agb.html', messages=messages)

@main_bp.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'LXND',
        'version': '1.0.0'
    })

@main_bp.route('/api/search')
@login_required
def search():
    """Global search endpoint"""
    query = request.args.get('q', '').strip()
    
    if not query:
        return jsonify({'results': []})
    
    results = []
    
    # Search projects
    projects = Project.query.filter(
        Project.user_id == current_user.id,
        Project.name.contains(query)
    ).limit(5).all()
    
    for project in projects:
        results.append({
            'type': 'project',
            'title': project.name,
            'description': project.description or 'No description',
            'url': url_for('projects.project_detail', project_id=project.id)
        })
    
    # Search files
    files = FileUpload.query.filter(
        FileUpload.user_id == current_user.id,
        FileUpload.original_filename.contains(query)
    ).limit(5).all()
    
    for file in files:
        results.append({
            'type': 'file',
            'title': file.original_filename,
            'description': f'{file.get_size_formatted()} • {file.upload_date.strftime("%Y-%m-%d")}',
            'url': url_for('files.download_file', file_id=file.file_id)
        })
    
    # Search websites
    websites = Website.query.filter(
        Website.user_id == current_user.id,
        Website.title.contains(query)
    ).limit(5).all()
    
    for website in websites:
        results.append({
            'type': 'website',
            'title': website.title,
            'description': f'{website.subdomain}.lxnd.cloud',
            'url': url_for('websites.website_detail', website_id=website.id)
        })
    
    return jsonify({'results': results})
