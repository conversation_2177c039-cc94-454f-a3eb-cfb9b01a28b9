from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, send_from_directory
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import shutil
import secrets
import string
from datetime import datetime

from database import db
from models.website import Website, WebsiteFile
from utils.forms import WebsiteForm
from utils.decorators import lux_required
from utils.language import get_messages

websites_bp = Blueprint('websites', __name__)

@websites_bp.route('/dashboard')
@login_required
def websites_dashboard():
    """Websites dashboard"""
    messages = get_messages()
    
    # Get user's websites
    websites = Website.query.filter_by(user_id=current_user.id)\
                           .order_by(Website.created_at.desc()).all()
    
    # Statistics
    total_websites = len(websites)
    total_files = sum(website.get_file_count() for website in websites)
    total_storage = sum(website.get_total_size() for website in websites)
    
    stats = {
        'total_websites': total_websites,
        'total_files': total_files,
        'total_storage': total_storage,
        'total_storage_formatted': format_bytes(total_storage)
    }
    
    return render_template('websites/dashboard.html', 
                         websites=websites,
                         stats=stats,
                         messages=messages)

@websites_bp.route('/create', methods=['GET', 'POST'])
@lux_required
def create_website():
    """Create new website"""
    form = WebsiteForm()
    messages = get_messages()
    
    if form.validate_on_submit():
        # Check if subdomain is available
        existing = Website.query.filter_by(subdomain=form.subdomain.data).first()
        if existing:
            flash('Subdomain already exists. Please choose another.', 'error')
            return render_template('websites/create.html', form=form, messages=messages)
        
        # Create website
        website = Website(
            user_id=current_user.id,
            subdomain=form.subdomain.data,
            title=form.title.data,
            description=form.description.data,
            ssl_enabled=form.ssl_enabled.data
        )
        
        # Generate SFTP credentials
        website.sftp_username = f"web_{form.subdomain.data}"
        website.sftp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
        
        db.session.add(website)
        db.session.commit()
        
        # Create website directory
        website_dir = os.path.join(os.getcwd(), 'websites', website.subdomain)
        os.makedirs(website_dir, exist_ok=True)
        
        # Create default index.html
        default_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{website.title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
        .container {{ max-width: 600px; margin: 0 auto; }}
        h1 {{ color: #333; }}
        p {{ color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to {website.title}</h1>
        <p>Your website is now live at {website.get_domain()}</p>
        <p>Upload your files to get started!</p>
    </div>
</body>
</html>"""
        
        index_path = os.path.join(website_dir, 'index.html')
        with open(index_path, 'w') as f:
            f.write(default_html)
        
        # Create database record for index.html
        index_file = WebsiteFile(
            website_id=website.id,
            filename='index.html',
            file_path=index_path,
            file_size=len(default_html.encode()),
            mime_type='text/html',
            is_index=True
        )
        
        db.session.add(index_file)
        db.session.commit()
        
        flash(f'Website "{website.title}" created successfully!', 'success')
        return redirect(url_for('websites.website_detail', website_id=website.id))
    
    return render_template('websites/create.html', form=form, messages=messages)

@websites_bp.route('/<int:website_id>')
@login_required
def website_detail(website_id):
    """Website detail page"""
    website = Website.query.filter_by(id=website_id, user_id=current_user.id).first()
    
    if not website:
        abort(404)
    
    messages = get_messages()
    
    # Get website files
    files = WebsiteFile.query.filter_by(website_id=website.id)\
                            .order_by(WebsiteFile.uploaded_at.desc()).all()
    
    # Statistics
    stats = {
        'total_files': len(files),
        'total_size': website.get_total_size(),
        'total_size_formatted': website.get_total_size_formatted(),
        'domain': website.get_domain()
    }
    
    return render_template('websites/detail.html', 
                         website=website,
                         files=files,
                         stats=stats,
                         messages=messages)

@websites_bp.route('/<int:website_id>/upload', methods=['POST'])
@login_required
def upload_file(website_id):
    """Upload file to website"""
    website = Website.query.filter_by(id=website_id, user_id=current_user.id).first()
    
    if not website:
        abort(404)
    
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('websites.website_detail', website_id=website_id))
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('websites.website_detail', website_id=website_id))
    
    # Secure filename
    filename = secure_filename(file.filename)
    
    # Save file
    website_dir = os.path.join(os.getcwd(), 'websites', website.subdomain)
    os.makedirs(website_dir, exist_ok=True)
    file_path = os.path.join(website_dir, filename)
    
    file.save(file_path)
    file_size = os.path.getsize(file_path)
    
    # Create database record
    website_file = WebsiteFile(
        website_id=website.id,
        filename=filename,
        file_path=file_path,
        file_size=file_size,
        mime_type=file.content_type,
        is_index=(filename.lower() == 'index.html')
    )
    
    db.session.add(website_file)
    db.session.commit()
    
    flash(f'File "{filename}" uploaded successfully!', 'success')
    return redirect(url_for('websites.website_detail', website_id=website_id))

@websites_bp.route('/<int:website_id>/files/<int:file_id>/delete', methods=['POST'])
@login_required
def delete_file(website_id, file_id):
    """Delete website file"""
    website = Website.query.filter_by(id=website_id, user_id=current_user.id).first()
    
    if not website:
        abort(404)
    
    file = WebsiteFile.query.filter_by(id=file_id, website_id=website.id).first()
    
    if not file:
        abort(404)
    
    # Delete file from disk
    if os.path.exists(file.file_path):
        os.remove(file.file_path)
    
    # Delete from database
    db.session.delete(file)
    db.session.commit()
    
    flash(f'File "{file.filename}" deleted successfully!', 'success')
    return redirect(url_for('websites.website_detail', website_id=website_id))

@websites_bp.route('/<int:website_id>/delete', methods=['POST'])
@login_required
def delete_website(website_id):
    """Delete website"""
    website = Website.query.filter_by(id=website_id, user_id=current_user.id).first()
    
    if not website:
        abort(404)
    
    # Delete website directory
    website_dir = os.path.join(os.getcwd(), 'websites', website.subdomain)
    if os.path.exists(website_dir):
        shutil.rmtree(website_dir)
    
    # Delete from database (cascade will delete files)
    website_title = website.title
    db.session.delete(website)
    db.session.commit()
    
    flash(f'Website "{website_title}" deleted successfully!', 'success')
    return redirect(url_for('websites.websites_dashboard'))

@websites_bp.route('/<int:website_id>/settings', methods=['GET', 'POST'])
@login_required
def website_settings(website_id):
    """Website settings"""
    website = Website.query.filter_by(id=website_id, user_id=current_user.id).first()
    
    if not website:
        abort(404)
    
    messages = get_messages()
    
    if request.method == 'POST':
        # Update website settings
        website.title = request.form.get('title', website.title)
        website.description = request.form.get('description', website.description)
        website.ssl_enabled = 'ssl_enabled' in request.form
        
        db.session.commit()
        
        flash('Website settings updated successfully!', 'success')
        return redirect(url_for('websites.website_detail', website_id=website_id))
    
    return render_template('websites/settings.html', 
                         website=website,
                         messages=messages)

# Public website serving
@websites_bp.route('/serve/<subdomain>')
@websites_bp.route('/serve/<subdomain>/<path:filename>')
def serve_website(subdomain, filename='index.html'):
    """Serve website files"""
    website = Website.query.filter_by(subdomain=subdomain).first()
    
    if not website:
        abort(404)
    
    website_dir = os.path.join(os.getcwd(), 'websites', subdomain)
    file_path = os.path.join(website_dir, filename)
    
    if not os.path.exists(file_path):
        # Try to serve index.html for directory requests
        if filename != 'index.html':
            index_path = os.path.join(website_dir, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(website_dir, 'index.html')
        abort(404)
    
    return send_from_directory(website_dir, filename)

# API Routes
@websites_bp.route('/api/list')
@login_required
def api_list_websites():
    """API endpoint to list user's websites"""
    websites = Website.query.filter_by(user_id=current_user.id).all()
    
    websites_data = []
    for website in websites:
        websites_data.append({
            'id': website.id,
            'subdomain': website.subdomain,
            'title': website.title,
            'description': website.description,
            'domain': website.get_domain(),
            'ssl_enabled': website.ssl_enabled,
            'file_count': website.get_file_count(),
            'total_size': website.get_total_size(),
            'total_size_formatted': website.get_total_size_formatted(),
            'created_at': website.created_at.isoformat()
        })
    
    return jsonify({
        'success': True,
        'websites': websites_data,
        'total_websites': len(websites_data)
    })

def format_bytes(bytes_value):
    """Format bytes to human readable format"""
    if bytes_value < 1024:
        return f"{bytes_value} B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value / 1024:.1f} KB"
    elif bytes_value < 1024 * 1024 * 1024:
        return f"{bytes_value / (1024 * 1024):.1f} MB"
    else:
        return f"{bytes_value / (1024 * 1024 * 1024):.2f} GB"
