from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

from database import db
from models.url_shortener import ShortenedURL
from utils.forms import ShortenURLForm
from utils.language import get_messages

shortener_bp = Blueprint('shortener', __name__)

@shortener_bp.route('/')
def shortener_index():
    """URL shortener main page"""
    form = ShortenURLForm()
    messages = get_messages()
    
    # Get recent URLs if user is logged in
    recent_urls = []
    if current_user.is_authenticated:
        recent_urls = ShortenedURL.query.filter_by(user_id=current_user.id)\
                                       .order_by(ShortenedURL.created_at.desc()).limit(5).all()
    
    return render_template('shortener/index.html', form=form, recent_urls=recent_urls, messages=messages)

@shortener_bp.route('/shorten', methods=['POST'])
def shorten_url():
    """Shorten a URL"""
    form = ShortenURLForm()
    
    if form.validate_on_submit():
        # Check if custom code is available
        custom_code = form.custom_code.data
        if custom_code:
            existing = ShortenedURL.query.filter_by(short_code=custom_code).first()
            if existing:
                flash('Custom code already exists. Please choose another.', 'error')
                return redirect(url_for('shortener.shortener_index'))
        
        # Create shortened URL
        shortened_url = ShortenedURL(
            user_id=current_user.id if current_user.is_authenticated else None,
            original_url=form.original_url.data,
            title=form.title.data,
            description=form.description.data,
            expires_at=form.expires_at.data
        )
        
        # Set custom code if provided
        if custom_code:
            shortened_url.short_code = custom_code
        
        # Set password if provided
        if form.password.data:
            shortened_url.password_hash = generate_password_hash(form.password.data)
        
        db.session.add(shortened_url)
        db.session.commit()
        
        short_url = shortened_url.get_short_url()
        flash(f'URL shortened successfully! Short URL: {short_url}', 'success')
        
        return jsonify({
            'success': True,
            'short_url': short_url,
            'short_code': shortened_url.short_code,
            'original_url': shortened_url.original_url
        })
    
    # Return errors
    errors = []
    for field, field_errors in form.errors.items():
        for error in field_errors:
            errors.append(f'{field}: {error}')
    
    return jsonify({'success': False, 'errors': errors}), 400

@shortener_bp.route('/s/<short_code>')
def redirect_short_url(short_code):
    """Redirect to original URL"""
    shortened_url = ShortenedURL.query.filter_by(short_code=short_code).first()
    
    if not shortened_url:
        abort(404)
    
    if not shortened_url.is_active:
        abort(410)  # Gone
    
    if shortened_url.is_expired():
        abort(410)  # Gone
    
    # Check if password protected
    if shortened_url.is_password_protected():
        return redirect(url_for('shortener.password_prompt', short_code=short_code))
    
    # Increment click count
    shortened_url.increment_clicks()
    db.session.commit()
    
    return redirect(shortened_url.original_url)

@shortener_bp.route('/s/<short_code>/password', methods=['GET', 'POST'])
def password_prompt(short_code):
    """Password prompt for protected URLs"""
    shortened_url = ShortenedURL.query.filter_by(short_code=short_code).first()
    
    if not shortened_url:
        abort(404)
    
    if not shortened_url.is_password_protected():
        return redirect(url_for('shortener.redirect_short_url', short_code=short_code))
    
    messages = get_messages()
    
    if request.method == 'POST':
        password = request.form.get('password')
        
        if password and check_password_hash(shortened_url.password_hash, password):
            # Increment click count
            shortened_url.increment_clicks()
            db.session.commit()
            
            return redirect(shortened_url.original_url)
        else:
            flash('Incorrect password', 'error')
    
    return render_template('shortener/password.html', 
                         shortened_url=shortened_url, 
                         messages=messages)

@shortener_bp.route('/dashboard')
@login_required
def dashboard():
    """User's shortened URLs dashboard"""
    messages = get_messages()
    
    # Get user's URLs with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    urls = ShortenedURL.query.filter_by(user_id=current_user.id)\
                            .order_by(ShortenedURL.created_at.desc())\
                            .paginate(page=page, per_page=per_page, error_out=False)
    
    # Statistics
    total_urls = ShortenedURL.query.filter_by(user_id=current_user.id).count()
    total_clicks = sum(url.click_count for url in ShortenedURL.query.filter_by(user_id=current_user.id).all())
    active_urls = ShortenedURL.query.filter_by(user_id=current_user.id, is_active=True).count()
    
    stats = {
        'total_urls': total_urls,
        'total_clicks': total_clicks,
        'active_urls': active_urls,
        'average_clicks': round(total_clicks / max(total_urls, 1), 1)
    }
    
    return render_template('shortener/dashboard.html', 
                         urls=urls, 
                         stats=stats,
                         messages=messages)

@shortener_bp.route('/delete/<int:url_id>', methods=['POST'])
@login_required
def delete_url(url_id):
    """Delete shortened URL"""
    shortened_url = ShortenedURL.query.filter_by(id=url_id, user_id=current_user.id).first()
    
    if not shortened_url:
        abort(404)
    
    db.session.delete(shortened_url)
    db.session.commit()
    
    flash('URL deleted successfully', 'success')
    return redirect(url_for('shortener.dashboard'))

@shortener_bp.route('/toggle/<int:url_id>', methods=['POST'])
@login_required
def toggle_url(url_id):
    """Toggle URL active status"""
    shortened_url = ShortenedURL.query.filter_by(id=url_id, user_id=current_user.id).first()
    
    if not shortened_url:
        return jsonify({'error': 'URL not found'}), 404
    
    shortened_url.is_active = not shortened_url.is_active
    db.session.commit()
    
    status = 'activated' if shortened_url.is_active else 'deactivated'
    return jsonify({
        'success': True,
        'status': status,
        'is_active': shortened_url.is_active
    })

@shortener_bp.route('/stats/<int:url_id>')
@login_required
def url_stats(url_id):
    """URL statistics"""
    shortened_url = ShortenedURL.query.filter_by(id=url_id, user_id=current_user.id).first()
    
    if not shortened_url:
        abort(404)
    
    messages = get_messages()
    
    return render_template('shortener/stats.html', 
                         url=shortened_url,
                         messages=messages)

# API Routes
@shortener_bp.route('/api/shorten', methods=['POST'])
def api_shorten():
    """API endpoint to shorten URL"""
    data = request.get_json()
    
    if not data or 'url' not in data:
        return jsonify({'error': 'URL is required'}), 400
    
    # Create shortened URL
    shortened_url = ShortenedURL(
        original_url=data['url'],
        title=data.get('title'),
        description=data.get('description')
    )
    
    # Set custom code if provided
    custom_code = data.get('custom_code')
    if custom_code:
        existing = ShortenedURL.query.filter_by(short_code=custom_code).first()
        if existing:
            return jsonify({'error': 'Custom code already exists'}), 409
        shortened_url.short_code = custom_code
    
    db.session.add(shortened_url)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'short_url': shortened_url.get_short_url(),
        'short_code': shortened_url.short_code,
        'original_url': shortened_url.original_url
    })

@shortener_bp.route('/api/stats/<short_code>')
def api_url_stats(short_code):
    """API endpoint to get URL stats"""
    shortened_url = ShortenedURL.query.filter_by(short_code=short_code).first()
    
    if not shortened_url:
        return jsonify({'error': 'URL not found'}), 404
    
    return jsonify({
        'short_code': shortened_url.short_code,
        'original_url': shortened_url.original_url,
        'title': shortened_url.title,
        'click_count': shortened_url.click_count,
        'created_at': shortened_url.created_at.isoformat(),
        'last_clicked': shortened_url.last_clicked.isoformat() if shortened_url.last_clicked else None,
        'is_active': shortened_url.is_active,
        'expires_at': shortened_url.expires_at.isoformat() if shortened_url.expires_at else None
    })
