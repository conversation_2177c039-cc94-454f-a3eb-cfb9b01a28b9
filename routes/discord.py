from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, abort
from flask_login import login_required, current_user, login_user
import requests
import secrets
from datetime import datetime, timedelta

from database import db
from models.user import User
from models.discord_settings import DiscordSettings
from utils.language import get_messages

discord_bp = Blueprint('discord', __name__)

# Discord OAuth2 Configuration
DISCORD_CLIENT_ID = '1263893025749377024'
DISCORD_CLIENT_SECRET = 'YOUR_DISCORD_CLIENT_SECRET'  # Replace with actual secret
DISCORD_REDIRECT_URI = 'https://lxnd.cloud/auth/discord/callback'
DISCORD_API_BASE = 'https://discord.com/api/v10'

@discord_bp.route('/auth')
@login_required
def discord_auth():
    """Initiate Discord OAuth2 flow"""
    state = secrets.token_urlsafe(32)
    session['discord_oauth_state'] = state
    
    oauth_url = (
        f"https://discord.com/api/oauth2/authorize"
        f"?client_id={DISCORD_CLIENT_ID}"
        f"&redirect_uri={DISCORD_REDIRECT_URI}"
        f"&response_type=code"
        f"&scope=identify"
        f"&state={state}"
    )
    
    return redirect(oauth_url)

@discord_bp.route('/callback')
def discord_callback():
    """Handle Discord OAuth2 callback"""
    code = request.args.get('code')
    state = request.args.get('state')
    
    # Verify state
    if not state or state != session.get('discord_oauth_state'):
        flash('Invalid OAuth state', 'error')
        return redirect(url_for('main.dashboard'))
    
    if not code:
        flash('Discord authorization failed', 'error')
        return redirect(url_for('main.dashboard'))
    
    try:
        # Exchange code for access token
        token_data = {
            'client_id': DISCORD_CLIENT_ID,
            'client_secret': DISCORD_CLIENT_SECRET,
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': DISCORD_REDIRECT_URI
        }
        
        token_response = requests.post(
            f"{DISCORD_API_BASE}/oauth2/token",
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if token_response.status_code != 200:
            flash('Failed to get Discord access token', 'error')
            return redirect(url_for('main.dashboard'))
        
        token_info = token_response.json()
        access_token = token_info['access_token']
        
        # Get user info from Discord
        user_response = requests.get(
            f"{DISCORD_API_BASE}/users/@me",
            headers={'Authorization': f"Bearer {access_token}"}
        )
        
        if user_response.status_code != 200:
            flash('Failed to get Discord user info', 'error')
            return redirect(url_for('main.dashboard'))
        
        discord_user = user_response.json()
        
        # Update current user with Discord info
        if current_user.is_authenticated:
            current_user.discord_id = discord_user['id']
            current_user.discord_username = f"{discord_user['username']}#{discord_user['discriminator']}"
            current_user.discord_avatar = discord_user.get('avatar')
            current_user.discord_linked_at = datetime.utcnow()
            
            db.session.commit()
            
            flash('Discord account linked successfully!', 'success')
        else:
            # Handle pending verification from Discord bot
            pending_verification = session.get('pending_discord_verification')
            if pending_verification:
                discord_user_id = pending_verification.get('discord_user_id')
                
                if discord_user_id == discord_user['id']:
                    # Find or create user account
                    user = User.query.filter_by(discord_id=discord_user['id']).first()
                    
                    if not user:
                        # Create new user account
                        username = discord_user['username']
                        # Ensure unique username
                        counter = 1
                        original_username = username
                        while User.query.filter_by(username=username).first():
                            username = f"{original_username}_{counter}"
                            counter += 1
                        
                        user = User(
                            username=username,
                            email=f"{discord_user['id']}@discord.temp",  # Temporary email
                            discord_id=discord_user['id'],
                            discord_username=f"{discord_user['username']}#{discord_user['discriminator']}",
                            discord_avatar=discord_user.get('avatar'),
                            discord_linked_at=datetime.utcnow()
                        )
                        user.set_password(secrets.token_urlsafe(32))  # Random password
                        
                        db.session.add(user)
                        db.session.commit()
                    
                    # Log in the user
                    login_user(user)
                    session.pop('pending_discord_verification', None)
                    
                    flash('Discord verification completed! Welcome to LXND!', 'success')
                else:
                    flash('Discord account mismatch. Please try again.', 'error')
    
    except Exception as e:
        flash(f'Discord authentication error: {str(e)}', 'error')
    
    return redirect(url_for('main.dashboard'))

@discord_bp.route('/unlink', methods=['POST'])
@login_required
def unlink_discord():
    """Unlink Discord account"""
    current_user.discord_id = None
    current_user.discord_username = None
    current_user.discord_avatar = None
    current_user.discord_linked_at = None
    
    db.session.commit()
    
    flash('Discord account unlinked successfully', 'success')
    return redirect(url_for('auth.profile'))

@discord_bp.route('/bot/verify', methods=['POST'])
def bot_verify_user():
    """API endpoint for Discord bot to verify users"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    discord_user_id = data.get('discord_user_id')
    username = data.get('username')
    
    if not discord_user_id or not username:
        return jsonify({'error': 'Missing required fields'}), 400
    
    # Find user by username
    user = User.query.filter_by(username=username).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    # Check if Discord account is already linked
    if user.discord_id:
        if user.discord_id == discord_user_id:
            return jsonify({
                'success': True,
                'message': 'User already verified',
                'user_id': user.id,
                'is_lux': user.is_lux_active()
            })
        else:
            return jsonify({'error': 'User has different Discord account linked'}), 409
    
    # Link Discord account
    user.discord_id = discord_user_id
    user.discord_linked_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'User verified successfully',
        'user_id': user.id,
        'is_lux': user.is_lux_active()
    })

@discord_bp.route('/bot/user/<discord_user_id>')
def bot_get_user(discord_user_id):
    """API endpoint for Discord bot to get user info"""
    user = User.query.filter_by(discord_id=discord_user_id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    return jsonify({
        'user_id': user.id,
        'username': user.username,
        'email': user.email,
        'is_lux': user.is_lux_active(),
        'lux_status': user.get_lux_status(),
        'is_admin': user.is_admin,
        'is_banned': user.is_banned,
        'discord_linked_at': user.discord_linked_at.isoformat() if user.discord_linked_at else None
    })

@discord_bp.route('/bot/settings')
def bot_get_settings():
    """API endpoint for Discord bot to get settings"""
    settings = DiscordSettings.get_settings()
    
    return jsonify({
        'guild_id': settings.guild_id,
        'log_channel_id': settings.log_channel_id,
        'welcome_channel_id': settings.welcome_channel_id,
        'verification_channel_id': settings.verification_channel_id,
        'verified_role_id': settings.verified_role_id,
        'lux_role_id': settings.lux_role_id,
        'admin_role_id': settings.admin_role_id,
        'verification_enabled': settings.verification_enabled,
        'ticket_system_enabled': settings.ticket_system_enabled,
        'auto_role_enabled': settings.auto_role_enabled,
        'welcome_message': settings.welcome_message,
        'verification_message': settings.verification_message
    })

@discord_bp.route('/bot/log', methods=['POST'])
def bot_log_event():
    """API endpoint for Discord bot to log events"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Here you could implement logging to database or file
    # For now, just return success
    
    return jsonify({'success': True})

@discord_bp.route('/oauth/auth')
def discord_bot_oauth():
    """Handle Discord OAuth2 through Dashboard"""
    discord_user_id = request.args.get('discord_user_id')
    state = request.args.get('state')
    
    # If coming from Discord bot command, store the discord_user_id in session
    if discord_user_id:
        session['pending_discord_verification'] = {
            'discord_user_id': discord_user_id,
            'timestamp': datetime.utcnow().isoformat()
        }
    
    # If user is logged in, proceed with Discord OAuth
    if current_user.is_authenticated:
        return redirect(url_for('discord.discord_auth'))
    else:
        # Store the intent to verify after login
        session['after_login_action'] = 'discord_verify'
        flash('Please log in to your LXND account to complete Discord verification.', 'info')
        return redirect(url_for('auth.login'))

# Webhook endpoints for Discord bot
@discord_bp.route('/webhook/member_join', methods=['POST'])
def webhook_member_join():
    """Webhook for when a member joins the Discord server"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Process member join event
    # You can implement welcome message logic here
    
    return jsonify({'success': True})

@discord_bp.route('/webhook/member_leave', methods=['POST'])
def webhook_member_leave():
    """Webhook for when a member leaves the Discord server"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    discord_user_id = data.get('user_id')
    
    if discord_user_id:
        # Optionally unlink Discord account when user leaves
        user = User.query.filter_by(discord_id=discord_user_id).first()
        if user:
            # You can choose to unlink or keep the link
            pass
    
    return jsonify({'success': True})
