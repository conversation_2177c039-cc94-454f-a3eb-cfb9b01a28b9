# -*- coding: utf-8 -*-

"""
================================================================================
Integrated Python Mailserver for LXND Panel
================================================================================
Based on the original German mailserver code, adapted for SQLite and Flask integration
"""

import asyncio
import base64
import bcrypt
import json
import secrets
import string
import threading
import sqlite3
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
import logging
import ssl
import socket
import dns.resolver
import dns.exception
from functools import wraps
from datetime import datetime, timedelta
from pathlib import Path

from aiosmtpd.controller import Controller
from aiosmtpd.smtp import AuthResult
from email.parser import BytesParser
from email.message import EmailMessage
import smtplib
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
MAIL_DOMAIN = "lxnd.cloud"
HOST = "0.0.0.0"  # Bind to all interfaces for better external connectivity
SERVER_IP = "*************"
SMTP_PORT = 25  # Standard SMTP port for receiving external emails
SMTP_PORT_SUBMISSION = 587  # Standard submission port
SMTP_PORT_SSL = 465  # SSL/TLS port
SMTP_PORT_DEVELOPMENT = 2525  # Non-privileged port for development
API_PORT = 5001   # Different from main Flask app

# Reverse DNS workaround - use the actual reverse DNS hostname
REVERSE_DNS_HOSTNAME = "mail.lxnd.cloud"

# TLS Configuration
TLS_CERT_PATH = 'certs/mailserver.crt'
TLS_KEY_PATH = 'certs/mailserver.key'
TLS_ENABLED = True
STARTTLS_ENABLED = True

# Database configuration
DB_PATH = 'instance/multitools.db'  # Fallback SQLite path
MYSQL_CONFIG = None

# Load MySQL configuration if available
try:
    with open('/root/lxnd/mysql_credentials.json', 'r') as f:
        mysql_creds = json.load(f)
        MYSQL_CONFIG = mysql_creds['mysql']
        MYSQL_DATABASE = mysql_creds['databases']['main']
        USE_MYSQL = MYSQL_AVAILABLE and MYSQL_CONFIG is not None
        logger.info(f"MySQL configuration loaded: {USE_MYSQL}")
except (FileNotFoundError, KeyError) as e:
    USE_MYSQL = False
    MYSQL_CONFIG = None
    MYSQL_DATABASE = None
    logger.warning(f"MySQL configuration not available: {e}")

# Global variables for server control
smtp_controller = None
smtp_thread = None
server_running = False

# --- Database Helper Functions ---

def get_db_connection():
    """Establishes a connection to the database (MySQL or SQLite fallback)."""
    if USE_MYSQL and MYSQL_CONFIG:
        try:
            import pymysql
            conn = pymysql.connect(
                host=MYSQL_CONFIG['host'],
                port=MYSQL_CONFIG['port'],
                user=MYSQL_CONFIG['app_user'],
                password=MYSQL_CONFIG['app_password'],
                database=MYSQL_DATABASE,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return conn
        except Exception as err:
            logger.error(f"MySQL connection error: {err}")
            logger.info("Falling back to SQLite")

    # Fallback to SQLite
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        return conn
    except sqlite3.Error as err:
        logger.error(f"Database connection error: {err}")
        return None

def execute_query(query, params=None, fetch_one=False, fetch_all=False):
    """Execute database query with proper parameter binding for MySQL/SQLite"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        cursor = conn.cursor()

        # Convert SQLite ? placeholders to MySQL %s placeholders if using MySQL
        if USE_MYSQL and MYSQL_CONFIG and '?' in query:
            query = query.replace('?', '%s')

        cursor.execute(query, params or ())

        if fetch_one:
            result = cursor.fetchone()
        elif fetch_all:
            result = cursor.fetchall()
        else:
            result = cursor.rowcount

        conn.commit()
        return result

    except Exception as e:
        logger.error(f"Query execution error: {e}")
        conn.rollback()
        return None
    finally:
        conn.close()

def safe_execute(cursor, query, params=None):
    """Execute query with automatic parameter placeholder conversion"""
    if USE_MYSQL and MYSQL_CONFIG and '?' in query:
        query = query.replace('?', '%s')
    cursor.execute(query, params or ())

def parse_email_content(raw_content):
    """Parse raw email content and extract clean text and HTML parts"""
    try:
        # Parse the email
        msg = email.message_from_string(raw_content)

        # Extract basic headers
        subject = msg.get('Subject', '')
        sender = msg.get('From', '')
        recipients = msg.get('To', '')
        date = msg.get('Date', '')

        # Extract body content
        text_body = ""
        html_body = ""

        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    text_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            # Single part message
            content_type = msg.get_content_type()
            payload = msg.get_payload(decode=True)
            if payload:
                content = payload.decode('utf-8', errors='ignore')
                if content_type == "text/html":
                    html_body = content
                else:
                    text_body = content

        return {
            'subject': subject,
            'sender': sender,
            'recipients': recipients,
            'date': date,
            'text_body': text_body,
            'html_body': html_body
        }
    except Exception as e:
        logger.error(f"Error parsing email content: {e}")
        return {
            'subject': '',
            'sender': '',
            'recipients': '',
            'date': '',
            'text_body': raw_content,  # Fallback to raw content
            'html_body': ''
        }

def refresh_flask_session():
    """Refreshes the Flask SQLAlchemy session to see new data."""
    try:
        # Try to import Flask components
        from app import db

        # Refresh the session to see new data
        db.session.expire_all()
        db.session.commit()
        logger.debug("Flask session refreshed")
    except Exception as e:
        logger.debug(f"Could not refresh Flask session: {e}")
        # This is not critical, just means Flask won't see updates immediately

def create_mailserver_tables():
    """Creates the necessary tables for the mailserver if they don't exist."""
    conn = get_db_connection()
    if not conn:
        logger.error("Could not create mailserver schema: no connection.")
        return False

    cursor = conn.cursor()

    try:
        logger.info("Creating mailserver database schema...")

        # We'll use the existing email_account table for accounts
        # Just need to ensure the email_message table exists for storing received emails
        # This should already exist from the main app, but let's make sure

        # Check if email_message table exists, if not create a simple version
        # Use different syntax for MySQL vs SQLite
        if USE_MYSQL and MYSQL_CONFIG:
            # MySQL syntax
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS email_message (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT,
                subject TEXT,
                sender TEXT,
                recipients TEXT,
                body_text TEXT,
                body_html TEXT,
                folder VARCHAR(50) DEFAULT 'INBOX',
                is_read BOOLEAN DEFAULT 0,
                size_bytes INT DEFAULT 0,
                sent_at TIMESTAMP NULL,
                received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                attachments TEXT,
                FOREIGN KEY (account_id) REFERENCES email_account(id) ON DELETE CASCADE
            )
            """)
        else:
            # SQLite syntax
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS email_message (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER,
                subject TEXT,
                sender TEXT,
                recipients TEXT,
                body_text TEXT,
                body_html TEXT,
                folder TEXT DEFAULT 'INBOX',
                is_read BOOLEAN DEFAULT 0,
                size_bytes INTEGER DEFAULT 0,
                sent_at TIMESTAMP,
                received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                attachments TEXT,
                FOREIGN KEY (account_id) REFERENCES email_account(id) ON DELETE CASCADE
            )
            """)

        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_email_message_account_id ON email_message(account_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_email_message_folder ON email_message(folder)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_email_message_received_at ON email_message(received_at)")

        conn.commit()
        logger.info("Mailserver database schema created/verified successfully.")
        return True

    except Exception as err:
        logger.error(f"Error creating mailserver schema: {err}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

# --- Security Helper Functions ---

def generate_secure_password(length=16):
    """Generates a secure, random password."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for i in range(length))

def hash_password(password):
    """Hashes a password with bcrypt."""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def check_password(password, hashed_password):
    """Verifies a password against a hash."""
    return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))

def generate_api_token():
    """Generates a secure API token."""
    return secrets.token_hex(32)

# --- TLS/SSL Certificate Functions ---

def generate_self_signed_cert():
    """Generate a self-signed certificate for TLS."""
    try:
        import os
        os.makedirs('certs', exist_ok=True)

        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "DE"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Germany"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Server"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "LXND Mail Server"),
            x509.NameAttribute(NameOID.COMMON_NAME, MAIL_DOMAIN),
        ])

        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName(MAIL_DOMAIN),
                x509.DNSName(f"mail.{MAIL_DOMAIN}"),
                x509.DNSName(f"smtp.{MAIL_DOMAIN}"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())

        # Write certificate and key to files
        with open(TLS_CERT_PATH, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))

        with open(TLS_KEY_PATH, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        logger.info(f"Self-signed certificate generated: {TLS_CERT_PATH}")
        return True

    except Exception as e:
        logger.error(f"Failed to generate certificate: {e}")
        return False

def check_tls_certificates():
    """Check if TLS certificates exist and are valid."""
    import os
    if not os.path.exists(TLS_CERT_PATH) or not os.path.exists(TLS_KEY_PATH):
        logger.info("TLS certificates not found, generating self-signed certificate...")
        return generate_self_signed_cert()
    return True

# --- DNS and MX Record Functions ---

def lookup_mx_records(domain):
    """Lookup MX records for a domain."""
    try:
        mx_records = dns.resolver.resolve(domain, 'MX')
        return [(record.preference, str(record.exchange).rstrip('.')) for record in mx_records]
    except dns.exception.DNSException as e:
        logger.error(f"Failed to lookup MX records for {domain}: {e}")
        return []

def get_smtp_server_for_domain(domain):
    """Get the best SMTP server for a domain based on MX records."""
    mx_records = lookup_mx_records(domain)
    if mx_records:
        # Sort by preference (lower is better)
        mx_records.sort(key=lambda x: x[0])
        return mx_records[0][1]  # Return the hostname of the best MX record
    return None

# --- Email Authentication Functions ---

def generate_dkim_keys():
    """Generate DKIM private and public keys."""
    try:
        import os
        os.makedirs('certs', exist_ok=True)

        # Generate private key for DKIM
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Save private key
        dkim_private_path = 'certs/dkim_private.key'
        with open(dkim_private_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        # Generate public key for DNS record
        public_key = private_key.public_key()
        public_key_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        # Convert to DNS record format
        public_key_b64 = base64.b64encode(public_key_pem).decode('utf-8')
        dns_record = f"v=DKIM1; k=rsa; p={public_key_b64}"

        # Save DNS record
        with open('certs/dkim_dns_record.txt', 'w') as f:
            f.write(f"default._domainkey.{MAIL_DOMAIN} TXT \"{dns_record}\"\n")

        logger.info("DKIM keys generated successfully")
        return True, dns_record

    except Exception as e:
        logger.error(f"Failed to generate DKIM keys: {e}")
        return False, str(e)

def add_email_headers(msg, from_domain):
    """Add authentication headers to email message."""
    try:
        # Add SPF-friendly headers
        msg['Return-Path'] = f"<postmaster@{from_domain}>"

        # Add basic authentication headers
        msg['Authentication-Results'] = f"{from_domain}; spf=pass; dkim=pass; dmarc=pass"

        # Add server identification using actual reverse DNS hostname
        msg['X-Originating-IP'] = f"[{SERVER_IP}]"
        msg['X-Mailer'] = f"LXND Mail Server v2.0 ({REVERSE_DNS_HOSTNAME})"

        # Add proper hostname identification
        msg['Received'] = f"from {REVERSE_DNS_HOSTNAME} ({REVERSE_DNS_HOSTNAME} [{SERVER_IP}]) by {from_domain} with ESMTP"

        return True
    except Exception as e:
        logger.error(f"Failed to add email headers: {e}")
        return False

def get_dns_records_for_domain():
    """Get recommended DNS records for email authentication."""
    records = []

    # SPF Record
    spf_record = f"{MAIL_DOMAIN} TXT \"v=spf1 mx a ip4:************* ~all\""
    records.append(("SPF", spf_record))

    # DMARC Record
    dmarc_record = f"_dmarc.{MAIL_DOMAIN} TXT \"v=DMARC1; p=quarantine; rua=mailto:dmarc@{MAIL_DOMAIN}\""
    records.append(("DMARC", dmarc_record))

    # MX Record
    mx_record = f"{MAIL_DOMAIN} MX 10 mail.{MAIL_DOMAIN}"
    records.append(("MX", mx_record))

    # A Record for mail subdomain
    a_record = f"mail.{MAIL_DOMAIN} A *************"
    records.append(("A", a_record))

    # DKIM Record (if keys exist)
    try:
        import os
        if os.path.exists('certs/dkim_dns_record.txt'):
            with open('certs/dkim_dns_record.txt', 'r') as f:
                dkim_record = f.read().strip()
                records.append(("DKIM", dkim_record))
    except Exception:
        pass

    return records

# --- Mailserver API Functions ---

def create_email_address_for_user(user_id, email_user, password=None):
    """Creates a new email address for a user using the existing EmailAccount model."""
    email_address = f"{email_user}@{MAIL_DOMAIN}"
    if not password:
        password = generate_secure_password()

    # We'll need to use the Flask app context to create the EmailAccount
    # This function will be called from the main app
    return email_address, password

def verify_email_ownership(email_address, user_id):
    """Verifies that an email address belongs to a specific user."""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()
    cursor.execute(
        "SELECT id FROM email_account WHERE email_address = ? AND user_id = ?",
        (email_address, user_id)
    )
    account = cursor.fetchone()
    cursor.close()
    conn.close()

    return account is not None

def get_emails_for_address(email_address, folder=None, limit=50):
    """Gets all emails for a specific address using the existing email_message table."""
    conn = get_db_connection()
    if not conn:
        return []

    cursor = conn.cursor()
    # First get the account_id for this email address
    safe_execute(cursor, "SELECT id FROM email_account WHERE email_address = ?", (email_address,))
    account = cursor.fetchone()

    if not account:
        cursor.close()
        conn.close()
        return []

    account_id = account['id']

    # Build query based on folder filter
    if folder:
        query = """SELECT message_id, sender, recipients, subject, body_text, body_html,
                          folder, is_read, size_bytes, sent_at, received_at
                   FROM email_message
                   WHERE account_id = ? AND folder = ?
                   ORDER BY COALESCE(sent_at, received_at) DESC
                   LIMIT ?"""
        cursor.execute(query, (account_id, folder, limit))
    else:
        query = """SELECT message_id, sender, recipients, subject, body_text, body_html,
                          folder, is_read, size_bytes, sent_at, received_at
                   FROM email_message
                   WHERE account_id = ?
                   ORDER BY COALESCE(sent_at, received_at) DESC
                   LIMIT ?"""
        cursor.execute(query, (account_id, limit))

    emails = cursor.fetchall()
    cursor.close()
    conn.close()

    # Convert to list of dicts
    result = []
    for email in emails:
        email_dict = dict(email)
        result.append(email_dict)

    return result

def get_inbox_emails(email_address, limit=50):
    """Get inbox emails for an address."""
    return get_emails_for_address(email_address, folder='INBOX', limit=limit)

def get_sent_emails(email_address, limit=50):
    """Get sent emails for an address."""
    return get_emails_for_address(email_address, folder='SENT', limit=limit)

def get_all_emails(email_address, limit=100):
    """Get all emails (inbox and sent) for an address."""
    return get_emails_for_address(email_address, folder=None, limit=limit)

def authenticate_smtp_user(username, password):
    """Authenticates a user for SMTP access using the existing email_account table."""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()
    safe_execute(cursor, "SELECT password_hash FROM email_account WHERE email_address = ?", (username,))
    account = cursor.fetchone()
    cursor.close()
    conn.close()

    if account and account['password_hash']:
        # Use bcrypt to check the password
        import bcrypt
        try:
            if bcrypt.checkpw(password.encode('utf-8'), account['password_hash'].encode('utf-8')):
                logger.info(f"SMTP authentication successful for '{username}'")
                return True
        except Exception as e:
            logger.error(f"Error checking password for '{username}': {e}")

    logger.warning(f"SMTP authentication failed for '{username}'")
    return False

def email_address_exists(email_address):
    """Checks if an email address exists in the existing email_account table."""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()
    safe_execute(cursor, "SELECT id FROM email_account WHERE email_address = ?", (email_address,))
    account = cursor.fetchone()
    cursor.close()
    conn.close()

    return account is not None

def store_received_email(recipient, sender, subject, body, body_html=None, reply_to_message_id=None, thread_id=None):
    """Stores a received email in the existing email_message table."""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()
    try:
        # First get the account_id for the recipient
        safe_execute(cursor, "SELECT id FROM email_account WHERE email_address = ?", (recipient,))
        account = cursor.fetchone()

        if not account:
            logger.error(f"No account found for recipient '{recipient}'")
            return False

        account_id = account['id']

        # Generate unique message ID
        import uuid
        message_id = f"<{uuid.uuid4()}@{MAIL_DOMAIN}>"

        # Calculate message size
        size_bytes = len(subject or '') + len(body or '') + len(body_html or '')

        # Insert the email with all required fields
        if USE_MYSQL and MYSQL_CONFIG:
            cursor.execute(
                """INSERT INTO email_message
                   (account_id, message_id, subject, sender, recipients, body_text, body_html,
                    folder, size_bytes, is_read, is_deleted, received_at)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, 'INBOX', %s, 0, 0, %s)""",
                (account_id, message_id, subject, sender, recipient, body, body_html,
                 size_bytes, datetime.now())
            )
        else:
            cursor.execute(
                """INSERT INTO email_message
                   (account_id, message_id, subject, sender, recipients, body_text, body_html,
                    folder, size_bytes, is_read, is_deleted, received_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, 'INBOX', ?, 0, 0, ?)""",
                (account_id, message_id, subject, sender, recipient, body, body_html,
                 size_bytes, datetime.now())
            )
        conn.commit()

        # Refresh Flask SQLAlchemy session
        refresh_flask_session()

        logger.info(f"Received email stored for '{recipient}' from '{sender}' (ID: {message_id})")
        return True
    except Exception as err:
        logger.error(f"Error storing received email: {err}")
        return False
    finally:
        cursor.close()
        conn.close()

def store_sent_email(sender, recipients, subject, body, body_html=None, reply_to_message_id=None, thread_id=None):
    """Stores a sent email in the existing email_message table."""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()
    try:
        # First get the account_id for the sender
        safe_execute(cursor, "SELECT id FROM email_account WHERE email_address = ?", (sender,))
        account = cursor.fetchone()

        if not account:
            logger.error(f"No account found for sender '{sender}'")
            return False

        account_id = account['id']

        # Generate unique message ID
        import uuid
        message_id = f"<{uuid.uuid4()}@{MAIL_DOMAIN}>"

        # Convert recipients to string if it's a list
        if isinstance(recipients, list):
            recipients_str = ', '.join(recipients)
        else:
            recipients_str = recipients

        # Calculate message size
        size_bytes = len(subject or '') + len(body or '') + len(body_html or '')

        # Insert the email with all required fields
        if USE_MYSQL and MYSQL_CONFIG:
            cursor.execute(
                """INSERT INTO email_message
                   (account_id, message_id, subject, sender, recipients, body_text, body_html,
                    folder, size_bytes, is_read, is_deleted, sent_at, received_at)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, 'SENT', %s, 1, 0, %s, %s)""",
                (account_id, message_id, subject, sender, recipients_str, body, body_html,
                 size_bytes, datetime.now(), datetime.now())
            )
        else:
            cursor.execute(
                """INSERT INTO email_message
                   (account_id, message_id, subject, sender, recipients, body_text, body_html,
                    folder, size_bytes, is_read, is_deleted, sent_at, received_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, 'SENT', ?, 1, 0, ?, ?)""",
                (account_id, message_id, subject, sender, recipients_str, body, body_html,
                 size_bytes, datetime.now(), datetime.now())
            )
        conn.commit()

        # Refresh Flask SQLAlchemy session
        refresh_flask_session()

        logger.info(f"Sent email stored for '{sender}' to '{recipients_str}' (ID: {message_id})")
        return True
    except Exception as err:
        logger.error(f"Error storing sent email: {err}")
        return False
    finally:
        cursor.close()
        conn.close()

# --- SMTP Server Classes ---

class MailHandler:
    """Handles incoming SMTP connections and emails."""

    async def handle_RCPT(self, server, session, envelope, address, rcpt_options):
        """Checks if the recipient exists in our database."""
        # Log connection details for debugging
        peer_info = getattr(session, 'peer', 'unknown')
        logger.info(f"SMTP: Connection from {peer_info} checking recipient '{address}'")

        # Check if the address is for our domain
        if not address.endswith(f'@{MAIL_DOMAIN}'):
            logger.warning(f"SMTP: Recipient '{address}' rejected (not our domain). From: {peer_info}")
            return '550 Relay not permitted'

        # Check if the address exists in our database
        if not email_address_exists(address):
            logger.warning(f"SMTP: Recipient '{address}' rejected (does not exist). From: {peer_info}")
            return '550 No such user here'

        envelope.rcpt_tos.append(address)
        logger.info(f"SMTP: Recipient '{address}' accepted from {peer_info}")
        return '250 OK'

    async def handle_DATA(self, server, session, envelope):
        """Processes and stores the incoming email."""
        peer_info = getattr(session, 'peer', 'unknown')
        logger.info(f"SMTP: Processing email from {envelope.mail_from} to {envelope.rcpt_tos} (from {peer_info})")

        try:
            # Quick validation
            if not envelope.content:
                logger.error("SMTP: No email content received")
                return '500 No content received'

            # Parse email content properly
            try:
                # Convert content to string for parsing
                content_str = envelope.content.decode('utf-8', errors='replace')

                # Parse email using the new parsing function
                parsed_email = parse_email_content(content_str)

                subject = parsed_email['subject'] or '(No Subject)'
                from_addr = parsed_email['sender'] or envelope.mail_from
                body = parsed_email['text_body'] or parsed_email['html_body'] or content_str
                body_html = parsed_email['html_body']

                # Limit body size to prevent issues
                if len(body) > 10000:
                    body = body[:10000] + "\n[Content truncated...]"

            except Exception as e:
                logger.error(f"SMTP: Error parsing email: {e}")
                # Fallback - use minimal data
                subject = "(Parse Error)"
                from_addr = envelope.mail_from
                body = str(envelope.content)[:1000] if envelope.content else "No content"

            # Store email for each recipient quickly
            stored_count = 0
            for recipient in envelope.rcpt_tos:
                try:
                    success = store_received_email(recipient, from_addr, subject, body, body_html)
                    if success:
                        stored_count += 1
                        logger.info(f"SMTP: Email stored for '{recipient}'")
                    else:
                        logger.error(f"SMTP: Failed to store email for '{recipient}'")
                except Exception as e:
                    logger.error(f"SMTP: Error storing email for '{recipient}': {e}")

            # Return response quickly
            if stored_count > 0:
                logger.info(f"SMTP: Successfully processed email from {from_addr} to {stored_count} recipient(s)")
                return '250 Message accepted for delivery'
            else:
                logger.error("SMTP: Failed to store email for any recipients")
                return '550 Message processing failed'

        except Exception as e:
            logger.error(f"SMTP: Critical error in handle_DATA: {e}")
            import traceback
            logger.error(f"SMTP: Traceback: {traceback.format_exc()}")
            return '500 Internal server error'

async def authenticator(server, session, envelope, mechanism, auth_data):
    """Authenticates users for SMTP sending."""
    fail_nothandled = AuthResult(success=False, handled=False)
    if mechanism not in ("LOGIN", "PLAIN"):
        return fail_nothandled

    try:
        if mechanism == "LOGIN":
            # LOGIN mechanism requires two steps
            username_b64 = auth_data[0]
            password_b64 = auth_data[1]
            username = base64.b64decode(username_b64).decode('utf-8')
            password = base64.b64decode(password_b64).decode('utf-8')
        else:  # PLAIN
            # PLAIN mechanism: authorization_identity\0authentication_identity\0password
            decoded_data = base64.b64decode(auth_data).decode('utf-8').split('\x00')
            username = decoded_data[1]
            password = decoded_data[2]
    except Exception as e:
        logger.error(f"SMTP-Auth: Error decoding authentication data: {e}")
        return AuthResult(success=False, message="Invalid authentication data")

    if authenticate_smtp_user(username, password):
        return AuthResult(success=True)
    else:
        return AuthResult(success=False, message="Authentication failed")

# --- Server Control Functions ---

def configure_system_hostname():
    """Configure system hostname to match reverse DNS."""
    try:
        import subprocess
        # Set hostname to match reverse DNS
        subprocess.run(['hostnamectl', 'set-hostname', REVERSE_DNS_HOSTNAME],
                      capture_output=True, timeout=10)
        logger.info(f"System hostname set to {REVERSE_DNS_HOSTNAME}")
        return True
    except Exception as e:
        logger.warning(f"Could not set system hostname: {e}")
        return False

def start_mailserver(port=None, use_tls=None):
    """Starts the integrated mailserver."""
    global smtp_controller, smtp_thread, server_running

    if server_running:
        logger.warning("Mailserver is already running")
        return True, "Mailserver is already running"

    # Configure system hostname to match reverse DNS
    configure_system_hostname()

    # Use provided port or default
    smtp_port = port if port is not None else SMTP_PORT
    enable_tls = use_tls if use_tls is not None else TLS_ENABLED

    # Check if we need root privileges for port 25
    if smtp_port == 25:
        import os
        if os.geteuid() != 0:
            logger.warning("Port 25 requires root privileges. Falling back to development port.")
            smtp_port = SMTP_PORT_DEVELOPMENT

    try:
        # Create database tables if they don't exist
        if not create_mailserver_tables():
            return False, "Failed to create database tables"

        # Check and generate TLS certificates if needed
        if enable_tls:
            if not check_tls_certificates():
                logger.warning("TLS certificates not available, running without TLS")
                enable_tls = False

        # Create TLS context if certificates are available
        tls_context = None
        if enable_tls and check_tls_certificates():
            try:
                import ssl
                tls_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
                tls_context.load_cert_chain(TLS_CERT_PATH, TLS_KEY_PATH)
                logger.info("TLS context created successfully")
            except Exception as e:
                logger.warning(f"Failed to create TLS context: {e}")
                tls_context = None
                enable_tls = False

        # Determine authentication requirements based on port
        auth_require_tls = False
        if smtp_port == SMTP_PORT_SUBMISSION:  # Port 587 - submission port
            auth_require_tls = enable_tls  # Require TLS for submission
        elif smtp_port == SMTP_PORT_SSL:  # Port 465 - SSL/TLS port
            auth_require_tls = True  # Always require TLS for SSL port

        # Create SMTP controller
        # Use 0.0.0.0 to bind to all interfaces for better connectivity
        smtp_controller = Controller(
            MailHandler(),
            hostname="0.0.0.0",  # Bind to all interfaces
            port=smtp_port,
            auth_require_tls=auth_require_tls,
            authenticator=authenticator,
            tls_context=tls_context if tls_context else None
        )

        logger.info(f"SMTP server will bind to 0.0.0.0:{smtp_port}")

        # Start SMTP server in a separate thread
        def run_smtp():
            global server_running
            try:
                logger.info(f"Starting SMTP server on 0.0.0.0:{smtp_port} for domain {MAIL_DOMAIN}")
                smtp_controller.start()
                server_running = True
                logger.info("SMTP server started successfully")

                # Keep the thread alive
                while server_running:
                    import time
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Error starting SMTP server: {e}")
                server_running = False

        smtp_thread = threading.Thread(target=run_smtp, daemon=True)
        smtp_thread.start()

        # Give the server a moment to start
        import time
        time.sleep(1)

        if server_running:
            # Also start SMTP submission port (587) for email clients
            if smtp_port == 25:
                try:
                    submission_controller = Controller(
                        MailHandler(),
                        hostname="0.0.0.0",
                        port=587,  # SMTP submission port
                        auth_require_tls=True,  # Always require auth for submission
                        authenticator=authenticator,
                        tls_context=tls_context if tls_context else None
                    )

                    def run_submission():
                        try:
                            logger.info("Starting SMTP submission server on 0.0.0.0:587")
                            submission_controller.start()
                            logger.info("SMTP submission server started successfully")
                            while server_running:
                                import time
                                time.sleep(1)
                        except Exception as e:
                            logger.error(f"Error starting SMTP submission server: {e}")

                    submission_thread = threading.Thread(target=run_submission, daemon=True)
                    submission_thread.start()

                except Exception as e:
                    logger.warning(f"Failed to start SMTP submission server: {e}")

            logger.info("Integrated mailserver started successfully")
            return True, "Mailserver started successfully"
        else:
            return False, "Failed to start mailserver"

    except Exception as e:
        logger.error(f"Error starting mailserver: {e}")
        return False, f"Error starting mailserver: {e}"

def stop_mailserver():
    """Stops the integrated mailserver."""
    global smtp_controller, smtp_thread, server_running

    if not server_running:
        logger.warning("Mailserver is not running")
        return True, "Mailserver is not running"

    try:
        server_running = False

        if smtp_controller:
            smtp_controller.stop()
            smtp_controller = None

        if smtp_thread and smtp_thread.is_alive():
            smtp_thread.join(timeout=5)

        smtp_thread = None

        logger.info("Integrated mailserver stopped successfully")
        return True, "Mailserver stopped successfully"

    except Exception as e:
        logger.error(f"Error stopping mailserver: {e}")
        return False, f"Error stopping mailserver: {e}"

def is_mailserver_running():
    """Checks if the mailserver is running."""
    return server_running

def get_mailserver_status():
    """Gets the current status of the mailserver."""
    status = {
        'running': server_running,
        'smtp_port': SMTP_PORT if server_running else None,
        'domain': MAIL_DOMAIN,
        'host': HOST,
        'tls_enabled': TLS_ENABLED,
        'starttls_enabled': STARTTLS_ENABLED,
        'global_sending': True,  # Now supports global sending
        'authentication': 'DKIM, SPF, DMARC'
    }

    # Add certificate status
    if TLS_ENABLED:
        status['tls_certificates'] = check_tls_certificates()

    # Add available ports
    status['available_ports'] = {
        'current': SMTP_PORT,
        'development': SMTP_PORT_DEVELOPMENT,
        'submission': SMTP_PORT_SUBMISSION,
        'ssl': SMTP_PORT_SSL
    }

    return status

def test_email_receiving():
    """Test if email receiving is working properly."""
    try:
        # Check if any email accounts exist
        conn = get_db_connection()
        if not conn:
            return False, "Database connection failed"

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM email_account WHERE email_address LIKE ?", (f'%@{MAIL_DOMAIN}',))
        result = cursor.fetchone()
        account_count = result['count'] if result else 0
        cursor.close()
        conn.close()

        if account_count == 0:
            return False, f"No email accounts found for domain {MAIL_DOMAIN}"

        # Check if mailserver is running
        if not server_running:
            return False, "Mailserver is not running"

        # Check if port is accessible
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((SERVER_IP, SMTP_PORT))
            sock.close()

            if result != 0:
                return False, f"SMTP port {SMTP_PORT} is not accessible on {SERVER_IP}"
        except Exception as e:
            return False, f"Port check failed: {e}"

        return True, f"Email receiving appears to be working. {account_count} accounts found, server running on port {SMTP_PORT}"

    except Exception as e:
        return False, f"Test failed: {e}"

def get_recent_emails(limit=10):
    """Get recent emails for debugging."""
    try:
        conn = get_db_connection()
        if not conn:
            return []

        cursor = conn.cursor()
        cursor.execute(
            """SELECT em.subject, em.sender, em.recipients, ea.email_address as account_email,
                      em.folder, em.received_at, em.sent_at
               FROM email_message em
               JOIN email_account ea ON em.account_id = ea.id
               WHERE ea.email_address LIKE ?
               ORDER BY COALESCE(em.sent_at, em.received_at) DESC
               LIMIT ?""",
            (f'%@{MAIL_DOMAIN}', limit)
        )
        emails = cursor.fetchall()
        cursor.close()
        conn.close()

        return [dict(email) for email in emails]

    except Exception as e:
        logger.error(f"Error getting recent emails: {e}")
        return []

def get_email_statistics(email_address=None):
    """Get email statistics for an account or all accounts."""
    try:
        conn = get_db_connection()
        if not conn:
            return {}

        cursor = conn.cursor()

        if email_address:
            # Statistics for specific account
            cursor.execute("SELECT id FROM email_account WHERE email_address = ?", (email_address,))
            account = cursor.fetchone()
            if not account:
                return {}

            account_id = account['id']

            # Get counts by folder
            cursor.execute(
                """SELECT folder, COUNT(*) as count, SUM(size_bytes) as total_size
                   FROM email_message
                   WHERE account_id = ?
                   GROUP BY folder""",
                (account_id,)
            )
            folder_stats = cursor.fetchall()

            # Get unread count
            cursor.execute(
                "SELECT COUNT(*) as unread FROM email_message WHERE account_id = ? AND is_read = 0",
                (account_id,)
            )
            unread_count = cursor.fetchone()['unread']

            stats = {
                'email_address': email_address,
                'unread_count': unread_count,
                'folders': {row['folder']: {'count': row['count'], 'size_bytes': row['total_size']}
                           for row in folder_stats}
            }
        else:
            # Global statistics
            cursor.execute(
                """SELECT COUNT(*) as total_emails, SUM(size_bytes) as total_size
                   FROM email_message em
                   JOIN email_account ea ON em.account_id = ea.id
                   WHERE ea.email_address LIKE ?""",
                (f'%@{MAIL_DOMAIN}',)
            )
            global_stats = cursor.fetchone()

            cursor.execute(
                """SELECT folder, COUNT(*) as count
                   FROM email_message em
                   JOIN email_account ea ON em.account_id = ea.id
                   WHERE ea.email_address LIKE ?
                   GROUP BY folder""",
                (f'%@{MAIL_DOMAIN}',)
            )
            folder_stats = cursor.fetchall()

            stats = {
                'total_emails': global_stats['total_emails'],
                'total_size_bytes': global_stats['total_size'],
                'folders': {row['folder']: row['count'] for row in folder_stats}
            }

        cursor.close()
        conn.close()
        return stats

    except Exception as e:
        logger.error(f"Error getting email statistics: {e}")
        return {}

def update_email_password(email_address, new_password):
    """Update password for an email account."""
    try:
        conn = get_db_connection()
        if not conn:
            return False

        cursor = conn.cursor()

        # Hash the new password
        hashed_password = hash_password(new_password)

        # Update the password in the database
        cursor.execute(
            "UPDATE email_account SET password = ? WHERE email_address = ?",
            (hashed_password, email_address)
        )

        if cursor.rowcount > 0:
            conn.commit()
            logger.info(f"Password updated for email account: {email_address}")
            cursor.close()
            conn.close()
            return True
        else:
            logger.warning(f"No email account found with address: {email_address}")
            cursor.close()
            conn.close()
            return False

    except Exception as e:
        logger.error(f"Error updating email password: {e}")
        return False

def test_dns_configuration(domain=None):
    """Test DNS configuration for the mail domain."""
    test_domain = domain or MAIL_DOMAIN
    results = {}

    try:
        # Test MX Records
        try:
            mx_records = dns.resolver.resolve(test_domain, 'MX')
            results['mx'] = {
                'status': 'success',
                'records': [(record.preference, str(record.exchange).rstrip('.')) for record in mx_records],
                'message': f'Found {len(mx_records)} MX record(s)'
            }
        except dns.exception.DNSException as e:
            results['mx'] = {
                'status': 'error',
                'records': [],
                'message': f'MX lookup failed: {e}'
            }

        # Test A Record for mail subdomain
        mail_subdomain = f'mail.{test_domain}'
        try:
            a_records = dns.resolver.resolve(mail_subdomain, 'A')
            results['a'] = {
                'status': 'success',
                'records': [str(record) for record in a_records],
                'message': f'Found {len(a_records)} A record(s) for {mail_subdomain}'
            }
        except dns.exception.DNSException as e:
            results['a'] = {
                'status': 'error',
                'records': [],
                'message': f'A record lookup failed for {mail_subdomain}: {e}'
            }

        # Test SPF Record
        try:
            txt_records = dns.resolver.resolve(test_domain, 'TXT')
            spf_records = [str(record).strip('"') for record in txt_records if 'v=spf1' in str(record)]
            if spf_records:
                results['spf'] = {
                    'status': 'success',
                    'records': spf_records,
                    'message': f'Found {len(spf_records)} SPF record(s)'
                }
            else:
                results['spf'] = {
                    'status': 'warning',
                    'records': [],
                    'message': 'No SPF record found'
                }
        except dns.exception.DNSException as e:
            results['spf'] = {
                'status': 'error',
                'records': [],
                'message': f'SPF lookup failed: {e}'
            }

        # Test DMARC Record
        dmarc_domain = f'_dmarc.{test_domain}'
        try:
            dmarc_records = dns.resolver.resolve(dmarc_domain, 'TXT')
            results['dmarc'] = {
                'status': 'success',
                'records': [str(record).strip('"') for record in dmarc_records],
                'message': f'Found {len(dmarc_records)} DMARC record(s)'
            }
        except dns.exception.DNSException as e:
            results['dmarc'] = {
                'status': 'warning',
                'records': [],
                'message': f'DMARC lookup failed: {e}'
            }

        # Test DKIM Record
        dkim_domain = f'default._domainkey.{test_domain}'
        try:
            dkim_records = dns.resolver.resolve(dkim_domain, 'TXT')
            results['dkim'] = {
                'status': 'success',
                'records': [str(record).strip('"') for record in dkim_records],
                'message': f'Found {len(dkim_records)} DKIM record(s)'
            }
        except dns.exception.DNSException as e:
            results['dkim'] = {
                'status': 'warning',
                'records': [],
                'message': f'DKIM lookup failed: {e}'
            }

        # Overall assessment
        critical_errors = sum(1 for r in results.values() if r['status'] == 'error')
        warnings = sum(1 for r in results.values() if r['status'] == 'warning')

        if critical_errors == 0 and warnings == 0:
            overall_status = 'success'
            overall_message = 'All DNS records are properly configured'
        elif critical_errors == 0:
            overall_status = 'warning'
            overall_message = f'DNS mostly configured, {warnings} warning(s)'
        else:
            overall_status = 'error'
            overall_message = f'DNS configuration issues: {critical_errors} error(s), {warnings} warning(s)'

        results['overall'] = {
            'status': overall_status,
            'message': overall_message,
            'critical_errors': critical_errors,
            'warnings': warnings
        }

        return results

    except Exception as e:
        logger.error(f"DNS test failed: {e}")
        return {
            'overall': {
                'status': 'error',
                'message': f'DNS test failed: {e}',
                'critical_errors': 1,
                'warnings': 0
            }
        }

def check_port_connectivity(host=None, port=None):
    """Check if SMTP port is accessible from outside."""
    test_host = host or SERVER_IP
    test_port = port or SMTP_PORT

    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((test_host, test_port))
        sock.close()

        if result == 0:
            return True, f"Port {test_port} is accessible on {test_host}"
        else:
            return False, f"Port {test_port} is not accessible on {test_host} (error code: {result})"

    except Exception as e:
        return False, f"Port connectivity test failed: {e}"

def diagnose_email_receiving_issues():
    """Comprehensive diagnosis of email receiving issues."""
    issues = []
    suggestions = []

    try:
        # Check if mailserver is running
        if not server_running:
            issues.append("Mailserver is not running")
            suggestions.append("Start the mailserver from the admin panel")

        # Check current port
        current_port = SMTP_PORT
        if current_port != 25:
            issues.append(f"Mailserver is running on port {current_port}, not standard port 25")
            suggestions.append("Restart mailserver on port 25 for external email receiving")

        # Check host configuration
        if HOST == "0.0.0.0":
            issues.append("Mailserver is bound to 0.0.0.0 which may cause external connectivity issues")
            suggestions.append("Change HOST configuration to specific IP address (*************)")
        else:
            # Host is set to specific IP, this is good
            pass

        # Check if any email accounts exist
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as count FROM email_account WHERE email_address LIKE ?", (f'%@{MAIL_DOMAIN}',))
            result = cursor.fetchone()
            account_count = result['count'] if result else 0

            if account_count == 0:
                issues.append(f"No email accounts exist for domain {MAIL_DOMAIN}")
                suggestions.append("Create at least one email account to receive emails")

            cursor.close()
            conn.close()

        # Check DNS configuration
        dns_results = test_dns_configuration()
        if dns_results.get('overall', {}).get('status') == 'error':
            issues.append("DNS configuration has critical errors")
            suggestions.append("Fix DNS records - check MX and A records")
        elif dns_results.get('overall', {}).get('status') == 'warning':
            issues.append("DNS configuration has warnings")
            suggestions.append("Review DNS configuration for optimal setup")

        # Check MX records specifically
        mx_status = dns_results.get('mx', {}).get('status')
        if mx_status != 'success':
            issues.append("MX records are not properly configured")
            suggestions.append(f"Add MX record: {MAIL_DOMAIN} MX 10 mail.{MAIL_DOMAIN}")

        # Check A record for mail subdomain
        a_status = dns_results.get('a', {}).get('status')
        if a_status != 'success':
            issues.append("A record for mail subdomain is missing")
            suggestions.append(f"Add A record: mail.{MAIL_DOMAIN} A {SERVER_IP}")

        # Check port connectivity
        port_accessible, port_message = check_port_connectivity()
        if not port_accessible:
            issues.append("SMTP port is not accessible from outside")
            suggestions.append("Check firewall settings and ISP port blocking")

        # Check if running as root (for port 25)
        import os
        if current_port == 25 and os.geteuid() != 0:
            issues.append("Not running as root - cannot bind to port 25")
            suggestions.append("Run the application as root to use port 25")

        # Overall assessment
        if not issues:
            return {
                'status': 'success',
                'message': 'Email receiving appears to be configured correctly',
                'issues': [],
                'suggestions': ['Send a test email to verify functionality']
            }
        else:
            return {
                'status': 'error' if len(issues) > 2 else 'warning',
                'message': f'Found {len(issues)} issue(s) that may prevent email receiving',
                'issues': issues,
                'suggestions': suggestions
            }

    except Exception as e:
        return {
            'status': 'error',
            'message': f'Diagnosis failed: {e}',
            'issues': ['Unable to complete diagnosis'],
            'suggestions': ['Check system logs for errors']
        }

def monitor_smtp_connections(duration_seconds=60):
    """Monitor SMTP connections for debugging purposes."""
    import subprocess
    import time

    print(f"Monitoring SMTP connections for {duration_seconds} seconds...")
    print("Waiting for incoming connections...")

    start_time = time.time()
    while time.time() - start_time < duration_seconds:
        try:
            # Check for active connections to port 25
            result = subprocess.run(['ss', '-tuln', 'sport', '=', ':25'],
                                  capture_output=True, text=True, timeout=5)
            if result.stdout:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                if lines and lines[0]:  # If there are connections
                    print(f"Active SMTP connections: {len(lines)}")
                    for line in lines:
                        print(f"  {line}")

            time.sleep(5)
        except Exception as e:
            print(f"Monitoring error: {e}")
            break

    print("Monitoring completed.")

def send_email_via_mailserver(from_address, to_address, subject, body, smtp_password=None):
    """Sends an email via the integrated mailserver."""
    try:
        # Determine if this is a local or external email
        to_domain = to_address.split('@')[1] if '@' in to_address else ''

        if to_domain in [MAIL_DOMAIN, f'mail.{MAIL_DOMAIN}']:
            # Local email - send via local SMTP server
            return _send_local_email(from_address, to_address, subject, body, smtp_password)
        else:
            # External email - send directly to recipient's MX server
            return _send_external_email(from_address, to_address, subject, body)

    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False, f"Error sending email: {e}"

def _send_local_email(from_address, to_address, subject, body, smtp_password=None):
    """Send email to local address via local SMTP server."""
    try:
        # Create email message
        msg = EmailMessage()
        msg['Subject'] = subject
        msg['From'] = from_address
        msg['To'] = to_address
        msg.set_content(body)

        # Send via local SMTP server
        with smtplib.SMTP(SERVER_IP, SMTP_PORT, timeout=10) as server:
            if smtp_password:
                # Authenticate if password provided
                server.login(from_address, smtp_password)
            server.send_message(msg)

        # Store sent email in database
        store_sent_email(from_address, to_address, subject, body)

        logger.info(f"Local email sent successfully from {from_address} to {to_address}")
        return True, "Local email sent successfully"

    except Exception as e:
        logger.error(f"Error sending local email: {e}")
        return False, f"Error sending local email: {e}"

def _send_external_email(from_address, to_address, subject, body):
    """Send email to external address via recipient's MX server."""
    try:
        # Get recipient domain
        to_domain = to_address.split('@')[1]

        # Lookup MX record for recipient domain
        mx_server = get_smtp_server_for_domain(to_domain)
        if not mx_server:
            return False, f"No MX record found for domain {to_domain}"

        # Create email message with proper headers
        msg = EmailMessage()
        msg['Subject'] = subject
        msg['From'] = from_address
        msg['To'] = to_address
        msg['Message-ID'] = f"<{secrets.token_hex(16)}@{MAIL_DOMAIN}>"
        msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')
        msg.set_content(body)

        # Add authentication headers
        from_domain = from_address.split('@')[1] if '@' in from_address else MAIL_DOMAIN
        add_email_headers(msg, from_domain)

        # Send via recipient's MX server
        with smtplib.SMTP(mx_server, 25, timeout=30) as server:
            # Try STARTTLS if available
            try:
                server.starttls()
                logger.info(f"STARTTLS enabled for {mx_server}")
            except Exception:
                logger.warning(f"STARTTLS not available for {mx_server}")

            # Send the message
            server.send_message(msg)

        # Store sent email in database
        store_sent_email(from_address, to_address, subject, body)

        logger.info(f"External email sent successfully from {from_address} to {to_address} via {mx_server}")
        return True, f"External email sent successfully via {mx_server}"

    except Exception as e:
        logger.error(f"Error sending external email: {e}")
        return False, f"Error sending external email: {e}"

def send_email(from_address, to_address, subject, body, body_html=None, reply_to_message_id=None):
    """Send email with optional reply functionality"""
    try:
        # Determine if this is a local or external email
        to_domain = to_address.split('@')[1] if '@' in to_address else ''

        if to_domain == MAIL_DOMAIN:
            # Local email
            success = store_received_email(to_address, from_address, subject, body, body_html)
            if success:
                # Also store in sender's sent folder
                store_sent_email(from_address, to_address, subject, body, body_html)
                return True, "Email sent successfully (local)"
            else:
                return False, "Failed to deliver local email"
        else:
            # External email
            return _send_external_email(from_address, to_address, subject, body)

    except Exception as e:
        logger.error(f"Error in send_email: {e}")
        return False, f"Error sending email: {e}"
