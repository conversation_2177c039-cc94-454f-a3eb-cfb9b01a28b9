{"base.html": {"site_title": "lxnd.cloud", "nav_dashboard": "Dashboard", "nav_files": "Files", "nav_email": "Email", "nav_api_docs": "API Docs", "nav_admin": "Admin", "nav_home": "Home", "nav_profile": "Profile", "nav_logout": "Logout", "nav_login": "<PERSON><PERSON>", "nav_register": "Register", "flash_info_sr": "Info"}, "landing.html": {"page_title": "lxnd.cloud - Your All-in-One Solution", "hero_title": "lxnd.cloud", "hero_subtitle": "Your all-in-one platform for software licensing, file sharing & hosting, and more.", "btn_dashboard": "Dashboard", "btn_get_started": "Get Started", "btn_login": "<PERSON><PERSON>", "services_title": "Our Services", "services_subtitle": "Powerful tools to help you manage your digital assets", "license_manager_title": "License Manager", "license_manager_desc": "Generate and manage software licenses with ease. Create projects, issue licenses, and validate them through our API.", "license_manager_features": ["• Secure license key generation", "• Expiration date management", "• API for license validation", "• Project organization"], "btn_manage_licenses": "Manage Licenses", "file_uploader_title": "File Uploader", "file_uploader_desc": "Upload, store, and share files securely. Our multithreaded upload system ensures fast and reliable transfers.", "file_uploader_features": ["• Fast multithreaded uploads", "• Public or private file sharing", "• Download tracking", "• Support for multiple file types"], "btn_manage_files": "Manage Files", "api_title": "Simple API Integration", "api_subtitle": "Our REST API makes it easy to integrate license checking into your applications.", "btn_view_api_docs": "View Full API Docs", "btn_try_now": "Try It Now", "api_example_title": "Check License Validity", "cta_title": "Ready to Get Started?", "cta_subtitle": "Join thousands of developers who trust our platform.", "btn_create_account": "Create Free Account", "footer_text": "© 2025 lxnd.cloud. Built with Flask and Tailwind CSS."}, "login.html": {"page_title": "Login - lxnd.cloud", "form_title": "<PERSON><PERSON>", "no_account_text": "Don't have an account?", "register_link_text": "Register here", "back_home_text": "← Back to Home"}, "register.html": {"page_title": "Register - lxnd.cloud", "form_title": "Register", "have_account_text": "Already have an account?", "login_link_text": "Login here", "back_home_text": "← Back to Home"}, "dashboard.html": {"page_title": "Dashboard - lxnd.cloud", "welcome_text": "Welcome,", "projects_label": "Projects", "storage_label": "Storage", "license_keys_label": "License Keys", "files_uploaded_label": "Files Uploaded", "unlimited_text": "Unlimited", "used_text": "used", "used_across_projects": "used across all projects", "no_projects_text": "No projects yet", "total_files_text": "Total files in your account", "usage_warning_title": "Usage Warning", "license_manager_title": "License Manager", "license_manager_desc": "Manage your software licenses and projects.", "your_projects_title": "Your Projects", "licenses_text": "licenses", "active_status": "Active", "no_projects_desc": "You don't have any projects yet.", "btn_create_project": "Create Project", "btn_api_docs": "API Docs", "file_uploader_title": "File Uploader", "file_uploader_desc": "Upload, store, and share your files securely.", "quick_upload_title": "Quick Upload", "btn_manage_files": "Manage Files", "email_account_title": "<PERSON><PERSON> Account", "btn_create_email": "Create <PERSON><PERSON> Account", "btn_open_email": "Open Email", "no_display_name": "No display name", "storage_used_label": "Storage Used", "no_email_title": "No Email Account", "no_email_desc": "Create your free @lxnd.cloud email account", "account_info_title": "Account Information", "username_label": "Username:", "email_label": "Email:", "member_since_label": "Member Since:", "api_token_label": "API Token:", "btn_reset": "Reset"}, "404.html": {"page_title": "404 - lxnd.cloud", "heading": "404 - Page Not Found", "message": "The page you are looking for does not exist. Please check the URL and try again.", "back_home_text": "← Back to Home"}, "create_project.html": {"page_title": "Create Project - lxnd.cloud", "form_title": "Create New Project", "btn_cancel": "Cancel"}, "profile.html": {"page_title": "Profile - lxnd.cloud", "form_title": "User Profile", "account_info_title": "Account Information", "username_label": "Username:", "email_label": "Email:", "member_since_label": "Member since:", "api_config_title": "API Configuration", "api_token_label": "API Token:", "btn_copy": "Copy", "btn_reset_token": "Reset <PERSON>ken", "reset_confirm": "Are you sure? This will invalidate your current API token.", "api_access_title": "API Access", "api_access_desc": "Use your API token to access the License Management API. For detailed documentation and examples, </br>visit our", "api_docs_link": "API Documentation", "security_note_title": "Security Note", "security_note_desc": "Keep your API token secure and never expose it in client-side code. The token provides full access to your projects and licenses.", "copy_success": "API token copied to clipboard!"}, "api_docs.html": {"page_title": "API Documentation - Lxnd.cloud", "sidebar_title": "Documentation", "nav_overview": "Overview", "nav_authentication": "Authentication", "nav_license_api": "License API", "nav_license_check": "Check License", "nav_license_add": "Add License", "nav_file_api": "File Upload API", "nav_files_list": "List Files", "nav_files_upload": "Upload File", "nav_files_get": "Get File Info", "nav_files_delete": "Delete File", "nav_files_toggle": "Toggle Visibility", "nav_errors": "Erro<PERSON>", "main_title": "API Documentation", "overview_title": "Overview", "overview_desc": "The lxnd.cloud API provides endpoints for managing software licenses and file uploads. All API endpoints return JSON responses.", "license_api_title": "License API", "license_api_desc": "Validate and manage software licenses", "file_api_title": "File Upload API", "file_api_desc": "Upload, manage, and download files", "content_types_title": "Content Types", "auth_title": "Authentication", "auth_desc": "API endpoints use different authentication methods depending on the API. You can get your API token from your", "profile_link": "profile page", "auth_desc_suffix": "after creating an account.", "license_auth_title": "License API", "license_auth_desc": "Uses URL path authentication for simplicity", "file_auth_title": "File Upload API", "file_auth_desc": "Uses Bearer token in Authorization header", "license_section_title": "License API", "license_section_desc": "Endpoints for validating and managing software licenses.", "check_license_title": "Check License", "check_license_desc": "Validates a license key for a specific project and returns its status.", "description_title": "Description", "parameters_title": "Parameters", "response_title": "Response", "examples_title": "Examples", "add_license_title": "Add License", "add_license_desc": "Adds a new license key to a project. Requires authentication.", "request_body_title": "Request Body (Optional)", "file_section_title": "File Upload API", "file_section_desc": "Endpoints for uploading, managing, and downloading files.", "list_files_title": "List Files", "list_files_desc": "Retrieves all files for the authenticated user.", "headers_title": "Headers", "upload_file_title": "Upload File", "upload_file_desc": "Uploads a file to the user's account.", "form_data_title": "Form Data", "get_file_title": "Get File Info", "get_file_desc": "Retrieves information about a specific file.", "delete_file_title": "Delete File", "delete_file_desc": "Deletes a file from the user's account.", "toggle_visibility_title": "Toggle File Visibility", "toggle_visibility_desc": "Toggles a file between public and private visibility.", "errors_title": "Erro<PERSON>", "status_codes_title": "HTTP Status Codes", "error_format_title": "Error Response Format", "common_errors_title": "Common Errors", "help_title": "Need Help?", "help_desc": "If you need assistance with the API, please check our examples or create an account to test the endpoints in your", "help_link": "profile page"}, "admin_dashboard.html": {"page_title": "Admin Dashboard - MultiTools", "main_title": "Admin Dashboard", "main_subtitle": "Manage users and system settings", "total_users_label": "Total Users", "admins_label": "Admins", "moderators_label": "Moderators", "banned_users_label": "Banned Users", "total_projects_label": "Total Projects", "total_storage_label": "Total Storage Used", "total_files_label": "Total Files", "discord_bot_title": "Discord <PERSON>", "discord_bot_desc": "Configure Discord bot appearance, status, and behavior", "embed_color_label": "Embed Color", "bot_status_label": "Bot Status", "activity_label": "Activity", "online_status": "Online", "watching_activity": "Watching LXND License System", "btn_bot_settings": "Bot Settings & Control", "email_service_title": "Email Service", "email_service_desc": "lxnd.cloud mail system", "domain_label": "Domain:", "default_storage_label": "Default Storage:", "status_label": "Status:", "active_status": "Active", "btn_email_management": "Email Management", "system_info_title": "System Information", "database_label": "Database", "connected_status": "Connected", "discord_bot_label": "<PERSON><PERSON>", "available_status": "Available", "api_status_label": "API Status", "btn_view_api_docs": "View API Documentation", "user_management_title": "User Management", "search_placeholder": "Search users...", "filter_all": "All Users", "filter_admin": "Admins", "filter_moderator": "Moderators", "filter_banned": "Banned", "filter_regular": "Regular Users", "btn_bulk_actions": "Bulk Actions", "selected_text": "Selected:", "users_text": "users", "select_action": "Select Action", "action_ban": "Ban Users", "action_unban": "Unban Users", "action_make_admin": "Make Admin", "action_remove_admin": "Remove <PERSON>", "action_delete": "Delete Users", "btn_select_all": "Select All", "btn_clear": "Clear", "btn_execute": "Execute", "table_user": "User", "table_role": "Role", "table_status": "Status", "table_joined": "Joined", "table_projects_usage": "Projects Usage", "table_storage_usage": "Storage Usage", "table_actions": "Actions", "role_admin": "Admin", "role_moderator": "Moderator", "role_user": "User", "status_banned": "Banned", "status_active": "Active", "btn_manage": "Manage", "btn_cancel_bulk": "Cancel"}, "email_compose.html": {"page_title": "Compose Email - lxnd.cloud", "form_title": "Compose Email", "from_label": "From:", "to_label": "To:", "cc_label": "CC:", "bcc_label": "BCC:", "subject_label": "Subject:", "body_label": "Message:", "attachments_label": "Attachments:", "btn_send": "Send Email", "btn_save_draft": "Save Draft", "btn_cancel": "Cancel", "to_placeholder": "<EMAIL>", "cc_placeholder": "<EMAIL> (optional)", "bcc_placeholder": "<EMAIL> (optional)", "subject_placeholder": "Enter subject", "body_placeholder": "Type your message here...", "select_account": "Select account", "email_sent_success": "<PERSON><PERSON> sent successfully!", "email_send_failed": "Failed to send email:", "invalid_sender": "Invalid sender account", "file_size_limit": "File size must be less than 25MB", "attachment_error": "Error processing attachment:"}, "email_reply.html": {"reply_email": "Reply to Email", "back_to_inbox": "Back to Inbox", "replying_to": "Replying to:", "from": "From", "subject": "Subject", "date": "Date", "no_subject": "(no subject)", "unknown": "Unknown", "from_account": "From Account", "to": "To", "message": "Message", "send_reply": "Send Reply", "cancel": "Cancel"}, "email_inbox.html": {"page_title": "Inbox - lxnd.cloud", "inbox_title": "Inbox", "sent_title": "<PERSON><PERSON>", "compose_title": "Compose", "settings_title": "Settings", "btn_compose": "Compose", "btn_refresh": "Refresh", "btn_mark_read": "<PERSON> <PERSON>", "btn_mark_unread": "<PERSON> as Unread", "btn_delete": "Delete", "btn_archive": "Archive", "table_from": "From", "table_subject": "Subject", "table_date": "Date", "table_size": "Size", "no_emails": "No emails in this folder", "unread_badge": "Unread", "attachment_icon": "📎", "select_all": "Select All", "selected_count": "selected", "bulk_actions": "Bulk Actions", "search_placeholder": "Search emails...", "filter_all": "All", "filter_unread": "Unread", "filter_read": "Read", "filter_attachments": "With Attachments"}, "email_sent.html": {"page_title": "Sent - lxnd.cloud", "sent_title": "Sent Em<PERSON>", "table_to": "To", "table_subject": "Subject", "table_date": "<PERSON><PERSON>", "table_size": "Size", "no_sent_emails": "No sent emails", "btn_compose": "Compose New", "btn_back_inbox": "Back to Inbox"}, "email_settings.html": {"page_title": "Email Settings - lxnd.cloud", "settings_title": "Email Account <PERSON>s", "account_info_title": "Account Information", "email_address_label": "Email Address:", "display_name_label": "Display Name:", "storage_used_label": "Storage Used:", "storage_quota_label": "Storage Quota:", "password_section_title": "Password Settings", "current_password_label": "Current Password:", "new_password_label": "New Password:", "confirm_password_label": "Confirm Password:", "btn_change_password": "Change Password", "imap_settings_title": "IMAP/POP3 Settings", "imap_server_label": "IMAP Server:", "imap_port_label": "IMAP Port:", "imap_encryption_label": "Encryption:", "smtp_server_label": "SMTP Server:", "smtp_port_label": "SMTP Port:", "smtp_encryption_label": "SMTP Encryption:", "username_label": "Username:", "password_note": "Use your email account password", "ssl_tls": "SSL/TLS", "starttls": "STARTTLS", "setup_instructions_title": "Email Client Setup Instructions", "thunderbird_title": "Mozilla Thunderbird", "outlook_title": "Microsoft Outlook", "apple_mail_title": "Apple Mail", "android_title": "Android Mail", "ios_title": "iOS Mail", "dns_testing_title": "DNS Testing", "btn_test_dns": "Test DNS Records", "dns_status_good": "DNS records are properly configured", "dns_status_warning": "DNS configuration has warnings", "dns_status_error": "DNS configuration has errors", "mx_record_label": "MX Record:", "a_record_label": "A Record:", "reverse_dns_label": "Reverse DNS:", "spf_record_label": "SPF Record:", "dkim_record_label": "DKIM Record:", "dmarc_record_label": "DMARC Record:"}, "mailserver_status": {"smtp_running": "SMTP Server Running", "smtp_stopped": "SMTP Server Stopped", "imap_running": "IMAP Server Running", "imap_stopped": "IMAP Server Stopped", "port_label": "Port:", "host_label": "Host:", "domain_label": "Domain:", "tls_enabled": "TLS Enabled", "tls_disabled": "TLS Disabled", "auth_required": "Authentication Required", "auth_optional": "Authentication Optional", "status_healthy": "Healthy", "status_warning": "Warning", "status_error": "Error", "btn_start": "Start", "btn_stop": "Stop", "btn_restart": "<PERSON><PERSON>", "btn_test": "Test Connection"}, "email_notifications": {"welcome_subject": "Welcome to lxnd.cloud!", "welcome_body": "Welcome to your new lxnd.cloud email account! Your account has been successfully created.", "password_changed_subject": "Password Changed", "password_changed_body": "Your email account password has been successfully changed.", "account_created_subject": "New Email Account Created", "account_created_body": "A new email account has been created: {email_address}", "storage_warning_subject": "Storage Quota Warning", "storage_warning_body": "Your email storage is {percentage}% full. Please consider cleaning up old emails.", "storage_full_subject": "Storage Quota Exceeded", "storage_full_body": "Your email storage quota has been exceeded. New emails may not be received until you free up space."}, "javascript_messages": {"please_select_action": "Please select an action", "please_select_users": "Please select at least one user", "confirm_ban": "Are you sure you want to ban", "confirm_unban": "Are you sure you want to unban", "confirm_make_admin": "Are you sure you want to make admin", "confirm_remove_admin": "Are you sure you want to remove admin from", "confirm_delete": "Are you sure you want to DELETE", "confirm_delete_warning": "This action cannot be undone!", "users_suffix": "user(s)?", "email_copied": "Email address copied to clipboard!", "password_copied": "Password copied to clipboard!", "dns_test_started": "Testing DNS records...", "dns_test_completed": "DNS test completed", "email_marked_read": "Email marked as read", "email_marked_unread": "Email marked as unread", "email_deleted": "Email deleted", "emails_deleted": "emails deleted", "confirm_delete_email": "Are you sure you want to delete this email?", "confirm_delete_emails": "Are you sure you want to delete the selected emails?", "no_emails_selected": "No emails selected", "connection_test_success": "Connection test successful", "connection_test_failed": "Connection test failed:", "server_starting": "Starting server...", "server_stopping": "Stopping server...", "server_restarting": "Restarting server..."}}