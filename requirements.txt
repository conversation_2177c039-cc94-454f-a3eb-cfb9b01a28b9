# Flask and Web Framework Dependencies
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Login==0.6.3
Flask-WTF==1.2.1
WTForms==3.1.1
Werkzeug==3.0.1

# Database
SQLAlchemy==2.0.23
PyMySQL==1.1.0

# Security and Authentication
bcrypt==4.1.2

# Discord Bot Dependencies
discord.py==2.3.2
python-dotenv==1.0.0

# System Monitoring
psutil==5.9.6

# Email and SMTP
aiosmtpd==1.4.4.post2
dnspython==2.4.2
cryptography==41.0.7
# Note: email, smtplib, imaplib are part of Python standard library

# File Upload and Processing
# Note: Most file handling uses standard library modules

# Async Support (for Discord bot)
asyncio
# Note: asyncio is part of Python standard library since 3.4

# JSON and Data Processing
# Note: json, uuid, csv, io are part of Python standard library

# Date and Time
# Note: datetime is part of Python standard library

# Threading and Concurrency
# Note: threading, concurrent.futures are part of Python standard library

# Network and Socket Programming
# Note: socket, socketserver, smtpd are part of Python standard library

# Subprocess and System
# Note: subprocess, sys, os are part of Python standard library

# Logging
# Note: logging is part of Python standard library

# Base64 and Encoding
# Note: base64 is part of Python standard library

# String and Text Processing
# Note: string, secrets are part of Python standard library

# Functional Programming
# Note: functools is part of Python standard library

# Development and Testing (optional)
# pytest==7.4.3
# pytest-flask==1.3.0

# Production Server (optional)
# gunicorn==21.2.0
# waitress==2.1.2
