#!/usr/bin/env python3
"""
MySQL Database Manager for LXND Application
Handles all MySQL operations and provides admin interface
"""

try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    try:
        import pymysql
        MYSQL_AVAILABLE = True
    except ImportError:
        MYSQL_AVAILABLE = False
import json
import os
from datetime import datetime
import logging

class MySQLManager:
    def __init__(self, credentials_file='/root/lxnd/mysql_credentials.json'):
        """Initialize MySQL manager with credentials"""
        self.credentials = self.load_credentials(credentials_file)
        self.connection = None
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging for MySQL operations"""
        logging.basicConfig(
            filename='/root/lxnd/mysql.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def load_credentials(self, file_path):
        """Load MySQL credentials from JSON file"""
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise Exception(f"Credentials file not found: {file_path}")
        except json.JSONDecodeError:
            raise Exception(f"Invalid JSON in credentials file: {file_path}")
    
    def connect(self, database=None, use_app_user=True):
        """Connect to MySQL database"""
        try:
            mysql_config = self.credentials['mysql']

            config = {
                'host': mysql_config['host'],
                'port': mysql_config['port'],
                'user': mysql_config['app_user'] if use_app_user else mysql_config['root_user'],
                'password': mysql_config['app_password'] if use_app_user else mysql_config['root_password'],
                'autocommit': True,
                'charset': 'utf8mb4'
            }

            if database:
                config['database'] = database

            # Try mysql.connector first, then pymysql
            if 'mysql.connector' in globals():
                self.connection = mysql.connector.connect(**config)
            else:
                # Use pymysql
                import pymysql
                self.connection = pymysql.connect(**config)

            self.logger.info(f"Connected to MySQL database: {database or 'none'}")
            return True

        except Exception as e:
            self.logger.error(f"MySQL connection error: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MySQL"""
        if self.connection:
            try:
                if hasattr(self.connection, 'is_connected') and self.connection.is_connected():
                    self.connection.close()
                elif hasattr(self.connection, 'open') and self.connection.open:
                    self.connection.close()
                else:
                    self.connection.close()
                self.logger.info("MySQL connection closed")
            except:
                pass
    
    def execute_query(self, query, params=None, fetch=False):
        """Execute SQL query"""
        try:
            # Handle different cursor types
            if hasattr(self.connection, 'cursor'):
                if 'pymysql' in str(type(self.connection)):
                    import pymysql.cursors
                    cursor = self.connection.cursor(pymysql.cursors.DictCursor)
                else:
                    cursor = self.connection.cursor(dictionary=True)
            else:
                cursor = self.connection.cursor()

            cursor.execute(query, params or ())

            if fetch:
                result = cursor.fetchall()
                cursor.close()
                return result
            else:
                self.connection.commit()
                cursor.close()
                return True

        except Exception as e:
            self.logger.error(f"Query execution error: {e}")
            return None
    
    def create_tables(self):
        """Create all necessary tables"""
        tables = {
            'users': """
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(80) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    api_token VARCHAR(64) UNIQUE NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_admin BOOLEAN DEFAULT FALSE,
                    is_moderator BOOLEAN DEFAULT FALSE,
                    is_banned BOOLEAN DEFAULT FALSE,
                    is_lux BOOLEAN DEFAULT FALSE,
                    lux_expires_at TIMESTAMP NULL,
                    upload_limit_mb INT DEFAULT 15360,
                    max_projects INT DEFAULT 3,
                    max_keys_per_project INT DEFAULT 100,
                    discord_id VARCHAR(20) UNIQUE NULL,
                    discord_username VARCHAR(100) NULL,
                    discord_avatar VARCHAR(255) NULL,
                    discord_linked_at TIMESTAMP NULL,
                    last_login TIMESTAMP NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """,
            'projects': """
                CREATE TABLE IF NOT EXISTS projects (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """,
            'files': """
                CREATE TABLE IF NOT EXISTS files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    filename VARCHAR(255) NOT NULL,
                    original_filename VARCHAR(255) NOT NULL,
                    file_path VARCHAR(500) NOT NULL,
                    file_size BIGINT NOT NULL,
                    mime_type VARCHAR(100),
                    user_id INT NOT NULL,
                    project_id INT,
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
                )
            """,
            'email_accounts': """
                CREATE TABLE IF NOT EXISTS email_accounts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email_address VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    quota_mb INT DEFAULT 1000,
                    used_mb INT DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """,
            'emails': """
                CREATE TABLE IF NOT EXISTS emails (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    message_id VARCHAR(255) UNIQUE NOT NULL,
                    email_account_id INT NOT NULL,
                    sender VARCHAR(255) NOT NULL,
                    recipient VARCHAR(255) NOT NULL,
                    subject TEXT,
                    body_text TEXT,
                    body_html TEXT,
                    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_read BOOLEAN DEFAULT FALSE,
                    is_sent BOOLEAN DEFAULT FALSE,
                    has_attachments BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (email_account_id) REFERENCES email_accounts(id) ON DELETE CASCADE
                )
            """,
            'email_attachments': """
                CREATE TABLE IF NOT EXISTS email_attachments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email_id INT NOT NULL,
                    filename VARCHAR(255) NOT NULL,
                    file_path VARCHAR(500) NOT NULL,
                    file_size BIGINT NOT NULL,
                    mime_type VARCHAR(100),
                    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE
                )
            """,
            'smtp_servers': """
                CREATE TABLE IF NOT EXISTS smtp_servers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    host VARCHAR(255) NOT NULL,
                    port INT NOT NULL,
                    username VARCHAR(255),
                    password_hash VARCHAR(255),
                    use_tls BOOLEAN DEFAULT TRUE,
                    use_ssl BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'notification_templates': """
                CREATE TABLE IF NOT EXISTS notification_templates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    body_html TEXT NOT NULL,
                    body_text TEXT,
                    placeholders JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """
        }
        
        # Connect to main database
        if not self.connect('lxnd_main'):
            return False
        
        success = True
        for table_name, query in tables.items():
            if not self.execute_query(query):
                self.logger.error(f"Failed to create table: {table_name}")
                success = False
            else:
                self.logger.info(f"Table created/verified: {table_name}")
        
        return success
    
    def get_database_stats(self):
        """Get database statistics"""
        if not self.connect('lxnd_main'):
            return None
        
        stats = {}
        
        # Table row counts - check if tables exist first
        tables = ['users', 'projects', 'files', 'email_accounts', 'emails', 'smtp_servers', 'notification_templates']
        for table in tables:
            try:
                result = self.execute_query(f"SELECT COUNT(*) as count FROM {table}", fetch=True)
                stats[table] = result[0]['count'] if result else 0
            except:
                # Table might not exist, try alternative names
                alt_names = {
                    'users': 'user',
                    'projects': 'project',
                    'files': 'file_upload',
                    'email_accounts': 'email_account',
                    'emails': 'email_message'
                }
                if table in alt_names:
                    try:
                        result = self.execute_query(f"SELECT COUNT(*) as count FROM {alt_names[table]}", fetch=True)
                        stats[table] = result[0]['count'] if result else 0
                    except:
                        stats[table] = 0
                else:
                    stats[table] = 0
        
        # Database size
        size_query = """
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = 'lxnd_main'
        """
        result = self.execute_query(size_query, fetch=True)
        stats['database_size_mb'] = result[0]['size_mb'] if result else 0
        
        return stats
    
    def backup_database(self, backup_path='/root/lxnd/backups'):
        """Create database backup"""
        import subprocess
        from datetime import datetime
        
        os.makedirs(backup_path, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"{backup_path}/lxnd_backup_{timestamp}.sql"
        
        mysql_config = self.credentials['mysql']
        
        cmd = [
            'mysqldump',
            f'--user={mysql_config["root_user"]}',
            f'--password={mysql_config["root_password"]}',
            '--single-transaction',
            '--routines',
            '--triggers',
            'lxnd_main'
        ]
        
        try:
            with open(backup_file, 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
            
            self.logger.info(f"Database backup created: {backup_file}")
            return backup_file
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Backup failed: {e}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

# Convenience functions
def get_mysql_manager():
    """Get MySQL manager instance"""
    return MySQLManager()

def init_database():
    """Initialize database with all tables"""
    with get_mysql_manager() as db:
        return db.create_tables()

if __name__ == "__main__":
    # Initialize database when run directly
    if init_database():
        print("Database initialized successfully")
    else:
        print("Database initialization failed")
