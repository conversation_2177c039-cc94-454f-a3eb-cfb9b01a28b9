-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 18, 2025 at 10:50 PM
-- Server version: 10.5.29-MariaDB-0+deb11u1
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `lxnd_main`
--

-- --------------------------------------------------------

--
-- Table structure for table `bot_settings`
--

CREATE TABLE `bot_settings` (
  `id` int(11) NOT NULL,
  `embed_color` varchar(7) DEFAULT NULL,
  `global_footer` varchar(255) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `activity_type` varchar(20) DEFAULT NULL,
  `activity_text` varchar(128) DEFAULT NULL,
  `activity_url` varchar(255) DEFAULT NULL,
  `bot_name` varchar(100) DEFAULT NULL,
  `bot_avatar_url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bot_settings`
--

INSERT INTO `bot_settings` (`id`, `embed_color`, `global_footer`, `status`, `activity_type`, `activity_text`, `activity_url`, `bot_name`, `bot_avatar_url`, `created_at`, `updated_at`) VALUES
(1, '#00ff00', 'LXND License Management System', 'online', 'watching', 'LXND License System', NULL, NULL, NULL, '2025-07-18 00:05:07', '2025-07-18 00:05:07');

-- --------------------------------------------------------

--
-- Table structure for table `discord_role_updates`
--

CREATE TABLE `discord_role_updates` (
  `id` int(11) NOT NULL,
  `discord_id` varchar(20) NOT NULL,
  `role_type` varchar(50) NOT NULL,
  `action` varchar(10) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `processed` tinyint(1) DEFAULT 0,
  `processed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `emails`
--

CREATE TABLE `emails` (
  `id` int(11) NOT NULL,
  `message_id` varchar(255) NOT NULL,
  `email_account_id` int(11) NOT NULL,
  `sender` varchar(255) NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` text DEFAULT NULL,
  `body_text` text DEFAULT NULL,
  `body_html` text DEFAULT NULL,
  `received_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) DEFAULT 0,
  `is_sent` tinyint(1) DEFAULT 0,
  `has_attachments` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `email_account`
--

CREATE TABLE `email_account` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `email_address` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `smtp_password` text DEFAULT NULL,
  `display_name` varchar(100) DEFAULT NULL,
  `storage_used_mb` int(11) DEFAULT NULL,
  `storage_limit_mb` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_account`
--

INSERT INTO `email_account` (`id`, `user_id`, `email_address`, `password_hash`, `smtp_password`, `display_name`, `storage_used_mb`, `storage_limit_mb`, `is_active`, `is_verified`, `created_at`, `last_login`) VALUES
(1, 2, '<EMAIL>', '$2b$12$xzubBgvMKc4DWIAOPaeGbOR7Boebbmmd7DgBRxTRBiHw70djiIEBm', 'MDMyNGVpc0JlcmNoZW4hISEh', 'Luxend', 0, 10240, 1, 1, '2025-07-16 00:28:33', NULL),
(2, 2, '<EMAIL>', '$2b$12$LDTfYL0fCMU1wd7livYaaOKAHDmL1INOlZnOUsSJ4g.PR/qz0rViC', 'MDMyNGVpc0JlcmNoZW4hISEh', 'Luxend', 0, 10240, 1, 1, '2025-07-16 00:58:28', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `email_accounts`
--

CREATE TABLE `email_accounts` (
  `id` int(11) NOT NULL,
  `email_address` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  `quota_mb` int(11) DEFAULT 1000,
  `used_mb` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `email_attachments`
--

CREATE TABLE `email_attachments` (
  `id` int(11) NOT NULL,
  `email_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `email_message`
--

CREATE TABLE `email_message` (
  `id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `message_id` varchar(255) NOT NULL,
  `subject` varchar(500) DEFAULT NULL,
  `sender` varchar(255) NOT NULL,
  `recipients` text DEFAULT NULL,
  `cc` text DEFAULT NULL,
  `bcc` text DEFAULT NULL,
  `body_text` text DEFAULT NULL,
  `body_html` text DEFAULT NULL,
  `attachments` text DEFAULT NULL,
  `size_bytes` int(11) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT NULL,
  `is_starred` tinyint(1) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `folder` varchar(50) DEFAULT NULL,
  `sent_at` datetime DEFAULT NULL,
  `received_at` datetime DEFAULT NULL,
  `in_reply_to` varchar(255) DEFAULT NULL,
  `references` text DEFAULT NULL,
  `thread_id` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_message`
--

INSERT INTO `email_message` (`id`, `account_id`, `message_id`, `subject`, `sender`, `recipients`, `cc`, `bcc`, `body_text`, `body_html`, `attachments`, `size_bytes`, `is_read`, `is_starred`, `is_deleted`, `folder`, `sent_at`, `received_at`, `in_reply_to`, `references`, `thread_id`) VALUES
(1, 1, 'test-message-001', 'Test Email from MySQL', '<EMAIL>', '<EMAIL>', NULL, NULL, 'This is a test email to verify MySQL integration.', '<p>This is a test email to verify MySQL integration.</p>', NULL, 100, 0, 0, 0, 'INBOX', NULL, '2025-07-16 02:58:42', NULL, NULL, NULL),
(2, 2, '<<EMAIL>>', 'erterter', '<EMAIL>', '<EMAIL>', NULL, NULL, 'ert', NULL, NULL, 11, 1, NULL, 0, 'SENT', '2025-07-16 03:02:33', '2025-07-16 03:02:33', NULL, NULL, NULL),
(3, 2, '<<EMAIL>>', 'testnigger', '<EMAIL>', '<EMAIL>', NULL, NULL, 'testnigger', NULL, NULL, 20, 1, NULL, 0, 'SENT', '2025-07-16 11:20:27', '2025-07-16 11:20:27', NULL, NULL, NULL),
(4, 2, '<<EMAIL>>', 'rtr', '<EMAIL>', '<EMAIL>', NULL, NULL, 'rtr', NULL, NULL, 6, 1, NULL, 0, 'SENT', '2025-07-17 23:02:06', '2025-07-17 23:02:06', NULL, NULL, NULL),
(5, 1, '<<EMAIL>>', 'Test Email MySQL', '<EMAIL>', '<EMAIL>', NULL, NULL, 'This is a test email to verify MySQL integration works.', '<p>This is a test email to verify MySQL integration works.</p>', NULL, 133, 0, NULL, 0, 'INBOX', NULL, '2025-07-17 23:07:09', NULL, NULL, NULL),
(6, 2, '<<EMAIL>>', 'Re: rtr', 'vrxp <<EMAIL>>', '<EMAIL>', NULL, NULL, 'Received: by mail-oo1-f45.google.com with SMTP id 006d021491bc7-60d666804ebso1250862eaf.1\r\n        for <<EMAIL>>; Thu, 17 Jul 2025 14:08:30 -0700 (PDT)\r\nDKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=gmail.com; s=20230601; t=1752786510; x=1753391310; darn=lxnd.cloud;\r\n        h=to:subject:message-id:date:from:in-reply-to:references:mime-version\r\n         :from:to:cc:subject:date:message-id:reply-to;\r\n        bh=TrAGsFACsI0kQqyWNQyrZBcG2z1tZ4bIWxABTJUPGWk=;\r\n        b=miQ0DBx1U8crOkxcTBsnNUWmLg0RmklhWPjB/wu+27eR6mfRKzgAKnmr75TG8WCeIy\r\n         Gl+elvexNobf2QjL6CRB5iDmFGVdAtqALPOXL3MILIlLZbvbGA72ZwJjyNT1jGFoFNUN\r\n         5Bb+ogkOipsVxgXAzfK1midRso8lNs8H6G2oWudk+j4ya8u7+HpU+WtW/vdUabb/ovlS\r\n         n2MhCKtaITtT1GHUYwnssl29O/pdlummChNkmI9eWs+gnt6uwxNGEoKwRqpNySW44R/k\r\n         5KDiQgCorYKJwaxXf/we780Tjjfc5snsWMQguYPjGGXECsVlC3uy0Kx79fiZ1522odvJ\r\n         +sXQ==\r\nX-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=1e100.net; s=20230601; t=1752786510; x=1753391310;\r\n        h=to:subject:message-id:date:from:in-reply-to:references:mime-version\r\n         :x-gm-message-state:from:to:cc:subject:date:message-id:reply-to;\r\n        bh=TrAGsFACsI0kQqyWNQyrZBcG2z1tZ4bIWxABTJUPGWk=;\r\n        b=OGvdRQ98O7E4UwUXTt7xpZT/rUTsWmIDoH/RiH9ebHATCN3gHtLqoj6Do0W/afWE2i\r\n         6UJLZcqYxuer2FDJtnMfL9iLBM+aWtIBrF6RzyDSncRYL8tNKptfT/4U6i6V11jRtsmF\r\n         JJMVwY8K0iShqqDO6mMgp3JsKmRQGL732nE7RE//3IyAOn3/0JMw6lLNIJ1m+1dDzME4\r\n         WbxWR0FRsjj0Y77Eo9YrFnYIsoCcK2nuQWvwNZVYu/UEk0s/0ZLuUwMqKR03t8KOWrUZ\r\n         b+REd/09ti1IxlP+sWLg6WGI8ov/B4JiemZvymFbOq3K0HRS5Z/FCO+KFmfk6dyWi0AY\r\n         /y0w==\r\nX-Gm-Message-State: AOJu0YwHL7WpTwDkuDGKgSXrRRgA9wPHM8c4gjbw+VR1Jn2Hnc3BRNqh\r\n	wIUS78PXekvFq7E33mDThn3OcEdEh1OAYtM9b3p5M268hNtAf0hy9KOzIFxn2V+r2TkvnvWdXNb\r\n	37PdNBmxkkTF7B/OcddQowuzVp8aOnwr5d9Cu\r\nX-Gm-Gg: ASbGncsac35dU5y+hkM1oeaeU/IjhZoFCtbxuZyEL5+5fivkGAgpD/4xSVc0tMv7+3d\r\n	lwBwpEUkN4+UjbbcfOZiNRkdDVJp8JUdcV4DoaUWs5aBfeGlY/Saxx/jyYTPJRrrEwt1hrhW7xT\r\n	VS/H5EhfASXAClzDG3dzHlTX9+T7B5BFzzwD6cjcYTs+jJ6BDdd6sZESuFI9h/YBT8Ml6ob3Tdn\r\n	4tPkw4WiZN3+GIceVbJ+SB0qQul6U3pKWoYJqA=\r\nX-Google-Smtp-Source: AGHT+IENITzj9/IhDsVRzStzF4eDGxNpHJ6rQ9EAA9ZeCUlN6KgshL0BLNaU6lN90WlepgcVsIm32E6jsIW/ttVNNiE=\r\nX-Received: by 2002:a05:6820:2702:b0:611:3e54:8d0a with SMTP id\r\n 006d021491bc7-615acc319a6mr3360603eaf.1.1752786509833; Thu, 17 Jul 2025\r\n 14:08:29 -0700 (PDT)\r\nMIME-Version: 1.0\r\nReferences: <<EMAIL>> <CAMAUxQEdoSoAdNV+DyZWdCpED46Gf_rAYGYY2KYd8A4t=<EMAIL>>\r\nIn-Reply-To: <CAMAUxQEdoSoAdNV+DyZWdCpED46Gf_rAYGYY2KYd8A4t=<EMAIL>>\r\nFrom: vrxp <<EMAIL>>\r\nDate: Thu, 17 Jul 2025 23:08:19 +0200\r\nX-Gm-Features: Ac12FXwJsy2plcgCUf4ag7uOo992hrvD9rJcFDWbnhkhhRxAzcXs2M9heyXtxmM\r\nMessage-ID: <CAMAUxQGi6tEaLGhg1YkK=<EMAIL>>\r\nSubject: Re: rtr\r\nTo: <EMAIL>\r\nContent-Type: multipart/alternative; boundary=\"00000000000049287b063a266946\"\r\n\r\n--00000000000049287b063a266946\r\nContent-Type: text/plain; charset=\"UTF-8\"\r\n\r\nOn Thu, 17 Jul 2025 at 23:02, vrxp <<EMAIL>> wrote:\r\n\r\n>\r\n>\r\n> On Thu, 17 Jul 2025 at 23:02, <<EMAIL>> wrote:\r\n>\r\n>> rtr\r\n>>\r\n>\r\n\r\n--00000000000049287b063a266946\r\nContent-Type: text/html; charset=\"UTF-8\"\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<div dir=3D\"ltr\"><br></div><br><div class=3D\"gmail_quote gmail_quote_contai=\r\nner\"><div dir=3D\"ltr\" class=3D\"gmail_attr\">On Thu, 17 Jul 2025 at 23:02, vr=\r\nxp &lt;<a href=3D\"mailto:<EMAIL>\"><EMAIL></a>&gt; wro=\r\nte:<br></div><blockquote class=3D\"gmail_quote\" style=3D\"margin:0px 0px 0px =\r\n0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=3D\"=\r\nltr\"><br></div><br><div class=3D\"gmail_quote\"><div dir=3D\"ltr\" class=3D\"gma=\r\nil_attr\">On Thu, 17 Jul 2025 at 23:02, &lt;<EMAIL>&gt; wrote:<br>=\r\n</div><blockquote class=3D\"gmail_quote\" style=3D\"margin:0px 0px 0px 0.8ex;b=\r\norder-left:1px solid rgb(204,204,204);padding-left:1ex\">rtr<br>\r\n</blockquote></div>\r\n</blockquote></div>\r\n\r\n--00000000000049287b063a266946--\r\n', NULL, NULL, 4109, 1, NULL, 0, 'INBOX', NULL, '2025-07-17 23:08:31', NULL, NULL, NULL),
(7, 2, '<<EMAIL>>', 'ewr', '<EMAIL>', '<EMAIL>', NULL, NULL, 'twer', NULL, NULL, 7, 1, NULL, 0, 'SENT', '2025-07-17 23:11:14', '2025-07-17 23:11:14', NULL, NULL, NULL),
(8, 2, '<<EMAIL>>', 'test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'test', NULL, NULL, 8, 1, NULL, 0, 'SENT', '2025-07-17 23:23:32', '2025-07-17 23:23:32', NULL, NULL, NULL),
(9, 2, '<<EMAIL>>', 'Re: test', 'vrxp <<EMAIL>>', '<EMAIL>', NULL, NULL, 'Received: by mail-oi1-f170.google.com with SMTP id 5614622812f47-41b309ce799so1075245b6e.2\r\n        for <<EMAIL>>; Thu, 17 Jul 2025 14:24:04 -0700 (PDT)\r\nDKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=gmail.com; s=20230601; t=1752787444; x=1753392244; darn=lxnd.cloud;\r\n        h=to:subject:message-id:date:from:in-reply-to:references:mime-version\r\n         :from:to:cc:subject:date:message-id:reply-to;\r\n        bh=PMN1jRBiULqYWdk8ZIZsjc6u/OUEjdAN3ONuzlG+W7w=;\r\n        b=mYP+RHaFejkx+SnlaSY9tFZ2UOGonJB9Snv1AwJvivMAeAqXTVfUpfcvHdGz45pWr4\r\n         OTkZthEfYO8SC4g9dhLvG/EfR03Uek/FkzlgDc/Y7fTwcgixkKCujglYL1l9MvVVWXvv\r\n         cjIXx2XSxwWrYb8TgdoHSS8J5dC/Ok1/1s7SQ0oyfQxFKlnQtDTxNd+peqomwslQ7e77\r\n         /ZlDLIvUgkfVoKvc9yc8Rx5GiW9CvKHrkrTzqtLdc1UgKYHEmI/StlwSQruajTMdgYOT\r\n         O8XtDu3cWlq5kIWfjrTjOXBqB4/4y+a3/KR1uA0Ypkk1vQtlyXH7CDtm7pkltnknwazq\r\n         XMAA==\r\nX-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=1e100.net; s=20230601; t=1752787444; x=1753392244;\r\n        h=to:subject:message-id:date:from:in-reply-to:references:mime-version\r\n         :x-gm-message-state:from:to:cc:subject:date:message-id:reply-to;\r\n        bh=PMN1jRBiULqYWdk8ZIZsjc6u/OUEjdAN3ONuzlG+W7w=;\r\n        b=FMAVHiKe0B8+EZMf73Vr7utlzpb41ROmDJzLTtCcnhjirBBRsmNmq0PKkFGzr1UgQT\r\n         orj82YAyjlqyAC2hN09V4UhmNtl4TSOaTdEQRJzaIZpWrX721N4nb3oPE581CwLqnjDv\r\n         EDg7t6VPyE22ScLchvd6PmZNPQLhnp/XKJitkftjPVGUP5BJH0KpnCHWNhdVFrkAO3+o\r\n         xEPGRrxlcKmIVj3c+1S/61rFYccg0I/C+4SJr9wYDlC9fW4X633ib4gHSP0M+VyrQVDH\r\n         r+SjZDCvX1k5Sv0TAzfUu9D2faArIE78HhZ2q6ebBLZ+Mmob3KX5wTebIsBkqo3+5jnP\r\n         qEWg==\r\nX-Gm-Message-State: AOJu0YwxNKlAHDOrLIBqpi/ulGMcZKTp2azG6m1dZqKDVmPanGpp10Av\r\n	kmGrNpumDBm3neRQB6cMQEOQ912wHjbicyCJiV8sOdGhi2R6tZbqdgGi8yf/37QeDc6EB1RwqZm\r\n	Pk2ZW48vlHLZQG1gapPg/2nAliNtipZY6pQqN\r\nX-Gm-Gg: ASbGnctONU5qO8HEGxgfUx1pP+7yXv44cZ7geoWKPTrCEHFEpLUdz8maJRFYufItnHK\r\n	Q5VxQ2vG0owF3P36kbrFbhKzPJhUUZwTo6KuJRAJRIZIQoPLoOzoljvK+jW/bq23wqpS39MQ5K7\r\n	r7Qax63pewyknHCLHrsv1ec5QrpYOt38M7yX03K65rxdlHd621QmvuC8BjZTePNzgG8rav8l00o\r\n	ZBCF1JYAO5s8N20Fj22npaNCtkC0iwbHCBOiYzMWRt6llZMcQ==\r\nX-Google-Smtp-Source: AGHT+IFJUqthOTRXp0vuDSJW2PlqkeBdK1Vt5Rt7QGUXeLNZ8xP7h8Z7i7XjGknJUZSJkrcz6urNDSM4FQGBZaS9FQo=\r\nX-Received: by 2002:a05:6808:3a0b:b0:40b:4208:8444 with SMTP id\r\n 5614622812f47-41d034f25c0mr5599782b6e.7.1752787443714; Thu, 17 Jul 2025\r\n 14:24:03 -0700 (PDT)\r\nMIME-Version: 1.0\r\nReferences: <<EMAIL>>\r\nIn-Reply-To: <<EMAIL>>\r\nFrom: vrxp <<EMAIL>>\r\nDate: Thu, 17 Jul 2025 23:23:51 +0200\r\nX-Gm-Features: Ac12FXwBRX7ly-81Y6J93ZNLT3Qf_aVFFeWm-cYcWWLs8i3_AXX2y99IhZZxlrc\r\nMessage-ID: <CAMAUxQFOQd7Z=TuYWt-jNWbfHduYmTfeK4hzMnTHP7+=<EMAIL>>\r\nSubject: Re: test\r\nTo: <EMAIL>\r\nContent-Type: multipart/alternative; boundary=\"000000000000f310db063a26a035\"\r\n\r\n--000000000000f310db063a26a035\r\nContent-Type: text/plain; charset=\"UTF-8\"\r\n\r\ngu\r\n\r\nOn Thu, 17 Jul 2025 at 23:23, <<EMAIL>> wrote:\r\n\r\n> test\r\n>\r\n\r\n--000000000000f310db063a26a035\r\nContent-Type: text/html; charset=\"UTF-8\"\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<div dir=3D\"ltr\">gu</div><br><div class=3D\"gmail_quote gmail_quote_containe=\r\nr\"><div dir=3D\"ltr\" class=3D\"gmail_attr\">On Thu, 17 Jul 2025 at 23:23, &lt;=\r\<EMAIL>&gt; wrote:<br></div><blockquote class=3D\"gmail_quote\" sty=\r\nle=3D\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);paddi=\r\nng-left:1ex\">test<br>\r\n</blockquote></div>\r\n\r\n--000000000000f310db063a26a035--\r\n', NULL, NULL, 3584, 1, NULL, 0, 'INBOX', NULL, '2025-07-17 23:24:05', NULL, NULL, NULL),
(10, 2, '<<EMAIL>>', 'Re: test', 'vrxp <<EMAIL>>', '<EMAIL>', NULL, NULL, 'esdfs\r\n\r\nOn Thu, 17 Jul 2025 at 23:23, vrxp <<EMAIL>> wrote:\r\n\r\n> gu\r\n>\r\n> On Thu, 17 Jul 2025 at 23:23, <<EMAIL>> wrote:\r\n>\r\n>> test\r\n>>\r\n>\r\n', '<div dir=\"ltr\">esdfs</div><br><div class=\"gmail_quote gmail_quote_container\"><div dir=\"ltr\" class=\"gmail_attr\">On Thu, 17 Jul 2025 at 23:23, vrxp &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">gu</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Thu, 17 Jul 2025 at 23:23, &lt;<EMAIL>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\">test<br>\r\n</blockquote></div>\r\n</blockquote></div>\r\n', NULL, 852, 1, NULL, 0, 'INBOX', NULL, '2025-07-17 23:52:04', NULL, NULL, NULL),
(11, 2, '<<EMAIL>>', 'test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'twerasd', NULL, NULL, 11, 1, NULL, 0, 'SENT', '2025-07-18 00:58:48', '2025-07-18 00:58:48', NULL, NULL, NULL),
(12, 1, '<<EMAIL>>', 'Welcome to LXND, sadfsdfsdf!', '<EMAIL>', '<EMAIL>', NULL, NULL, 'Hello sadfsdfsdf,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', NULL, NULL, 414, 1, NULL, 0, 'SENT', '2025-07-18 08:53:38', '2025-07-18 08:53:38', NULL, NULL, NULL),
(13, 1, '<<EMAIL>>', 'Welcome to LXND, sadfsdfsdf!', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, 'Hello sadfsdfsdf,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin: 0;\">Welcome to LXND!</h1>\n    </div>\n\n    <div style=\"background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n        <h2 style=\"color: #1e293b; margin-top: 0;\">Hello sadfsdfsdf,</h2>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            Welcome to LXND! Your account <strong><EMAIL></strong> has been successfully created.\n        </p>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            You can now access all our services and features. If you have any questions or need assistance,\n            please don\'t hesitate to contact our support team.\n        </p>\n    </div>\n\n    <div style=\"background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;\">\n        <p style=\"margin: 0; color: #475569;\"><strong>Support:</strong> <EMAIL></p>\n        <p style=\"margin: 5px 0 0 0; color: #475569;\"><strong>Help Center:</strong> https://lxnd.cloud/support</p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 30px;\">\n        <p style=\"color: #64748b; font-size: 14px; margin: 0;\">\n            Best regards,<br>\n            The LXND Team\n        </p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;\">\n        <p style=\"color: #94a3b8; font-size: 12px; margin: 0;\">\n            This email was sent on 2025-07-18.\n        </p>\n    </div>\n</div>', NULL, 1983, 0, 0, 0, 'SENT', '2025-07-18 06:53:38', '2025-07-18 06:53:38', NULL, NULL, NULL),
(14, 2, '<<EMAIL>>', 'Welcome to LXND, luxend2!', '<EMAIL>', '<EMAIL>', NULL, NULL, 'Hello luxend2,\r\n\r\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\r\n\r\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\r\n\r\nSupport: <EMAIL>\r\nHelp Center: https://lxnd.cloud/support\r\n\r\nBest regards,\r\nThe LXND Team\r\n\r\n---\r\nThis email was sent on 2025-07-18.\r\n', '', NULL, 418, 0, NULL, 0, 'INBOX', NULL, '2025-07-18 13:38:51', NULL, NULL, NULL),
(15, 1, '<<EMAIL>>', 'Welcome to LXND, luxend2!', '<EMAIL>', '<EMAIL>', NULL, NULL, 'Hello luxend2,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', NULL, NULL, 403, 1, NULL, 0, 'SENT', '2025-07-18 13:38:51', '2025-07-18 13:38:51', NULL, NULL, NULL),
(16, 1, '<<EMAIL>>', 'Welcome to LXND, luxend2!', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, 'Hello luxend2,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin: 0;\">Welcome to LXND!</h1>\n    </div>\n\n    <div style=\"background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n        <h2 style=\"color: #1e293b; margin-top: 0;\">Hello luxend2,</h2>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            Welcome to LXND! Your account <strong><EMAIL></strong> has been successfully created.\n        </p>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            You can now access all our services and features. If you have any questions or need assistance,\n            please don\'t hesitate to contact our support team.\n        </p>\n    </div>\n\n    <div style=\"background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;\">\n        <p style=\"margin: 0; color: #475569;\"><strong>Support:</strong> <EMAIL></p>\n        <p style=\"margin: 5px 0 0 0; color: #475569;\"><strong>Help Center:</strong> https://lxnd.cloud/support</p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 30px;\">\n        <p style=\"color: #64748b; font-size: 14px; margin: 0;\">\n            Best regards,<br>\n            The LXND Team\n        </p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;\">\n        <p style=\"color: #94a3b8; font-size: 12px; margin: 0;\">\n            This email was sent on 2025-07-18.\n        </p>\n    </div>\n</div>', NULL, 1967, 0, 0, 0, 'SENT', '2025-07-18 11:38:51', '2025-07-18 11:38:51', NULL, NULL, NULL),
(17, 1, '<<EMAIL>>', 'Welcome to LXND, traili!', '<EMAIL>', '<EMAIL>', NULL, NULL, 'Hello traili,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', NULL, NULL, 395, 1, NULL, 0, 'SENT', '2025-07-18 13:39:02', '2025-07-18 13:39:02', NULL, NULL, NULL),
(18, 1, '<<EMAIL>>', 'Welcome to LXND, traili!', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, 'Hello traili,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin: 0;\">Welcome to LXND!</h1>\n    </div>\n\n    <div style=\"background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n        <h2 style=\"color: #1e293b; margin-top: 0;\">Hello traili,</h2>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            Welcome to LXND! Your account <strong><EMAIL></strong> has been successfully created.\n        </p>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            You can now access all our services and features. If you have any questions or need assistance,\n            please don\'t hesitate to contact our support team.\n        </p>\n    </div>\n\n    <div style=\"background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;\">\n        <p style=\"margin: 0; color: #475569;\"><strong>Support:</strong> <EMAIL></p>\n        <p style=\"margin: 5px 0 0 0; color: #475569;\"><strong>Help Center:</strong> https://lxnd.cloud/support</p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 30px;\">\n        <p style=\"color: #64748b; font-size: 14px; margin: 0;\">\n            Best regards,<br>\n            The LXND Team\n        </p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;\">\n        <p style=\"color: #94a3b8; font-size: 12px; margin: 0;\">\n            This email was sent on 2025-07-18.\n        </p>\n    </div>\n</div>', NULL, 1953, 1, 0, 0, 'SENT', '2025-07-18 11:39:02', '2025-07-18 11:39:02', NULL, NULL, NULL),
(19, 2, '<<EMAIL>>', 'tetst', '<EMAIL>', '<EMAIL>', NULL, NULL, 'rtewaeasd', NULL, NULL, 14, 1, NULL, 0, 'SENT', '2025-07-18 17:04:52', '2025-07-18 17:04:52', NULL, NULL, NULL),
(20, 1, '<<EMAIL>>', 'test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'tqeasdasd', NULL, NULL, 13, 1, NULL, 0, 'SENT', '2025-07-18 17:05:21', '2025-07-18 17:05:21', NULL, NULL, NULL),
(21, 2, '<<EMAIL>>', 'Test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'test', NULL, NULL, 8, 1, NULL, 0, 'SENT', '2025-07-18 17:06:27', '2025-07-18 17:06:27', NULL, NULL, NULL),
(22, 2, '<<EMAIL>>', 'Test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'test', NULL, NULL, 8, 1, NULL, 0, 'SENT', '2025-07-18 17:07:28', '2025-07-18 17:07:28', NULL, NULL, NULL),
(23, 1, '<<EMAIL>>', 'Welcome to LXND, Marcel!', '<EMAIL>', '<EMAIL>', NULL, NULL, 'Hello Marcel,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', NULL, NULL, 415, 1, NULL, 0, 'SENT', '2025-07-18 17:25:37', '2025-07-18 17:25:37', NULL, NULL, NULL),
(24, 1, '<<EMAIL>>', 'Welcome to LXND, Marcel!', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, 'Hello Marcel,\n\nWelcome to LXND! <NAME_EMAIL> has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: <EMAIL>\nHelp Center: https://lxnd.cloud/support\n\nBest regards,\nThe LXND Team\n\n---\nThis email was sent on 2025-07-18.', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin: 0;\">Welcome to LXND!</h1>\n    </div>\n\n    <div style=\"background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n        <h2 style=\"color: #1e293b; margin-top: 0;\">Hello Marcel,</h2>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            Welcome to LXND! Your account <strong><EMAIL></strong> has been successfully created.\n        </p>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            You can now access all our services and features. If you have any questions or need assistance,\n            please don\'t hesitate to contact our support team.\n        </p>\n    </div>\n\n    <div style=\"background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;\">\n        <p style=\"margin: 0; color: #475569;\"><strong>Support:</strong> <EMAIL></p>\n        <p style=\"margin: 5px 0 0 0; color: #475569;\"><strong>Help Center:</strong> https://lxnd.cloud/support</p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 30px;\">\n        <p style=\"color: #64748b; font-size: 14px; margin: 0;\">\n            Best regards,<br>\n            The LXND Team\n        </p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;\">\n        <p style=\"color: #94a3b8; font-size: 12px; margin: 0;\">\n            This email was sent on 2025-07-18.\n        </p>\n    </div>\n</div>', NULL, 1993, 1, 0, 0, 'SENT', '2025-07-18 15:25:37', '2025-07-18 15:25:37', NULL, NULL, NULL),
(25, 1, '<<EMAIL>>', 'test', '<EMAIL>', '<EMAIL>', NULL, NULL, 'twrrtwer', NULL, NULL, 12, 1, NULL, 0, 'SENT', '2025-07-18 19:30:00', '2025-07-18 19:30:00', NULL, NULL, NULL),
(26, 1, '<<EMAIL>>', '<EMAIL>', '<EMAIL>', '<EMAIL>', NULL, NULL, '<EMAIL>', NULL, NULL, 70, 1, NULL, 0, 'SENT', '2025-07-18 19:30:57', '2025-07-18 19:30:57', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `email_settings`
--

CREATE TABLE `email_settings` (
  `id` int(11) NOT NULL,
  `smtp_server` varchar(255) DEFAULT NULL,
  `smtp_port` int(11) DEFAULT NULL,
  `smtp_use_tls` tinyint(1) DEFAULT NULL,
  `smtp_use_ssl` tinyint(1) DEFAULT NULL,
  `email_domain` varchar(100) DEFAULT NULL,
  `default_storage_gb` int(11) DEFAULT NULL,
  `max_attachment_mb` int(11) DEFAULT NULL,
  `imap_server` varchar(255) DEFAULT NULL,
  `imap_port` int(11) DEFAULT NULL,
  `imap_use_ssl` tinyint(1) DEFAULT NULL,
  `smtp_timeout` int(11) DEFAULT NULL,
  `imap_timeout` int(11) DEFAULT NULL,
  `enable_dkim` tinyint(1) DEFAULT NULL,
  `enable_spf` tinyint(1) DEFAULT NULL,
  `enable_dmarc` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_settings`
--

INSERT INTO `email_settings` (`id`, `smtp_server`, `smtp_port`, `smtp_use_tls`, `smtp_use_ssl`, `email_domain`, `default_storage_gb`, `max_attachment_mb`, `imap_server`, `imap_port`, `imap_use_ssl`, `smtp_timeout`, `imap_timeout`, `enable_dkim`, `enable_spf`, `enable_dmarc`, `created_at`, `updated_at`) VALUES
(1, 'smtp.gmail.com', 587, 1, 0, 'lxnd.cloud', 10, 25, '127.0.0.1', 2143, 0, 30, 30, 1, 1, 1, '2025-07-16 00:28:33', '2025-07-16 00:28:33');

-- --------------------------------------------------------

--
-- Table structure for table `email_template`
--

CREATE TABLE `email_template` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `subject` varchar(500) NOT NULL,
  `body_text` text NOT NULL,
  `body_html` text DEFAULT NULL,
  `template_type` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_template`
--

INSERT INTO `email_template` (`id`, `name`, `description`, `subject`, `body_text`, `body_html`, `template_type`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Default Welcome Email', 'Default welcome email sent to new users', 'Welcome to {{site_name}}, {{user_name}}!', 'Hello {{user_name}},\n\nWelcome to {{site_name}}! Your account {{user_email}} has been successfully created.\n\nYou can now access all our services and features. If you have any questions or need assistance, please don\'t hesitate to contact our support team.\n\nSupport: {{admin_email}}\nHelp Center: {{support_url}}\n\nBest regards,\nThe {{site_name}} Team\n\n---\nThis email was sent on {{current_date}}.', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin: 0;\">Welcome to {{site_name}}!</h1>\n    </div>\n\n    <div style=\"background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n        <h2 style=\"color: #1e293b; margin-top: 0;\">Hello {{user_name}},</h2>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            Welcome to {{site_name}}! Your account <strong>{{user_email}}</strong> has been successfully created.\n        </p>\n        <p style=\"color: #475569; line-height: 1.6;\">\n            You can now access all our services and features. If you have any questions or need assistance,\n            please don\'t hesitate to contact our support team.\n        </p>\n    </div>\n\n    <div style=\"background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;\">\n        <p style=\"margin: 0; color: #475569;\"><strong>Support:</strong> {{admin_email}}</p>\n        <p style=\"margin: 5px 0 0 0; color: #475569;\"><strong>Help Center:</strong> {{support_url}}</p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 30px;\">\n        <p style=\"color: #64748b; font-size: 14px; margin: 0;\">\n            Best regards,<br>\n            The {{site_name}} Team\n        </p>\n    </div>\n\n    <div style=\"text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;\">\n        <p style=\"color: #94a3b8; font-size: 12px; margin: 0;\">\n            This email was sent on {{current_date}}.\n        </p>\n    </div>\n</div>', 'welcome', 1, '2025-07-16 00:26:50', '2025-07-16 00:26:50');

-- --------------------------------------------------------

--
-- Table structure for table `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `project_id` int(11) DEFAULT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `file_upload`
--

CREATE TABLE `file_upload` (
  `id` int(11) NOT NULL,
  `file_id` varchar(32) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `upload_date` datetime DEFAULT NULL,
  `download_count` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `public` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `file_upload`
--

INSERT INTO `file_upload` (`id`, `file_id`, `filename`, `original_filename`, `file_size`, `file_type`, `upload_date`, `download_count`, `user_id`, `public`) VALUES
(1, 'cvsgRrGh7GgvIBZq5xfctQ', '87f2f05d2c5b470abe254bf8d6e1cadb.jpg', '1716375369462.jpg', 112550, 'jpg', '2025-07-16 00:30:53', 0, 1, 0),
(2, 'sWVAxkHPxHoT7ZQrBdKnTg', '141d93dbbbc04ed18178c134e24fafc9.jpg', '1716375369163.jpg', 314597, 'jpg', '2025-07-18 00:18:33', 0, 2, 0),
(3, 'nGGPOrsI5aMvSZH27YPJMA', '99730104ae974eb29f5ad3206af70f34.zip', 'lxnd.zip', 101148130, 'zip', '2025-07-18 15:58:26', 3, 2, 1);

-- --------------------------------------------------------

--
-- Table structure for table `license`
--

CREATE TABLE `license` (
  `id` int(11) NOT NULL,
  `license_key` varchar(64) NOT NULL,
  `project_id` varchar(32) NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `last_checked` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `duration_days` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `license`
--

INSERT INTO `license` (`id`, `license_key`, `project_id`, `is_active`, `created_at`, `last_checked`, `expires_at`, `duration_days`) VALUES
(1, 'krC4iN2YdK7spJrHkS6-edK96oftwcDNhpRbdBhiaW0', 'tBVXQ569ekTY2zlSVLsErw', 1, '2025-07-17 22:41:44', NULL, NULL, NULL),
(2, 'FJOoNRVmC7Ed9k3DJZ6dUtPT_GD3_WmqM0TF0cpEoic', 'tBVXQ569ekTY2zlSVLsErw', 1, '2025-07-18 17:13:00', NULL, NULL, NULL),
(3, 'jvr5pVvIvT3c7C2q_qIlPBgojE3b3ttU5DMPw35tWZs', 'tBVXQ569ekTY2zlSVLsErw', 1, '2025-07-18 17:13:06', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `lux_license`
--

CREATE TABLE `lux_license` (
  `id` int(11) NOT NULL,
  `license_key` varchar(64) NOT NULL,
  `duration_days` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `used_by` int(11) DEFAULT NULL,
  `used_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lux_license`
--

INSERT INTO `lux_license` (`id`, `license_key`, `duration_days`, `is_active`, `created_at`, `used_by`, `used_at`) VALUES
(3, '4WJX-8IUB-LIKD-IELZ', NULL, 1, '2025-07-17 23:17:23', 3, '2025-07-18 06:23:53'),
(4, 'LAE6-TA4F-GOTC-I905', 365, 1, '2025-07-18 11:34:41', 6, '2025-07-18 11:40:08'),
(6, 'JSCX-HCR3-G0GV-GWUS', NULL, 1, '2025-07-18 15:56:51', 2, '2025-07-18 15:56:57');

-- --------------------------------------------------------

--
-- Table structure for table `notification_templates`
--

CREATE TABLE `notification_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `body_html` text NOT NULL,
  `body_text` text DEFAULT NULL,
  `placeholders` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`placeholders`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project`
--

CREATE TABLE `project` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `project_id` varchar(32) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `project`
--

INSERT INTO `project` (`id`, `name`, `description`, `project_id`, `user_id`, `created_at`) VALUES
(1, 'lxnd', 'Lux ranks', 'tBVXQ569ekTY2zlSVLsErw', 2, '2025-07-17 22:11:48'),
(2, 'Zilen', 'Bis jetzt noch keine Ahnung', 'oflvb1w9nTKX23-XBcYdpA', 3, '2025-07-18 06:30:58'),
(3, 'test', 'tt', 'oYcxjwlaeGXY56YM_pf_pw', 2, '2025-07-18 12:30:48');

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `projects` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `shortened_url`
--

CREATE TABLE `shortened_url` (
  `id` int(11) NOT NULL,
  `short_code` varchar(10) NOT NULL,
  `original_url` text NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `click_count` int(11) DEFAULT NULL,
  `last_clicked` datetime DEFAULT NULL,
  `custom_domain` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shortened_url`
--

INSERT INTO `shortened_url` (`id`, `short_code`, `original_url`, `title`, `description`, `user_id`, `created_at`, `expires_at`, `is_active`, `is_public`, `password_hash`, `click_count`, `last_clicked`, `custom_domain`) VALUES
(1, 'HsLFV7', 'https://example.com/very-long-url-for-testing', 'Test Link', 'This is a test link for the shortener service', NULL, '2025-07-17 22:16:22', NULL, 1, 1, NULL, 1, '2025-07-17 22:17:01', NULL),
(2, 'VnzUrl', 'https://google.com', NULL, NULL, 2, '2025-07-17 22:24:01', NULL, 1, 1, 'scrypt:32768:8:1$9unv5jupw7Goq5pO$507f8341c79119dad5745c62c4d594ff9802bb2df0433ab309493a81eb467a1e179e612d21497fbcf1f2c3f4c2929ddb2b98ffc40769940af9d0cfe2422f063a', 3, '2025-07-18 16:24:15', NULL),
(3, 'mpHwpM', 'https://captchasolver.ai/', NULL, NULL, 6, '2025-07-18 11:44:32', NULL, 1, 1, NULL, 3, '2025-07-18 11:44:59', NULL),
(4, 'discord', 'https://discord.gg/FzXrAS7qeu', NULL, NULL, 5, '2025-07-18 16:29:13', NULL, 1, 1, NULL, 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `smtp_servers`
--

CREATE TABLE `smtp_servers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `host` varchar(255) NOT NULL,
  `port` int(11) NOT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `use_tls` tinyint(1) DEFAULT 1,
  `use_ssl` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `url_click`
--

CREATE TABLE `url_click` (
  `id` int(11) NOT NULL,
  `url_id` int(11) NOT NULL,
  `clicked_at` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `referer` text DEFAULT NULL,
  `country` varchar(2) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `device_type` varchar(50) DEFAULT NULL,
  `browser` varchar(100) DEFAULT NULL,
  `os` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `url_click`
--

INSERT INTO `url_click` (`id`, `url_id`, `clicked_at`, `ip_address`, `user_agent`, `referer`, `country`, `city`, `device_type`, `browser`, `os`) VALUES
(1, 1, '2025-07-17 22:17:01', '***********', 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0', NULL, NULL, NULL, 'desktop', 'Firefox', NULL),
(2, 2, '2025-07-17 22:29:45', '***********', 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0', 'https://lxnd.cloud/s/VnzUrl/password', NULL, NULL, 'desktop', 'Firefox', NULL),
(3, 3, '2025-07-18 11:44:40', '**************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, NULL, NULL, 'desktop', 'Chrome', NULL),
(4, 3, '2025-07-18 11:44:49', '**************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, NULL, NULL, 'desktop', 'Chrome', NULL),
(5, 3, '2025-07-18 11:44:59', '*************', 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0', NULL, NULL, NULL, 'desktop', 'Firefox', NULL),
(6, 2, '2025-07-18 16:24:05', '*************', 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0', 'https://lxnd.cloud/s/VnzUrl/password', NULL, NULL, 'desktop', 'Firefox', NULL),
(7, 2, '2025-07-18 16:24:15', '*************', 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0', 'https://lxnd.cloud/shorten', NULL, NULL, 'desktop', 'Firefox', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `username` varchar(80) NOT NULL,
  `email` varchar(120) NOT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `api_token` varchar(64) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT NULL,
  `is_moderator` tinyint(1) DEFAULT NULL,
  `is_banned` tinyint(1) DEFAULT NULL,
  `upload_limit_mb` int(11) DEFAULT NULL,
  `max_projects` int(11) DEFAULT NULL,
  `max_keys_per_project` int(11) DEFAULT NULL,
  `is_lux` tinyint(1) DEFAULT 0,
  `lux_expires_at` datetime DEFAULT NULL,
  `discord_id` varchar(20) DEFAULT NULL,
  `discord_username` varchar(100) DEFAULT NULL,
  `discord_avatar` varchar(255) DEFAULT NULL,
  `discord_linked_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `username`, `email`, `password_hash`, `api_token`, `created_at`, `is_admin`, `is_moderator`, `is_banned`, `upload_limit_mb`, `max_projects`, `max_keys_per_project`, `is_lux`, `lux_expires_at`, `discord_id`, `discord_username`, `discord_avatar`, `discord_linked_at`) VALUES
(1, 'a', '<EMAIL>', 'scrypt:32768:8:1$BGi37Npl8hDGoz7T$5f951c53834afb23ce92579969abc08f7660f40a4ff00d48a505e35270564fb836ec2be4345fd77f8557c8b5320cd5b7a3288a672ce1b4dec7e9b9a9925649b0', 'Y8WFOouVRGf61lHneOvrzOttk8yeUBOK', '2025-07-16 00:25:48', 1, 1, 0, 15360, 3, 100, 0, NULL, NULL, NULL, NULL, NULL),
(2, 'luxend', '<EMAIL>', 'scrypt:32768:8:1$1qVMk50iCorplWWu$d151c48b0d203dd1901b851ddf1ae04a67acefd282751c587d8b33f044986a323125db37ff06447ce86b88a35604b88711c6d29a34351a4500bc6dc93f890a87', 'lXJWsP7RwkjUPlN84jqtHRB-_LqSLbLwOgVuQmvgnhI', '2025-07-16 00:56:11', 1, 0, 0, 15360, 3, 100, 1, NULL, '1155259528485539932', 'luxend0#0', 'e530301e03eb00737776cb0b4b501757', '2025-07-18 13:04:44'),
(3, 'Gakuseei', '<EMAIL>', 'scrypt:32768:8:1$wuNfF95D2VMUZgxy$f926c5744e78c325722ff63169964f14bdbf8775dd079f212cbe557bd92a2595b6a8258712b123c8330a83e1a1cea4f7b41dbde6c9385e0b50283e2124d3bc0a', 'VlrGQ_yQ2spsWRO09DHDgWUuzOyuArr1mVy925ike64', '2025-07-18 06:23:24', 0, 0, 0, 15360, 3, 100, 1, NULL, NULL, NULL, NULL, NULL),
(5, 'luxend2', '<EMAIL>', 'scrypt:32768:8:1$JDwBGIDWUfGBk1Ct$e44bb2f89507f75549b0cb41e500be923ef61c43586f00533855978abb1b5f8a35b36291b3acc87f80381f30b26ee938858d9dc6b1a3e1780b4eee723acbd448', 'ZpH6DyHIMrrrBxl1npG2ufe1Mx3mx_9BjUr46vxRVBM', '2025-07-18 11:38:51', 0, 0, 0, 15360, 3, 100, 0, NULL, NULL, NULL, NULL, NULL),
(6, 'traili', '<EMAIL>', 'scrypt:32768:8:1$Ht64Oq1ZeAIkCDBn$7b6d448258deeff40a6cb445d7c9db35d12c990a318cc63e006dc435b8fd5108356477505aa3c8ded3f3c6a08fd17a505e72d9926d618cd1a1342f46fdaeb231', 'QSwI-q-n_eG_4vV8XaFqdMOQGVPbHudjcVBtvLOEQsY', '2025-07-18 11:39:01', 0, 0, 0, 15360, 3, 100, 1, '2026-07-18 11:40:08', NULL, NULL, NULL, NULL),
(7, 'Marcel', '<EMAIL>', 'scrypt:32768:8:1$7nYTePvVCLqixY0r$82bc1b586bb065a38716c65bc2f863e1c0dbfe220a21a9f61bbe1e535dc8029a96c2763341e47a18b765d12c52112b3f810d7bea6f0b00add9293d0276500cfb', 'dJKNUNZxkBFPKm2cUceO7aVcO01-zHgs9828UBgBwAw', '2025-07-18 15:25:36', 1, 0, 0, 15360, 3, 100, 0, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(80) NOT NULL,
  `email` varchar(120) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `is_admin` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `api_token` varchar(64) DEFAULT NULL,
  `is_moderator` tinyint(1) DEFAULT 0,
  `is_banned` tinyint(1) DEFAULT 0,
  `upload_limit_mb` int(11) DEFAULT 15360,
  `max_projects` int(11) DEFAULT 3,
  `max_keys_per_project` int(11) DEFAULT 100
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `is_admin`, `created_at`, `last_login`, `is_active`, `api_token`, `is_moderator`, `is_banned`, `upload_limit_mb`, `max_projects`, `max_keys_per_project`) VALUES
(1, 'a', '<EMAIL>', 'scrypt:32768:8:1$BRafpTNedUWud642$88d7b9858fe4ae2faa34ae523f4bb59b5266dfaf8e8025cab9599505d9cf96d3173df175fb8752342f0624ff30cc60ad4c266dbe1c74bf1dcb9c2b58b70688d9', 1, '2025-07-16 00:17:57', NULL, 1, 'wUNvMLuhXu9S1i2dk3AzWw4mkuCJkfi0', 1, 0, 15360, 3, 100);

-- --------------------------------------------------------

--
-- Table structure for table `website`
--

CREATE TABLE `website` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subdomain` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `storage_used_mb` float DEFAULT NULL,
  `storage_limit_mb` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `ssl_enabled` tinyint(1) DEFAULT 0,
  `ssl_cert_path` varchar(500) DEFAULT NULL,
  `ssl_key_path` varchar(500) DEFAULT NULL,
  `sftp_enabled` tinyint(1) DEFAULT 1,
  `sftp_username` varchar(50) DEFAULT NULL,
  `sftp_password` varchar(128) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `website`
--

INSERT INTO `website` (`id`, `user_id`, `subdomain`, `title`, `description`, `is_active`, `storage_used_mb`, `storage_limit_mb`, `created_at`, `updated_at`, `ssl_enabled`, `ssl_cert_path`, `ssl_key_path`, `sftp_enabled`, `sftp_username`, `sftp_password`) VALUES
(6, 2, 'luxend', 'Luxend personal web', '', 1, 0, 2048, '2025-07-18 19:02:59', '2025-07-18 19:03:11', 1, '/etc/letsencrypt/live/luxend.lxnd.cloud/fullchain.pem', '/etc/letsencrypt/live/luxend.lxnd.cloud/privkey.pem', 1, 'web_luxend', '2dCh9-ssSLPTrQ8fWWbcrQ'),
(7, 2, 'test', 'test', '', 1, 0, 2048, '2025-07-18 19:03:28', '2025-07-18 19:13:25', 1, '/etc/letsencrypt/live/test.lxnd.cloud/fullchain.pem', '/etc/letsencrypt/live/test.lxnd.cloud/privkey.pem', 1, 'web_test', '5dpz7ID8hcQjS57uGLSaWA'),
(8, 2, 'nigger', 'nigger', 'wrwer', 1, 0, 2048, '2025-07-18 19:33:30', '2025-07-18 19:33:30', 0, NULL, NULL, 1, 'web_nigger', 'R6xOim4v2e_K_g_LVVIqpg');

-- --------------------------------------------------------

--
-- Table structure for table `website_file`
--

CREATE TABLE `website_file` (
  `id` int(11) NOT NULL,
  `website_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `is_index` tinyint(1) DEFAULT NULL,
  `uploaded_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `bot_settings`
--
ALTER TABLE `bot_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `discord_role_updates`
--
ALTER TABLE `discord_role_updates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_discord_id` (`discord_id`),
  ADD KEY `idx_processed` (`processed`),
  ADD KEY `idx_timestamp` (`timestamp`);

--
-- Indexes for table `emails`
--
ALTER TABLE `emails`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `message_id` (`message_id`),
  ADD KEY `email_account_id` (`email_account_id`);

--
-- Indexes for table `email_account`
--
ALTER TABLE `email_account`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email_address` (`email_address`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `email_accounts`
--
ALTER TABLE `email_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email_address` (`email_address`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `email_attachments`
--
ALTER TABLE `email_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `email_id` (`email_id`);

--
-- Indexes for table `email_message`
--
ALTER TABLE `email_message`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `message_id` (`message_id`),
  ADD KEY `idx_email_message_account_id` (`account_id`),
  ADD KEY `idx_email_message_folder` (`folder`),
  ADD KEY `idx_email_message_received_at` (`received_at`);

--
-- Indexes for table `email_settings`
--
ALTER TABLE `email_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `email_template`
--
ALTER TABLE `email_template`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `project_id` (`project_id`);

--
-- Indexes for table `file_upload`
--
ALTER TABLE `file_upload`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `file_id` (`file_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `license`
--
ALTER TABLE `license`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `license_key` (`license_key`),
  ADD KEY `project_id` (`project_id`);

--
-- Indexes for table `lux_license`
--
ALTER TABLE `lux_license`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `license_key` (`license_key`),
  ADD KEY `used_by` (`used_by`);

--
-- Indexes for table `notification_templates`
--
ALTER TABLE `notification_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `project`
--
ALTER TABLE `project`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `project_id` (`project_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `shortened_url`
--
ALTER TABLE `shortened_url`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ix_shortened_url_short_code` (`short_code`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `smtp_servers`
--
ALTER TABLE `smtp_servers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `url_click`
--
ALTER TABLE `url_click`
  ADD PRIMARY KEY (`id`),
  ADD KEY `url_id` (`url_id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `api_token` (`api_token`),
  ADD UNIQUE KEY `discord_id` (`discord_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `api_token` (`api_token`);

--
-- Indexes for table `website`
--
ALTER TABLE `website`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subdomain` (`subdomain`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `website_file`
--
ALTER TABLE `website_file`
  ADD PRIMARY KEY (`id`),
  ADD KEY `website_id` (`website_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `bot_settings`
--
ALTER TABLE `bot_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `discord_role_updates`
--
ALTER TABLE `discord_role_updates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `emails`
--
ALTER TABLE `emails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `email_account`
--
ALTER TABLE `email_account`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `email_accounts`
--
ALTER TABLE `email_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `email_attachments`
--
ALTER TABLE `email_attachments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_message`
--
ALTER TABLE `email_message`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `email_settings`
--
ALTER TABLE `email_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `email_template`
--
ALTER TABLE `email_template`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `file_upload`
--
ALTER TABLE `file_upload`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `license`
--
ALTER TABLE `license`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `lux_license`
--
ALTER TABLE `lux_license`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `notification_templates`
--
ALTER TABLE `notification_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project`
--
ALTER TABLE `project`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `projects`
--
ALTER TABLE `projects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `shortened_url`
--
ALTER TABLE `shortened_url`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `smtp_servers`
--
ALTER TABLE `smtp_servers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `url_click`
--
ALTER TABLE `url_click`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `website`
--
ALTER TABLE `website`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `website_file`
--
ALTER TABLE `website_file`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `emails`
--
ALTER TABLE `emails`
  ADD CONSTRAINT `emails_ibfk_1` FOREIGN KEY (`email_account_id`) REFERENCES `email_accounts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_account`
--
ALTER TABLE `email_account`
  ADD CONSTRAINT `email_account_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `email_accounts`
--
ALTER TABLE `email_accounts`
  ADD CONSTRAINT `email_accounts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_attachments`
--
ALTER TABLE `email_attachments`
  ADD CONSTRAINT `email_attachments_ibfk_1` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_message`
--
ALTER TABLE `email_message`
  ADD CONSTRAINT `email_message_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `email_account` (`id`);

--
-- Constraints for table `files`
--
ALTER TABLE `files`
  ADD CONSTRAINT `files_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `files_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `file_upload`
--
ALTER TABLE `file_upload`
  ADD CONSTRAINT `file_upload_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `license`
--
ALTER TABLE `license`
  ADD CONSTRAINT `license_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `project` (`project_id`);

--
-- Constraints for table `lux_license`
--
ALTER TABLE `lux_license`
  ADD CONSTRAINT `lux_license_ibfk_1` FOREIGN KEY (`used_by`) REFERENCES `user` (`id`);

--
-- Constraints for table `project`
--
ALTER TABLE `project`
  ADD CONSTRAINT `project_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `projects`
--
ALTER TABLE `projects`
  ADD CONSTRAINT `projects_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `shortened_url`
--
ALTER TABLE `shortened_url`
  ADD CONSTRAINT `shortened_url_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `url_click`
--
ALTER TABLE `url_click`
  ADD CONSTRAINT `url_click_ibfk_1` FOREIGN KEY (`url_id`) REFERENCES `shortened_url` (`id`);

--
-- Constraints for table `website`
--
ALTER TABLE `website`
  ADD CONSTRAINT `website_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `website_file`
--
ALTER TABLE `website_file`
  ADD CONSTRAINT `website_file_ibfk_1` FOREIGN KEY (`website_id`) REFERENCES `website` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
