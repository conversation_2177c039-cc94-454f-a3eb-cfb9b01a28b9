from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_wtf.csrf import CSRFProtect

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()

def init_extensions(app):
    """Initialize Flask extensions"""
    
    # Initialize database
    db.init_app(app)
    
    # Initialize login manager
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    # Initialize CSRF protection
    csrf.init_app(app)
    
    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from models.user import User
        return User.query.get(int(user_id))

def create_tables(app):
    """Create database tables"""
    with app.app_context():
        try:
            # Import all models to ensure they're registered
            from models.user import User
            from models.project import Project, License, LuxLicense
            from models.file_upload import FileUpload
            from models.email import EmailAccount, EmailMessage, EmailTemplate
            from models.website import Website, WebsiteFile
            from models.url_shortener import ShortenedURL
            from models.discord_settings import DiscordSettings
            
            # Create all tables
            db.create_all()
            print("🔧 Initializing LXND system...")
            print("  ✓ Database tables created")
            
            return True
            
        except Exception as e:
            print(f"❌ Startup error: {e}")
            print("🔄 Please check configuration and try again.")
            return False
