#!/usr/bin/env python3
"""
Create MySQL credentials file
"""

import json
import os

def create_credentials():
    """Create MySQL credentials file"""
    credentials = {
        "mysql": {
            "host": "localhost",
            "port": 3306,
            "root_user": "root",
            "root_password": "",
            "app_user": "lxnd_app",
            "app_password": "LxndApp2024!",
            "phpmyadmin_user": "phpmyadmin",
            "phpmyadmin_password": "PhpMyAdmin2024!"
        },
        "databases": {
            "main": "lxnd_main",
            "email": "lxnd_email",
            "discord": "lxnd_discord",
            "phpmyadmin": "phpmyadmin"
        },
        "phpmyadmin": {
            "url": "http://lxnd.cloud:8080",
            "local_url": "http://localhost:8080"
        }
    }
    
    with open('mysql_credentials.json', 'w') as f:
        json.dump(credentials, f, indent=2)
    
    print("MySQL credentials file created successfully!")

if __name__ == "__main__":
    create_credentials()
