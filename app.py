from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, send_from_directory, make_response, session, get_flashed_messages
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import Flask<PERSON>orm, CSRFProtect
from flask_wtf.file import <PERSON><PERSON>ield, FileRequired, FileAllowed
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField, SelectField, IntegerField, TextAreaField, BooleanField, FileField, DateTimeLocalField
from wtforms.fields import URLField
from wtforms.validators import DataRequired, Length, EqualTo, NumberRange, URL, Optional, Regexp
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from functools import wraps
import secrets
import string
import os
import time
from datetime import datetime, timedelta
import bcrypt
import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
import threading
import json
import uuid
import io
import csv
import logging

# Email server functionality removed - using external mail servers only
import concurrent.futures
import asyncio
import subprocess
import psutil
import sys
from mysql_manager import MySQLManager, get_mysql_manager
import requests
from urllib.parse import urlencode

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# MySQL Database Configuration
mysql_config = {
    "host": "localhost",
    "port": 3306,
    "app_user": "lxnd_app",
    "app_password": "LxndApp2024!",
    "database": "lxnd_main"
}

app.config['SQLALCHEMY_DATABASE_URI'] = f"mysql+pymysql://{mysql_config['app_user']}:{mysql_config['app_password']}@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = os.path.join(os.getcwd(), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024  # 10GB max upload
app.config['ALLOWED_EXTENSIONS'] = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'zip', 'rar', 'doc', 'docx', 'xls', 'xlsx', 'tar', 'tgz', 'gz', 'bz2', '7z', 'iso', 'dmg', 'exe', 'msi', 'deb', 'rpm', 'apk', 'sh', 'bat', 'ps1', 'psd', 'ai', 'eps', 'svg', 'csv', 'tsv', 'json', 'xml', 'html', 'htm', 'css', 'js', 'php', 'py', 'rb'}

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Discord OAuth2 Configuration
app.config['DISCORD_CLIENT_ID'] = os.getenv('DISCORD_CLIENT_ID', '1395744980668252220')
app.config['DISCORD_CLIENT_SECRET'] = os.getenv('DISCORD_CLIENT_SECRET', 'IddAilPc1qNeGpRaOyU85Wo2K9ObB6jo')
app.config['DISCORD_REDIRECT_URI'] = os.getenv('DISCORD_REDIRECT_URI', 'https://lxnd.cloud/auth/discord/callback')
app.config['DISCORD_BOT_TOKEN'] = os.getenv('DISCORD_BOT_TOKEN', 'MTM5NTc0NDk4MDY2ODI1MjIyMA.GtP41j.fn92FIjkkQGAKmIvldiVBOw84fuQAkYTAeOmcM')

# Global variable to track bot process
bot_process = None

db = SQLAlchemy(app)
csrf = CSRFProtect(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'


# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    api_token = db.Column(db.String(64), unique=True, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_admin = db.Column(db.Boolean, default=False)
    is_moderator = db.Column(db.Boolean, default=False)
    is_banned = db.Column(db.Boolean, default=False)
    is_lux = db.Column(db.Boolean, default=False)
    lux_expires_at = db.Column(db.DateTime, nullable=True)  # None = permanent
    upload_limit_mb = db.Column(db.Integer, default=15360)  # 100GB in MB
    max_projects = db.Column(db.Integer, default=3)
    max_keys_per_project = db.Column(db.Integer, default=100)
    projects = db.relationship('Project', backref='owner', lazy=True)
    files = db.relationship('FileUpload', backref='owner', lazy=True)
    email_accounts = db.relationship('EmailAccount', backref='owner', lazy=True)

    # Discord Integration
    discord_id = db.Column(db.String(20), unique=True, nullable=True)
    discord_username = db.Column(db.String(100), nullable=True)
    discord_avatar = db.Column(db.String(255), nullable=True)
    discord_linked_at = db.Column(db.DateTime, nullable=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def generate_api_token(self):
        self.api_token = secrets.token_urlsafe(32)
        return self.api_token
    
    def get_used_storage_mb(self):
        total_bytes = sum(file.file_size for file in self.files)
        return total_bytes / (1024 * 1024)  # Convert bytes to MB

    def get_storage_used_formatted(self):
        """Get storage used formatted as KB/MB/GB based on actual bytes"""
        total_bytes = sum(file.file_size for file in self.files)

        if total_bytes < 1024:
            return f"{total_bytes} B"
        elif total_bytes < 1024 * 1024:
            return f"{round(total_bytes / 1024, 1)} KB"
        elif total_bytes < 1024 * 1024 * 1024:
            return f"{round(total_bytes / (1024 * 1024), 1)} MB"
        else:
            return f"{round(total_bytes / (1024 * 1024 * 1024), 2)} GB"
    
    def get_storage_percent(self):
        if self.upload_limit_mb == 0:  # Unlimited
            return 0
        used = self.get_used_storage_mb()
        return min(100, int((used / self.upload_limit_mb) * 100))
    
    def can_upload(self, file_size_bytes):
        if self.upload_limit_mb == 0:  # Unlimited
            return True
        current_usage_mb = self.get_used_storage_mb()
        file_size_mb = file_size_bytes / (1024 * 1024)
        return (current_usage_mb + file_size_mb) <= self.upload_limit_mb
    
    def can_create_project(self):
        if self.max_projects == 0:  # Unlimited
            return True
        return Project.query.filter_by(user_id=self.id).count() < self.max_projects

    def is_lux_active(self):
        """Check if user has active Lux status"""
        if not self.is_lux:
            return False
        if self.lux_expires_at is None:
            return True  # Permanent Lux
        return datetime.utcnow() < self.lux_expires_at

    def get_lux_days_remaining(self):
        """Get days remaining for Lux status"""
        if not self.is_lux or self.lux_expires_at is None:
            return None
        delta = self.lux_expires_at - datetime.utcnow()
        return max(0, delta.days)

    def get_shortener_limit(self):
        """Get URL shortener limit based on user rank"""
        if self.is_admin:
            return 0  # Unlimited
        elif self.is_lux_active():
            return 1000  # Lux users get 1000 URLs
        else:
            return 50  # Regular users get 50 URLs

    def get_max_projects_limit(self):
        """Get project limit based on user rank"""
        if self.is_admin:
            return 0  # Unlimited
        elif self.is_lux_active():
            return 50  # Lux users get 50 projects
        else:
            return self.max_projects  # Use existing limit

    def get_storage_limit_mb_effective(self):
        """Get effective storage limit based on user rank"""
        if self.is_admin:
            return 0  # Unlimited
        elif self.is_lux_active():
            return 1024000  # Lux users get 1TB
        else:
            return self.upload_limit_mb  # Use existing limit

    def get_max_email_accounts(self):
        """Get maximum number of email accounts based on user type"""
        if self.is_admin:
            return float('inf')  # Admins get unlimited
        elif self.is_lux_active():
            return 3  # Lux users get 3 total (1 + 2 additional)
        else:
            return 1  # Regular users get 1

    def can_create_email_account(self):
        """Check if user can create another email account"""
        current_count = EmailAccount.query.filter_by(user_id=self.id).count()
        max_accounts = self.get_max_email_accounts()
        return current_count < max_accounts or max_accounts == float('inf')

    def can_create_license(self, project_id):
        if self.max_keys_per_project == 0:  # Unlimited
            return True
        return License.query.filter_by(project_id=project_id).count() < self.max_keys_per_project

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.String(32), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    licenses = db.relationship('License', backref='project', lazy=True)

    def __init__(self, **kwargs):
        super(Project, self).__init__(**kwargs)
        if not self.project_id:
            self.project_id = self.generate_project_id()
    
    def generate_project_id(self):
        return secrets.token_urlsafe(16)

class License(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(64), unique=True, nullable=False)
    project_id = db.Column(db.String(32), db.ForeignKey('project.project_id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_checked = db.Column(db.DateTime)
    expires_at = db.Column(db.DateTime, nullable=True)
    duration_days = db.Column(db.Integer, nullable=True)

    def __init__(self, **kwargs):
        super(License, self).__init__(**kwargs)
        if not self.license_key:
            self.license_key = self.generate_license_key()
    
    def generate_license_key(self):
        return secrets.token_urlsafe(32)
    
    def is_expired(self):
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self):
        return self.is_active and not self.is_expired()
    
    def days_until_expiry(self):
        if self.expires_at is None:
            return None
        delta = self.expires_at - datetime.utcnow()
        return delta.days if delta.days > 0 else 0

class LuxLicense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(64), unique=True, nullable=False)
    duration_days = db.Column(db.Integer, nullable=True)  # None = permanent
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    used_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    used_at = db.Column(db.DateTime, nullable=True)

    def __init__(self, **kwargs):
        super(LuxLicense, self).__init__(**kwargs)
        if not self.license_key:
            self.license_key = self.generate_license_key()

    def generate_license_key(self):
        """Generate a unique license key"""
        import string
        import secrets

        # Format: XXXX-XXXX-XXXX-XXXX
        segments = []
        for _ in range(4):
            segment = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
            segments.append(segment)

        key = '-'.join(segments)

        # Ensure uniqueness
        while LuxLicense.query.filter_by(license_key=key).first():
            segments = []
            for _ in range(4):
                segment = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
                segments.append(segment)
            key = '-'.join(segments)

        return key

    def is_used(self):
        """Check if license has been used"""
        return self.used_by is not None

    def use_license(self, user):
        """Use license for a user"""
        if self.is_used() or not self.is_active:
            return False

        self.used_by = user.id
        self.used_at = datetime.utcnow()

        # Grant Lux status to user
        user.is_lux = True
        if self.duration_days:
            user.lux_expires_at = datetime.utcnow() + timedelta(days=self.duration_days)
        else:
            user.lux_expires_at = None  # Permanent

        # Update Discord role if user has Discord linked
        if user.discord_id:
            update_discord_role(user.discord_id, 'lux', True)

        return True

    def get_user(self):
        """Get the user who used this license"""
        if self.used_by:
            return User.query.get(self.used_by)
        return None

class ShortenedUrl(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    short_code = db.Column(db.String(10), unique=True, nullable=False, index=True)
    original_url = db.Column(db.Text, nullable=False)
    title = db.Column(db.String(255))
    description = db.Column(db.Text)

    # User and creation info
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # Nullable for anonymous links
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)

    # Settings
    is_active = db.Column(db.Boolean, default=True)
    is_public = db.Column(db.Boolean, default=True)
    password_hash = db.Column(db.String(255), nullable=True)  # Optional password protection

    # Analytics
    click_count = db.Column(db.Integer, default=0)
    last_clicked = db.Column(db.DateTime, nullable=True)

    # Custom domain support
    custom_domain = db.Column(db.String(255), nullable=True)

    # Relationships
    clicks = db.relationship('UrlClick', backref='url', lazy=True, cascade='all, delete-orphan')

    def __init__(self, **kwargs):
        super(ShortenedUrl, self).__init__(**kwargs)
        if not self.short_code:
            self.short_code = self.generate_short_code()

    def generate_short_code(self, length=6):
        """Generate a unique short code"""
        import string
        import secrets

        characters = string.ascii_letters + string.digits
        while True:
            code = ''.join(secrets.choice(characters) for _ in range(length))
            # Check if code already exists
            if not ShortenedUrl.query.filter_by(short_code=code).first():
                return code

    def get_short_url(self, request=None):
        """Get the complete short URL"""
        if self.custom_domain:
            return f"https://{self.custom_domain}/{self.short_code}"
        else:
            # Use current domain or default
            if request:
                return f"{request.scheme}://{request.host}/s/{self.short_code}"
            else:
                return f"https://lxnd.cloud/s/{self.short_code}"

    def is_expired(self):
        """Check if URL has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    def check_password(self, password):
        """Check password for protected URLs"""
        if not self.password_hash:
            return True
        return check_password_hash(self.password_hash, password)

    def set_password(self, password):
        """Set password for URL protection"""
        if password:
            self.password_hash = generate_password_hash(password)
        else:
            self.password_hash = None

class UrlClick(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    url_id = db.Column(db.Integer, db.ForeignKey('shortened_url.id'), nullable=False)

    # Click information
    clicked_at = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))  # IPv6 support
    user_agent = db.Column(db.Text)
    referer = db.Column(db.Text)

    # Geolocation (optional)
    country = db.Column(db.String(2))  # ISO country code
    city = db.Column(db.String(100))

    # Device info
    device_type = db.Column(db.String(50))  # mobile, desktop, tablet
    browser = db.Column(db.String(100))
    os = db.Column(db.String(100))

class FileUpload(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.String(32), unique=True, nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    file_type = db.Column(db.String(50))
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    download_count = db.Column(db.Integer, default=0)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    public = db.Column(db.Boolean, default=False)

    def __init__(self, **kwargs):
        super(FileUpload, self).__init__(**kwargs)
        if not self.file_id:
            self.file_id = self.generate_file_id()

    def generate_file_id(self):
        return secrets.token_urlsafe(16)

    def get_download_url(self):
        return url_for('download_file', file_id=self.file_id, _external=True)

class BotSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # Embed settings
    embed_color = db.Column(db.String(7), default='#00ff00')  # Hex color
    global_footer = db.Column(db.String(255), default='LXND License Management System')

    # Bot status settings
    status = db.Column(db.String(20), default='online')  # online, idle, dnd, invisible
    activity_type = db.Column(db.String(20), default='watching')  # playing, streaming, listening, watching, custom
    activity_text = db.Column(db.String(128), default='LXND License System')
    activity_url = db.Column(db.String(255), nullable=True)  # For streaming activity

    # Bot profile settings
    bot_name = db.Column(db.String(100), nullable=True)  # Display name (if different from Discord username)
    bot_avatar_url = db.Column(db.String(255), nullable=True)  # Custom avatar URL

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_settings():
        """Get bot settings, create default if none exist"""
        settings = BotSettings.query.first()
        if not settings:
            settings = BotSettings()
            db.session.add(settings)
            db.session.commit()
        return settings

    def get_embed_color_int(self):
        """Convert hex color to integer for Discord"""
        try:
            return int(self.embed_color.replace('#', ''), 16)
        except:
            return 0x00ff00  # Default green

    def get_activity_type_enum(self):
        """Get Discord activity type enum"""
        activity_map = {
            'playing': 0,
            'streaming': 1,
            'listening': 2,
            'watching': 3,
            'custom': 4
        }
        return activity_map.get(self.activity_type, 3)  # Default to watching

    def get_status_enum(self):
        """Get Discord status enum"""
        status_map = {
            'online': 'online',
            'idle': 'idle',
            'dnd': 'dnd',
            'invisible': 'invisible'
        }
        return status_map.get(self.status, 'online')

class EmailSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # SMTP Configuration (for external emails)
    smtp_server = db.Column(db.String(255), default='smtp.gmail.com')
    smtp_port = db.Column(db.Integer, default=587)
    smtp_use_tls = db.Column(db.Boolean, default=True)
    smtp_use_ssl = db.Column(db.Boolean, default=False)

    # Domain Configuration
    email_domain = db.Column(db.String(100), default='lxnd.cloud')
    default_storage_gb = db.Column(db.Integer, default=10)
    max_attachment_mb = db.Column(db.Integer, default=25)

    # Server Settings
    imap_server = db.Column(db.String(255), default='127.0.0.1')
    imap_port = db.Column(db.Integer, default=2143)
    imap_use_ssl = db.Column(db.Boolean, default=False)

    # Additional settings for production
    smtp_timeout = db.Column(db.Integer, default=30)
    imap_timeout = db.Column(db.Integer, default=30)
    enable_dkim = db.Column(db.Boolean, default=True)
    enable_spf = db.Column(db.Boolean, default=True)
    enable_dmarc = db.Column(db.Boolean, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_settings():
        """Get email settings, create default if none exist"""
        settings = EmailSettings.query.first()
        if not settings:
            settings = EmailSettings()
            db.session.add(settings)
            db.session.commit()
        return settings

class EmailTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.String(255))
    subject = db.Column(db.String(500), nullable=False)
    body_text = db.Column(db.Text, nullable=False)
    body_html = db.Column(db.Text)
    template_type = db.Column(db.String(50), default='custom')  # welcome, notification, custom
    is_active = db.Column(db.Boolean, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<EmailTemplate {self.name}>'

    @staticmethod
    def get_welcome_template():
        """Get the active welcome template"""
        return EmailTemplate.query.filter_by(template_type='welcome', is_active=True).first()

    def render_template(self, variables=None):
        """Render template with variables"""
        if variables is None:
            variables = {}

        # Replace placeholders in subject and body
        rendered_subject = self.subject
        rendered_body_text = self.body_text
        rendered_body_html = self.body_html

        for var_name, var_value in variables.items():
            placeholder = f'{{{{{var_name}}}}}'
            rendered_subject = rendered_subject.replace(placeholder, str(var_value))
            rendered_body_text = rendered_body_text.replace(placeholder, str(var_value))
            if rendered_body_html:
                rendered_body_html = rendered_body_html.replace(placeholder, str(var_value))

        return {
            'subject': rendered_subject,
            'body_text': rendered_body_text,
            'body_html': rendered_body_html
        }

class EmailAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # Email Configuration
    email_address = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    smtp_password = db.Column(db.Text, nullable=True)  # Base64 encoded password for SMTP/IMAP
    display_name = db.Column(db.String(100))

    # Storage and Limits
    storage_used_mb = db.Column(db.Integer, default=0)
    storage_limit_mb = db.Column(db.Integer, default=10240)  # 10GB default

    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Relationships
    messages = db.relationship('EmailMessage', backref='account', lazy=True, cascade='all, delete-orphan')

    @property
    def received_emails(self):
        """Get received emails for this account"""
        return EmailMessage.query.filter_by(
            account_id=self.id,
            folder='INBOX'
        ).all()

    @property
    def sent_emails(self):
        """Get sent emails for this account"""
        return EmailMessage.query.filter_by(
            account_id=self.id,
            folder='SENT'
        ).all()

    def __init__(self, **kwargs):
        super(EmailAccount, self).__init__(**kwargs)
        if not self.display_name and self.email_address:
            self.display_name = self.email_address.split('@')[0]

    def get_user(self):
        """Get the user who owns this email account"""
        if self.user_id:
            return User.query.get(self.user_id)
        return None

    def set_password(self, password):
        """Set password hash and store encoded password for SMTP/IMAP"""
        # Store bcrypt hash for web authentication
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        # Also store base64 encoded password for SMTP/IMAP (NOT SECURE - use proper encryption in production)
        import base64
        # In production, use proper encryption like Fernet or similar
        self.smtp_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')

    def check_password(self, password):
        """Check password"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def get_smtp_password(self):
        """Get decoded password for SMTP/IMAP authentication"""
        try:
            import base64
            if self.smtp_password:
                return base64.b64decode(self.smtp_password.encode()).decode('utf-8')
            return None
        except:
            return None

    def set_smtp_password(self, password):
        """Set encoded password for SMTP/IMAP authentication"""
        try:
            import base64
            if password:
                self.smtp_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
            else:
                self.smtp_password = None
        except Exception as e:
            logger.error(f"Error setting SMTP password: {e}")
            self.smtp_password = None

    def get_storage_percent(self):
        """Get storage usage percentage"""
        if self.storage_limit_mb == 0:
            return 0
        return round((self.storage_used_mb / self.storage_limit_mb) * 100, 1)

    def get_storage_remaining_mb(self):
        """Get remaining storage in MB"""
        return max(0, self.storage_limit_mb - self.storage_used_mb)

    def get_storage_used_formatted(self):
        """Get storage used formatted as KB/MB/GB based on actual bytes"""
        # Get actual bytes from all messages
        total_bytes = db.session.query(db.func.sum(EmailMessage.size_bytes)).filter_by(
            account_id=self.id,
            is_deleted=False
        ).scalar() or 0

        if total_bytes < 1024:
            return f"{total_bytes} B"
        elif total_bytes < 1024 * 1024:
            return f"{round(total_bytes / 1024, 1)} KB"
        elif total_bytes < 1024 * 1024 * 1024:
            return f"{round(total_bytes / (1024 * 1024), 1)} MB"
        else:
            return f"{round(total_bytes / (1024 * 1024 * 1024), 2)} GB"

    def get_storage_limit_formatted(self):
        """Get storage limit formatted as GB"""
        return f"{round(self.storage_limit_mb / 1024, 1)} GB"

    def can_receive_email(self, size_mb):
        """Check if account can receive email of given size"""
        return self.is_active and (self.storage_used_mb + size_mb) <= self.storage_limit_mb

    def recalculate_storage(self):
        """Recalculate storage usage from all messages"""
        total_bytes = db.session.query(db.func.sum(EmailMessage.size_bytes)).filter_by(
            account_id=self.id,
            is_deleted=False
        ).scalar() or 0

        # Convert bytes to MB with proper rounding (only round up if > 0.5 MB)
        total_mb = total_bytes / (1024 * 1024)
        if total_mb < 0.1:  # Less than 0.1 MB, store as 0
            self.storage_used_mb = 0
        else:
            # Round to nearest MB for larger files
            import math
            self.storage_used_mb = math.ceil(total_mb)

        return self.storage_used_mb

class EmailMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    account_id = db.Column(db.Integer, db.ForeignKey('email_account.id'), nullable=False)

    # Message Headers
    message_id = db.Column(db.String(255), unique=True, nullable=False)
    subject = db.Column(db.String(500))
    sender = db.Column(db.String(255), nullable=False)
    recipients = db.Column(db.Text)  # JSON array of recipients
    cc = db.Column(db.Text)  # JSON array of CC recipients
    bcc = db.Column(db.Text)  # JSON array of BCC recipients

    # Message Content
    body_text = db.Column(db.Text)
    body_html = db.Column(db.Text)
    attachments = db.Column(db.Text)  # JSON array of attachment info

    # Message Properties
    size_bytes = db.Column(db.Integer, default=0)
    is_read = db.Column(db.Boolean, default=False)
    is_starred = db.Column(db.Boolean, default=False)
    is_deleted = db.Column(db.Boolean, default=False)
    folder = db.Column(db.String(50), default='INBOX')

    # Reply/Thread Properties
    in_reply_to = db.Column(db.String(255))  # Message-ID of the message this is replying to
    references = db.Column(db.Text)  # Space-separated list of Message-IDs in the thread
    thread_id = db.Column(db.String(255))  # Thread identifier for grouping related messages

    # Timestamps
    sent_at = db.Column(db.DateTime)
    received_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, **kwargs):
        super(EmailMessage, self).__init__(**kwargs)
        if not self.message_id:
            self.message_id = self.generate_message_id()

    def generate_message_id(self):
        """Generate unique message ID"""
        import uuid
        return f"<{uuid.uuid4()}@lxnd.cloud>"

    def get_size_mb(self):
        """Get message size in MB"""
        return round(self.size_bytes / (1024 * 1024), 2)

    def get_size_formatted(self):
        """Get message size formatted as KB or MB"""
        if not self.size_bytes:
            return "0 B"

        if self.size_bytes < 1024:
            return f"{self.size_bytes} B"
        elif self.size_bytes < 1024 * 1024:
            return f"{round(self.size_bytes / 1024, 1)} KB"
        else:
            return f"{round(self.size_bytes / (1024 * 1024), 2)} MB"

    def get_recipients_list(self):
        """Get recipients as list"""
        import json
        try:
            return json.loads(self.recipients) if self.recipients else []
        except:
            return []

    def set_recipients_list(self, recipients):
        """Set recipients from list"""
        import json
        self.recipients = json.dumps(recipients)

    def get_attachments_list(self):
        """Get attachments as list"""
        import json
        try:
            return json.loads(self.attachments) if self.attachments else []
        except:
            return []

    def set_attachments_list(self, attachments):
        """Set attachments from list"""
        import json
        self.attachments = json.dumps(attachments)

    @property
    def has_attachments(self):
        """Check if message has attachments"""
        attachments = self.get_attachments_list()
        return len(attachments) > 0

    def get_size_mb(self):
        """Get message size in MB as string"""
        if self.size_bytes:
            return f"{self.size_bytes / (1024 * 1024):.2f}"
        return "0.00"

    def get_size_mb_numeric(self):
        """Get message size in MB as numeric value"""
        if self.size_bytes:
            return self.size_bytes / (1024 * 1024)
        return 0.0

    def get_thread_messages(self):
        """Get all messages in the same thread"""
        if not self.thread_id:
            return [self]

        return EmailMessage.query.filter_by(
            account_id=self.account_id,
            thread_id=self.thread_id,
            is_deleted=False
        ).order_by(EmailMessage.received_at.asc()).all()

    def get_reply_subject(self):
        """Get subject for reply (with Re: prefix if not already present)"""
        if self.subject and not self.subject.lower().startswith('re:'):
            return f"Re: {self.subject}"
        return self.subject or "Re: (no subject)"

class Website(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subdomain = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    storage_used_mb = db.Column(db.Float, default=0.0)
    storage_limit_mb = db.Column(db.Integer, default=100)  # 100MB default, 2GB for Lux users
    ssl_enabled = db.Column(db.Boolean, default=False)
    ssl_cert_path = db.Column(db.String(500))
    ssl_key_path = db.Column(db.String(500))
    sftp_enabled = db.Column(db.Boolean, default=True)
    sftp_username = db.Column(db.String(50))
    sftp_password = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('websites', lazy=True))
    files = db.relationship('WebsiteFile', backref='website', lazy=True, cascade='all, delete-orphan')

    def get_url(self):
        """Get the full URL of the website"""
        return f"https://{self.subdomain}.lxnd.cloud"

    def get_storage_used_formatted(self):
        """Get storage used formatted as KB/MB/GB"""
        total_bytes = self.storage_used_mb * 1024 * 1024

        if total_bytes < 1024:
            return f"{int(total_bytes)} B"
        elif total_bytes < 1024 * 1024:
            return f"{round(total_bytes / 1024, 1)} KB"
        elif total_bytes < 1024 * 1024 * 1024:
            return f"{round(total_bytes / (1024 * 1024), 1)} MB"
        else:
            return f"{round(total_bytes / (1024 * 1024 * 1024), 2)} GB"

    def get_storage_percent(self):
        """Get storage usage percentage"""
        if self.storage_limit_mb == 0:
            return 0
        return min(100, int((self.storage_used_mb / self.storage_limit_mb) * 100))

    def can_upload(self, file_size_mb):
        """Check if file can be uploaded"""
        return (self.storage_used_mb + file_size_mb) <= self.storage_limit_mb

    def update_storage_usage(self):
        """Update storage usage from files"""
        total_bytes = sum(file.file_size for file in self.files)
        self.storage_used_mb = total_bytes / (1024 * 1024)
        return self.storage_used_mb

    def get_storage_limit_effective(self):
        """Get effective storage limit based on user type"""
        # Use the stored limit if available, otherwise calculate based on user type
        if hasattr(self, 'user') and self.user:
            if self.user.is_admin:
                return 10240  # 10GB for admins
            elif self.user.is_lux_active():
                return 2048   # 2GB for Lux users
            else:
                return 100    # 100MB for regular users
        else:
            # Fallback to stored limit
            return self.storage_limit_mb or 100

    def generate_ssl_certificate(self):
        """Generate Let's Encrypt SSL certificate for subdomain using standalone mode"""
        try:
            import subprocess
            import os
            import time

            domain = f"{self.subdomain}.lxnd.cloud"

            print(f"Generating Let's Encrypt SSL certificate for {domain}...")

            # First, try standalone mode (requires stopping Flask temporarily)
            cmd = [
                '/snap/bin/certbot', 'certonly',
                '--standalone',
                '--email', '<EMAIL>',
                '--agree-tos',
                '--no-eff-email',
                '--non-interactive',
                '--domains', domain,
                '--http-01-port', '80'
            ]

            print("Attempting standalone certificate generation...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Let's Encrypt stores certificates in /etc/letsencrypt/live/
                ssl_dir = f"/etc/letsencrypt/live/{domain}"

                self.ssl_enabled = True
                self.ssl_cert_path = f"{ssl_dir}/fullchain.pem"
                self.ssl_key_path = f"{ssl_dir}/privkey.pem"

                print(f"Let's Encrypt SSL certificate generated successfully for {domain}")
                return True
            else:
                print(f"Let's Encrypt failed: {result.stderr}")

                # Try DNS challenge as alternative
                print("Trying DNS challenge method...")
                dns_cmd = [
                    '/snap/bin/certbot', 'certonly',
                    '--manual',
                    '--preferred-challenges', 'dns',
                    '--email', '<EMAIL>',
                    '--agree-tos',
                    '--no-eff-email',
                    '--domains', domain,
                    '--dry-run'
                ]

                dns_result = subprocess.run(dns_cmd, capture_output=True, text=True)
                if dns_result.returncode == 0:
                    print("DNS challenge available, but requires manual setup")

                # Fallback: Create proper wildcard self-signed certificate
                print("Creating production-ready self-signed certificate as fallback...")
                return self._create_production_self_signed_certificate()

        except Exception as e:
            print(f"Error generating SSL certificate: {e}")
            return self._create_production_self_signed_certificate()

    def _create_self_signed_certificate(self):
        """Create self-signed certificate as fallback"""
        try:
            import subprocess
            import os

            domain = f"{self.subdomain}.lxnd.cloud"
            ssl_dir = f"/root/lxnd/ssl/{self.subdomain}"
            os.makedirs(ssl_dir, exist_ok=True)

            cert_path = f"{ssl_dir}/cert.pem"
            key_path = f"{ssl_dir}/key.pem"

            # Generate self-signed certificate
            cmd = [
                'openssl', 'req', '-x509', '-newkey', 'rsa:2048',
                '-keyout', key_path,
                '-out', cert_path,
                '-days', '365',
                '-nodes',
                '-subj', f'/C=DE/ST=Germany/L=Berlin/O=LXND/CN={domain}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.ssl_enabled = True
                self.ssl_cert_path = cert_path
                self.ssl_key_path = key_path

                # Set proper permissions
                subprocess.run(['chmod', '600', key_path])
                subprocess.run(['chmod', '644', cert_path])

                print(f"Self-signed certificate created for {domain} (development only)")
                return True
            else:
                print(f"Failed to create self-signed certificate: {result.stderr}")
                return False

        except Exception as e:
            print(f"Error creating self-signed certificate: {e}")
            return False

    def _create_production_self_signed_certificate(self):
        """Create production-ready self-signed certificate with proper SAN"""
        try:
            import subprocess
            import os
            import tempfile

            domain = f"{self.subdomain}.lxnd.cloud"
            ssl_dir = f"/root/lxnd/ssl/{self.subdomain}"
            os.makedirs(ssl_dir, exist_ok=True)

            cert_path = f"{ssl_dir}/cert.pem"
            key_path = f"{ssl_dir}/key.pem"

            # Create OpenSSL config with SAN
            config_content = f"""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = DE
ST = Germany
L = Berlin
O = LXND
CN = {domain}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = {domain}
DNS.2 = *.{domain}
"""

            # Write config to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
                f.write(config_content)
                config_file = f.name

            try:
                # Generate private key
                key_cmd = ['openssl', 'genrsa', '-out', key_path, '2048']
                subprocess.run(key_cmd, capture_output=True, text=True, check=True)

                # Generate certificate signing request
                csr_path = f"{ssl_dir}/cert.csr"
                csr_cmd = [
                    'openssl', 'req', '-new',
                    '-key', key_path,
                    '-out', csr_path,
                    '-config', config_file
                ]
                subprocess.run(csr_cmd, capture_output=True, text=True, check=True)

                # Generate self-signed certificate
                cert_cmd = [
                    'openssl', 'x509', '-req',
                    '-in', csr_path,
                    '-signkey', key_path,
                    '-out', cert_path,
                    '-days', '365',
                    '-extensions', 'v3_req',
                    '-extfile', config_file
                ]

                result = subprocess.run(cert_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    self.ssl_enabled = True
                    self.ssl_cert_path = cert_path
                    self.ssl_key_path = key_path

                    # Set proper permissions
                    subprocess.run(['chmod', '600', key_path])
                    subprocess.run(['chmod', '644', cert_path])

                    # Clean up
                    os.unlink(csr_path)

                    print(f"Production self-signed certificate created for {domain}")
                    print("Note: This is a self-signed certificate. For production, use Let's Encrypt.")
                    return True
                else:
                    print(f"Failed to create certificate: {result.stderr}")
                    return False

            finally:
                # Clean up config file
                os.unlink(config_file)

        except Exception as e:
            print(f"Error creating production self-signed certificate: {e}")
            return False

    def setup_sftp_access(self):
        """Setup SFTP access for the website"""
        try:
            import secrets
            import subprocess
            import os

            # Generate SFTP credentials
            self.sftp_username = f"web_{self.subdomain}"
            self.sftp_password = secrets.token_urlsafe(16)

            # Create website directory first
            website_dir = f"/root/lxnd/websites/{self.subdomain}"
            os.makedirs(website_dir, exist_ok=True)

            # Check if user already exists
            check_user = subprocess.run(['id', self.sftp_username], capture_output=True)
            if check_user.returncode == 0:
                # User exists, just update password and permissions
                cmd_passwd = f"echo '{self.sftp_username}:{self.sftp_password}' | chpasswd"
                subprocess.run(cmd_passwd, shell=True, capture_output=True)
                print(f"Updated password for existing user: {self.sftp_username}")
            else:
                # Create new user with proper home directory
                cmd_user = [
                    'useradd',
                    '-m',
                    '-d', website_dir,
                    '-s', '/bin/bash',
                    '-g', 'www-data',  # Add to www-data group
                    self.sftp_username
                ]

                result = subprocess.run(cmd_user, capture_output=True, text=True)
                if result.returncode != 0:
                    print(f"Failed to create user: {result.stderr}")
                    return False

                # Set password
                cmd_passwd = f"echo '{self.sftp_username}:{self.sftp_password}' | chpasswd"
                passwd_result = subprocess.run(cmd_passwd, shell=True, capture_output=True, text=True)
                if passwd_result.returncode != 0:
                    print(f"Failed to set password: {passwd_result.stderr}")
                    return False

                print(f"Created new SFTP user: {self.sftp_username}")

            # Set proper ownership and permissions
            # Make user owner of their website directory
            subprocess.run(['chown', '-R', f'{self.sftp_username}:www-data', website_dir], capture_output=True)

            # Set directory permissions: owner can read/write/execute, group can read/execute
            subprocess.run(['chmod', '-R', '755', website_dir], capture_output=True)

            # Ensure the user can write to their directory
            subprocess.run(['chmod', '755', website_dir], capture_output=True)

            # Create a welcome file if directory is empty
            welcome_file = os.path.join(website_dir, 'welcome.txt')
            if not os.listdir(website_dir):
                with open(welcome_file, 'w') as f:
                    f.write(f"Welcome to your SFTP directory for {self.subdomain}.lxnd.cloud!\n")
                    f.write("You can upload your website files here.\n")
                    f.write("Upload an index.html file to create your homepage.\n")

                # Set ownership of welcome file
                subprocess.run(['chown', f'{self.sftp_username}:www-data', welcome_file], capture_output=True)
                subprocess.run(['chmod', '644', welcome_file], capture_output=True)

            print(f"SFTP access configured for {self.subdomain} - User: {self.sftp_username}")
            return True

        except Exception as e:
            print(f"Error setting up SFTP: {e}")
            return False

    def get_sftp_info(self):
        """Get SFTP connection information"""
        if self.sftp_enabled and self.sftp_username:
            return {
                'host': 'lxnd.cloud',
                'port': 22,
                'username': self.sftp_username,
                'password': self.sftp_password,
                'path': f'/root/lxnd/websites/{self.subdomain}'
            }
        return None

class WebsiteFile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    website_id = db.Column(db.Integer, db.ForeignKey('website.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    mime_type = db.Column(db.String(100))
    is_index = db.Column(db.Boolean, default=False)  # Mark as index.html
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_size_formatted(self):
        """Get file size formatted"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{round(self.file_size / 1024, 1)} KB"
        else:
            return f"{round(self.file_size / (1024 * 1024), 1)} MB"

    def is_reply(self):
        """Check if this message is a reply to another message"""
        return bool(self.in_reply_to)

    def get_original_message(self):
        """Get the original message this is replying to"""
        if not self.in_reply_to:
            return None

        return EmailMessage.query.filter_by(
            account_id=self.account_id,
            message_id=self.in_reply_to,
            is_deleted=False
        ).first()

    def get_thread_id(self):
        """Generate or get thread ID for this message"""
        if self.thread_id:
            return self.thread_id

        # If this is a reply, use the original message's thread ID
        if self.in_reply_to:
            original = self.get_original_message()
            if original and original.thread_id:
                return original.thread_id

        # Otherwise, use this message's ID as thread ID
        return self.message_id

def send_welcome_email(user_email, user_name=None):
    """Send welcome email to new user"""
    try:
        # Get the active welcome template
        welcome_template = EmailTemplate.get_welcome_template()
        if not welcome_template:
            logger.warning("No active welcome email template found")
            return False, "No welcome template configured"

        # Get admin email account for sending
        admin_account = EmailAccount.query.filter_by(email_address='<EMAIL>').first()
        if not admin_account:
            logger.warning("Admin email account not found")
            return False, "Admin email account not configured"

        # Prepare template variables
        template_vars = {
            'user_name': user_name or user_email.split('@')[0],
            'user_email': user_email,
            'site_name': 'LXND',
            'admin_email': '<EMAIL>',
            'support_url': 'https://lxnd.cloud/support',
            'current_date': datetime.utcnow().strftime('%Y-%m-%d')
        }

        # Render the template
        rendered = welcome_template.render_template(template_vars)

        # Send the email
        email_service = EmailService()
        success, message = email_service.send_email(
            from_account=admin_account,
            to_addresses=[user_email],
            subject=rendered['subject'],
            body_text=rendered['body_text'],
            body_html=rendered['body_html']
        )

        if success:
            logger.info(f"Welcome email sent to {user_email}")
            return True, "Welcome email sent successfully"
        else:
            logger.error(f"Failed to send welcome email to {user_email}: {message}")
            return False, f"Failed to send welcome email: {message}"

    except Exception as e:
        logger.error(f"Error sending welcome email to {user_email}: {e}")
        return False, f"Error sending welcome email: {str(e)}"

def create_default_email_templates():
    """Create default email templates if they don't exist"""
    try:
        # Check if welcome template exists
        welcome_template = EmailTemplate.query.filter_by(template_type='welcome').first()
        if not welcome_template:
            # Create default welcome template
            welcome_template = EmailTemplate(
                name='Default Welcome Email',
                description='Default welcome email sent to new users',
                template_type='welcome',
                subject='Welcome to {{site_name}}, {{user_name}}!',
                body_text='''Hello {{user_name}},

Welcome to {{site_name}}! Your account {{user_email}} has been successfully created.

You can now access all our services and features. If you have any questions or need assistance, please don't hesitate to contact our support team.

Support: {{admin_email}}
Help Center: {{support_url}}

Best regards,
The {{site_name}} Team

---
This email was sent on {{current_date}}.''',
                body_html='''<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">Welcome to {{site_name}}!</h1>
    </div>

    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1e293b; margin-top: 0;">Hello {{user_name}},</h2>
        <p style="color: #475569; line-height: 1.6;">
            Welcome to {{site_name}}! Your account <strong>{{user_email}}</strong> has been successfully created.
        </p>
        <p style="color: #475569; line-height: 1.6;">
            You can now access all our services and features. If you have any questions or need assistance,
            please don't hesitate to contact our support team.
        </p>
    </div>

    <div style="background: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <p style="margin: 0; color: #475569;"><strong>Support:</strong> {{admin_email}}</p>
        <p style="margin: 5px 0 0 0; color: #475569;"><strong>Help Center:</strong> {{support_url}}</p>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <p style="color: #64748b; font-size: 14px; margin: 0;">
            Best regards,<br>
            The {{site_name}} Team
        </p>
    </div>

    <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
        <p style="color: #94a3b8; font-size: 12px; margin: 0;">
            This email was sent on {{current_date}}.
        </p>
    </div>
</div>''',
                is_active=True
            )

            db.session.add(welcome_template)
            db.session.commit()
            logger.info("Created default welcome email template")

    except Exception as e:
        logger.error(f"Error creating default email templates: {e}")

# Email Service Classes
class EmailService:
    """Email service for sending and receiving emails"""

    def __init__(self):
        # Always get fresh settings from database
        db.session.expire_all()  # Clear any cached data
        self.settings = EmailSettings.get_settings()

        # Try to import and use integrated mailserver
        self.use_integrated_mailserver = True
        try:
            import mailserver
            self.mailserver = mailserver
        except ImportError:
            self.use_integrated_mailserver = False
            self.mailserver = None

    def send_email(self, from_account, to_addresses, subject, body_text=None, body_html=None, attachments=None):
        """Send an email"""
        try:
            # Separate local and external addresses
            local_addresses = []
            external_addresses = []

            for to_address in to_addresses:
                if to_address.endswith('@lxnd.cloud') or to_address.endswith('@mail.lxnd.cloud'):
                    local_addresses.append(to_address)
                else:
                    external_addresses.append(to_address)

            # Send all addresses via integrated mailserver (it handles routing automatically)
            if self.use_integrated_mailserver and self.mailserver and self.mailserver.is_mailserver_running():
                for to_address in to_addresses:
                    password = from_account.get_smtp_password()
                    success, message = self.mailserver.send_email_via_mailserver(
                        from_account.email_address,
                        to_address,
                        subject,
                        body_text or body_html or '',
                        password
                    )
                    if success:
                        logger.info(f"Email sent via integrated mailserver to {to_address}")
                    else:
                        logger.error(f"Failed to send via integrated mailserver: {message}")
                        return False, f"Failed to send to {to_address}: {message}"
            else:
                # Fallback to external SMTP for all addresses
                success, message = self._send_via_external_smtp(from_account, to_addresses, subject, body_text, body_html, attachments)
                if not success:
                    return False, message

            # Save sent message to database
            self._save_sent_message(from_account, to_addresses, subject, body_text, body_html, attachments)
            return True, f"Email sent successfully to: {', '.join(to_addresses)}"

        except Exception as e:
            return False, f"Failed to send email: {str(e)}"

    def _send_via_external_smtp(self, from_account, to_addresses, subject, body_text=None, body_html=None, attachments=None):
        """Send email via external SMTP server"""
        try:
            # Create message with proper headers for external delivery
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = from_account.email_address
            msg['To'] = ', '.join(to_addresses)
            msg['Message-ID'] = f"<{uuid.uuid4()}@lxnd.cloud>"
            msg['Date'] = email.utils.formatdate(localtime=True)
            msg['X-Mailer'] = 'LXND Mail Server v1.0'
            msg['Return-Path'] = from_account.email_address
            msg['Sender'] = from_account.email_address

            # Add text part
            if body_text:
                text_part = MIMEText(body_text, 'plain', 'utf-8')
                msg.attach(text_part)

            # Add HTML part
            if body_html:
                html_part = MIMEText(body_html, 'html', 'utf-8')
                msg.attach(html_part)

            # Add attachments
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['data'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)

            # Connect to external SMTP server using settings from database
            # Try to connect with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Connect to external SMTP server
                    logger.info(f"Attempting SMTP connection to {self.settings.smtp_server}:{self.settings.smtp_port} (attempt {attempt + 1})")
                    server = smtplib.SMTP(self.settings.smtp_server, self.settings.smtp_port, timeout=10)

                    # Use STARTTLS if enabled
                    if self.settings.smtp_use_tls:
                        try:
                            server.starttls()
                            logger.info("STARTTLS enabled for SMTP connection")
                        except Exception as e:
                            logger.warning(f"STARTTLS failed: {e}")

                    # Authenticate with external server (required for external servers)
                    smtp_password = from_account.get_smtp_password()
                    if smtp_password:
                        try:
                            server.login(from_account.email_address, smtp_password)
                            logger.info("SMTP authentication successful")
                        except Exception as e:
                            logger.error(f"SMTP authentication failed: {e}")
                            return False, f"SMTP authentication failed: {e}"
                    else:
                        logger.error("No SMTP password available for authentication")
                        return False, "No SMTP password available for authentication"

                    # Send email to all addresses
                    text = msg.as_string()
                    server.sendmail(from_account.email_address, to_addresses, text)
                    server.quit()

                    logger.info(f"Successfully sent email from {from_account.email_address} to: {to_addresses}")

                    # Save sent message to database
                    self._save_sent_message(from_account, to_addresses, subject, body_text, body_html, attachments)

                    return True, f"Email sent successfully to: {', '.join(to_addresses)}"

                except ConnectionRefusedError as e:
                    logger.warning(f"Connection refused on attempt {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)  # Wait 1 second before retry
                        continue
                    else:
                        error_msg = f"Failed to send email after {max_retries} attempts: Connection refused. Check external SMTP server settings."
                        logger.error(error_msg)
                        return False, error_msg
                except Exception as e:
                    error_msg = f"Failed to send email: {str(e)}"
                    logger.error(error_msg)
                    return False, error_msg

        except Exception as e:
            return False, f"Failed to send email: {str(e)}"

    def _save_sent_message(self, from_account, to_addresses, subject, body_text, body_html, attachments):
        """Save sent message to database"""
        try:
            message = EmailMessage(
                account_id=from_account.id,
                subject=subject,
                sender=from_account.email_address,
                body_text=body_text,
                body_html=body_html,
                folder='SENT',
                sent_at=datetime.utcnow()
            )
            message.set_recipients_list(to_addresses)

            if attachments:
                message.set_attachments_list([{
                    'filename': att['filename'],
                    'size': len(att['data'])
                } for att in attachments])

            # Calculate message size
            message.size_bytes = len(str(message.body_text or '')) + len(str(message.body_html or ''))
            if attachments:
                message.size_bytes += sum(len(att['data']) for att in attachments)

            db.session.add(message)

            # Update account storage
            from_account.recalculate_storage()

            db.session.commit()

        except Exception as e:
            print(f"Error saving sent message: {e}")

    def check_email(self, email_account):
        """Check for new emails via IMAP from external server or integrated mailserver"""
        try:
            # Check if this is a local address and we have integrated mailserver
            if (self.use_integrated_mailserver and self.mailserver and
                (email_account.email_address.endswith('@lxnd.cloud') or email_account.email_address.endswith('@mail.lxnd.cloud'))):
                # Use integrated mailserver to get emails
                emails = self.mailserver.get_emails_for_address(email_account.email_address)
                return True, emails

            # Use external IMAP server
            # Connect to external IMAP server
            if self.settings.imap_use_ssl:
                mail = imaplib.IMAP4_SSL(self.settings.imap_server, self.settings.imap_port)
            else:
                mail = imaplib.IMAP4(self.settings.imap_server, self.settings.imap_port)

            # Authenticate with external server (required)
            smtp_password = email_account.get_smtp_password()
            if smtp_password:
                mail.login(email_account.email_address, smtp_password)
            else:
                raise Exception("No IMAP password available for authentication")

            # Select inbox
            mail.select('INBOX')

            # Search for new emails
            status, messages = mail.search(None, 'UNSEEN')

            new_messages = []
            if status == 'OK':
                for msg_id in messages[0].split():
                    # Fetch message
                    status, msg_data = mail.fetch(msg_id, '(RFC822)')

                    if status == 'OK':
                        email_message = email.message_from_bytes(msg_data[0][1])

                        # Parse and save message
                        saved_message = self._parse_and_save_message(email_account, email_message)
                        if saved_message:
                            new_messages.append(saved_message)

            mail.close()
            mail.logout()

            return True, new_messages

        except Exception as e:
            return False, f"Failed to check email: {str(e)}"

    def _parse_and_save_message(self, email_account, email_message):
        """Parse and save an email message"""
        try:
            # Extract headers
            subject = email_message.get('Subject', '')
            sender = email_message.get('From', '')
            date_str = email_message.get('Date', '')

            # Parse date
            sent_at = datetime.utcnow()
            try:
                from email.utils import parsedate_to_datetime
                sent_at = parsedate_to_datetime(date_str)
            except:
                pass

            # Extract body
            body_text = ''
            body_html = ''
            attachments = []

            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition'))

                    if content_type == 'text/plain' and 'attachment' not in content_disposition:
                        body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == 'text/html' and 'attachment' not in content_disposition:
                        body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif 'attachment' in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            # Get attachment data and encode it
                            attachment_data = part.get_payload(decode=True)
                            import base64
                            attachments.append({
                                'filename': filename,
                                'size': len(attachment_data),
                                'data': base64.b64encode(attachment_data).decode('utf-8'),
                                'content_type': part.get_content_type()
                            })
            else:
                body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')

            # Calculate size
            message_size = len(body_text) + len(body_html) + sum(att['size'] for att in attachments)

            # Check storage limit
            if not email_account.can_receive_email(message_size / (1024 * 1024)):
                return None  # Skip if storage full

            # Create and save message
            message = EmailMessage(
                account_id=email_account.id,
                subject=subject,
                sender=sender,
                body_text=body_text,
                body_html=body_html,
                size_bytes=message_size,
                sent_at=sent_at,
                received_at=datetime.utcnow()
            )

            message.set_attachments_list(attachments)

            db.session.add(message)

            # Update account storage
            email_account.recalculate_storage()

            db.session.commit()

            return message

        except Exception as e:
            print(f"Error parsing message: {e}")
            return None

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# Forms
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

class RegisterForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Register')

class ProjectForm(FlaskForm):
    name = StringField('Project Name', validators=[DataRequired(), Length(min=1, max=100)])
    description = StringField('Description')
    submit = SubmitField('Create Project')

class LicenseForm(FlaskForm):
    custom_key = StringField('Custom License Key (optional)', validators=[Length(max=64)])
    duration_type = SelectField('Duration Type', choices=[
        ('unlimited', 'Unlimited'),
        ('days', 'Days'),
        ('months', 'Months'),
        ('years', 'Years')
    ], default='unlimited')
    duration_value = IntegerField('Duration Value', validators=[NumberRange(min=1, max=3650)], default=30)
    submit = SubmitField('Generate License')

class FileUploadForm(FlaskForm):
    file = FileField('File', validators=[
        FileRequired(),
        FileAllowed(list(app.config['ALLOWED_EXTENSIONS']), 'File type not allowed')
    ])
    public = SelectField('Visibility', choices=[
        ('private', 'Private - Only you can access'),
        ('public', 'Public - Anyone with the link can access')
    ], default='private')
    submit = SubmitField('Upload File')

class ShortenUrlForm(FlaskForm):
    original_url = URLField('URL to Shorten', validators=[DataRequired(), URL()],
                           render_kw={'class': 'form-control', 'placeholder': 'https://example.com/very-long-url'})
    custom_code = StringField('Custom Short Code (Optional)', validators=[Optional(), Length(max=20)],
                             render_kw={'class': 'form-control', 'placeholder': 'my-link'})
    title = StringField('Title (Optional)', validators=[Optional(), Length(max=255)],
                       render_kw={'class': 'form-control', 'placeholder': 'My Awesome Link'})
    description = TextAreaField('Description (Optional)', validators=[Optional(), Length(max=500)],
                               render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'Description of this link'})
    expires_at = DateTimeLocalField('Expiration Date (Optional)', validators=[Optional()],
                                   render_kw={'class': 'form-control'})
    password = PasswordField('Password Protection (Optional)',
                            render_kw={'class': 'form-control', 'placeholder': 'Leave empty for no password'})
    is_public = SelectField('Visibility', choices=[
        ('true', 'Public - Visible in public listings'),
        ('false', 'Private - Only accessible via direct link')
    ], default='true', render_kw={'class': 'form-control'})
    submit = SubmitField('Shorten URL', render_kw={'class': 'btn btn-primary'})

class UrlPasswordForm(FlaskForm):
    password = PasswordField('Password', validators=[DataRequired()],
                            render_kw={'class': 'form-control', 'placeholder': 'Enter password'})
    submit = SubmitField('Access URL', render_kw={'class': 'btn btn-primary'})

class LuxLicenseForm(FlaskForm):
    license_key = StringField('License Key', validators=[DataRequired()],
                             render_kw={'class': 'form-control', 'placeholder': 'XXXX-XXXX-XXXX-XXXX'})
    submit = SubmitField('Activate License', render_kw={'class': 'btn btn-primary'})

class CreateLuxLicenseForm(FlaskForm):
    duration_days = IntegerField('Duration (Days)', validators=[Optional()],
                                render_kw={'class': 'form-control', 'placeholder': 'Leave empty for permanent'})
    quantity = IntegerField('Quantity', validators=[DataRequired(), NumberRange(min=1, max=100)],
                           render_kw={'class': 'form-control', 'placeholder': '1'}, default=1)
    submit = SubmitField('Generate Licenses', render_kw={'class': 'btn btn-primary'})

class WebsiteForm(FlaskForm):
    subdomain = StringField('Subdomain', validators=[
        DataRequired(),
        Length(min=3, max=50),
        Regexp(r'^[a-zA-Z0-9-]+$', message='Subdomain can only contain letters, numbers, and hyphens')
    ])
    title = StringField('Website Title', validators=[DataRequired(), Length(min=1, max=100)])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Create Website')

class WebsiteFileUploadForm(FlaskForm):
    file = FileField('File', validators=[DataRequired()])
    is_index = BooleanField('Set as index.html')
    submit = SubmitField('Upload File')

class BotSettingsForm(FlaskForm):
    # Embed settings
    embed_color = StringField('Embed Color', validators=[DataRequired()],
                             render_kw={'type': 'color', 'class': 'form-control'})
    global_footer = StringField('Global Footer Text', validators=[DataRequired(), Length(max=255)],
                               render_kw={'class': 'form-control', 'placeholder': 'LXND License Management System'})

    # Bot status settings
    status = SelectField('Bot Status', choices=[
        ('online', 'Online'),
        ('idle', 'Idle'),
        ('dnd', 'Do Not Disturb'),
        ('invisible', 'Invisible')
    ], default='online', render_kw={'class': 'form-control'})

    activity_type = SelectField('Activity Type', choices=[
        ('playing', 'Playing'),
        ('streaming', 'Streaming'),
        ('listening', 'Listening to'),
        ('watching', 'Watching'),
        ('custom', 'Custom')
    ], default='watching', render_kw={'class': 'form-control'})

    activity_text = StringField('Activity Text', validators=[DataRequired(), Length(max=128)],
                               render_kw={'class': 'form-control', 'placeholder': 'LXND License System'})

    activity_url = StringField('Activity URL (for streaming)', validators=[Length(max=255)],
                              render_kw={'class': 'form-control', 'placeholder': 'https://twitch.tv/username (optional)'})

    # Bot profile settings
    bot_name = StringField('Bot Display Name', validators=[Length(max=100)],
                          render_kw={'class': 'form-control', 'placeholder': 'Leave empty to use Discord username'})

    bot_avatar_url = StringField('Bot Avatar URL', validators=[Length(max=255)],
                                render_kw={'class': 'form-control', 'placeholder': 'https://example.com/avatar.png (optional)'})

    submit = SubmitField('Save Settings', render_kw={'class': 'btn btn-primary'})

class EmailAccountForm(FlaskForm):
    email_address = StringField('Email Address', validators=[DataRequired(), Length(max=255)],
                               render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'})
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)],
                            render_kw={'class': 'form-control'})
    confirm_password = PasswordField('Confirm Password',
                                   validators=[DataRequired(), EqualTo('password')],
                                   render_kw={'class': 'form-control'})
    display_name = StringField('Display Name', validators=[Length(max=100)],
                              render_kw={'class': 'form-control', 'placeholder': 'Your Name'})
    storage_limit_gb = IntegerField('Storage Limit (GB)', validators=[DataRequired(), NumberRange(min=1, max=100)],
                                   default=10, render_kw={'class': 'form-control'})
    submit = SubmitField('Create Email Account', render_kw={'class': 'btn btn-primary'})

class EmailNotificationForm(FlaskForm):
    template_name = StringField('Template Name', validators=[DataRequired()],
                               render_kw={'class': 'form-control', 'placeholder': 'Welcome Email'})
    subject = StringField('Subject', validators=[DataRequired()],
                         render_kw={'class': 'form-control', 'placeholder': 'Welcome to {{site_name}}'})
    body_text = TextAreaField('Email Body (Text)', validators=[DataRequired()],
                             render_kw={'class': 'form-control', 'rows': 10,
                                       'placeholder': 'Hello {{user_name}},\n\nWelcome to {{site_name}}!'})
    body_html = TextAreaField('Email Body (HTML)',
                             render_kw={'class': 'form-control', 'rows': 10,
                                       'placeholder': '<h1>Hello {{user_name}}</h1><p>Welcome to {{site_name}}!</p>'})
    recipients = TextAreaField('Recipients (one per line)', validators=[DataRequired()],
                              render_kw={'class': 'form-control', 'rows': 5,
                                        'placeholder': '<EMAIL>\<EMAIL>'})
    submit = SubmitField('Send Notification', render_kw={'class': 'btn btn-primary'})

class EmailTemplateForm(FlaskForm):
    name = StringField('Template Name', validators=[DataRequired(), Length(max=100)],
                      render_kw={'class': 'form-control', 'placeholder': 'e.g., Welcome Email'})
    description = StringField('Description', validators=[Length(max=255)],
                             render_kw={'class': 'form-control', 'placeholder': 'Brief description of this template'})
    template_type = SelectField('Template Type', choices=[
        ('custom', 'Custom Template'),
        ('welcome', 'Welcome Email'),
        ('notification', 'Notification')
    ], default='custom', render_kw={'class': 'form-control'})
    subject = StringField('Subject', validators=[DataRequired(), Length(max=500)],
                         render_kw={'class': 'form-control', 'placeholder': 'Email subject with {{variables}}'})
    body_text = TextAreaField('Message (Text)', validators=[DataRequired()],
                             render_kw={'class': 'form-control', 'rows': '10', 'placeholder': 'Plain text message with {{variables}}'})
    body_html = TextAreaField('Message (HTML)',
                             render_kw={'class': 'form-control', 'rows': '10', 'placeholder': 'HTML message (optional)'})
    is_active = BooleanField('Active', default=True, render_kw={'class': 'form-check-input'})
    submit = SubmitField('Save Template', render_kw={'class': 'btn btn-primary'})

class UserEmailAccountForm(FlaskForm):
    email_address = StringField('Email Address', validators=[DataRequired(), Length(max=255)],
                               render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'})
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)],
                            render_kw={'class': 'form-control'})
    confirm_password = PasswordField('Confirm Password',
                                   validators=[DataRequired(), EqualTo('password')],
                                   render_kw={'class': 'form-control'})
    display_name = StringField('Display Name', validators=[Length(max=100)],
                              render_kw={'class': 'form-control', 'placeholder': 'Your Name'})
    submit = SubmitField('Create Email Account', render_kw={'class': 'btn btn-primary'})

# Using external mail servers for email functionality

class ComposeEmailForm(FlaskForm):
    from_account = SelectField('From Account', coerce=int, validators=[DataRequired()],
                              render_kw={'class': 'form-control'})
    to = StringField('To', validators=[DataRequired()],
                    render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'})
    cc = StringField('CC', render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'})
    bcc = StringField('BCC', render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'})
    subject = StringField('Subject', validators=[DataRequired()],
                         render_kw={'class': 'form-control', 'placeholder': 'Email subject'})
    body = TextAreaField('Message', validators=[DataRequired()],
                        render_kw={'class': 'form-control', 'rows': '10', 'placeholder': 'Type your message here...'})
    attachments = FileField('Attachments', render_kw={'class': 'form-control', 'multiple': True})
    submit = SubmitField('Send Email', render_kw={'class': 'btn btn-primary'})

class EmailSettingsForm(FlaskForm):
    # SMTP Settings
    smtp_server = StringField('SMTP Server', validators=[DataRequired()],
                             render_kw={'class': 'form-control'})
    smtp_port = IntegerField('SMTP Port', validators=[DataRequired(), NumberRange(min=1, max=65535)],
                            render_kw={'class': 'form-control'})
    smtp_use_tls = SelectField('Use TLS', choices=[('true', 'Yes'), ('false', 'No')],
                              render_kw={'class': 'form-control'})
    smtp_use_ssl = SelectField('Use SSL', choices=[('true', 'Yes'), ('false', 'No')],
                              render_kw={'class': 'form-control'})

    # IMAP Settings
    imap_server = StringField('IMAP Server', validators=[DataRequired()],
                             render_kw={'class': 'form-control'})
    imap_port = IntegerField('IMAP Port', validators=[DataRequired(), NumberRange(min=1, max=65535)],
                            render_kw={'class': 'form-control'})
    imap_use_ssl = SelectField('IMAP Use SSL', choices=[('true', 'Yes'), ('false', 'No')],
                              render_kw={'class': 'form-control'})

    # Domain Settings
    email_domain = StringField('Email Domain', validators=[DataRequired()],
                              render_kw={'class': 'form-control'})
    default_storage_gb = IntegerField('Default Storage (GB)', validators=[DataRequired(), NumberRange(min=1, max=1000)],
                                     render_kw={'class': 'form-control'})
    max_attachment_mb = IntegerField('Max Attachment Size (MB)', validators=[DataRequired(), NumberRange(min=1, max=100)],
                                    render_kw={'class': 'form-control'})

    submit = SubmitField('Save Email Settings', render_kw={'class': 'btn btn-primary'})

class ChangeAccountPasswordForm(FlaskForm):
    current_password = PasswordField('Current Password', validators=[DataRequired()],
                                   render_kw={'class': 'form-control'})
    new_password = PasswordField('New Password', validators=[DataRequired(), Length(min=6)],
                               render_kw={'class': 'form-control'})
    confirm_password = PasswordField('Confirm New Password',
                                   validators=[DataRequired(), EqualTo('new_password')],
                                   render_kw={'class': 'form-control'})
    submit = SubmitField('Change Account Password', render_kw={'class': 'btn btn-primary'})

class ChangeEmailPasswordForm(FlaskForm):
    email_account = SelectField('Email Account', coerce=int, validators=[DataRequired()],
                               render_kw={'class': 'form-control'})
    current_password = PasswordField('Current Email Password', validators=[DataRequired()],
                                   render_kw={'class': 'form-control'})
    new_password = PasswordField('New Email Password', validators=[DataRequired(), Length(min=8)],
                               render_kw={'class': 'form-control'})
    confirm_password = PasswordField('Confirm New Email Password',
                                   validators=[DataRequired(), EqualTo('new_password')],
                                   render_kw={'class': 'form-control'})
    submit = SubmitField('Change Email Password', render_kw={'class': 'btn btn-primary'})

def load_messages():
    """Load messages from JSON file"""
    try:
        with open('messages.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

# Load messages globally
messages = load_messages()

# Configure logging
logger = logging.getLogger(__name__)

# Add custom Jinja2 filters
@app.template_filter('from_json')
def from_json_filter(value):
    """Convert JSON string to Python object"""
    try:
        return json.loads(value) if value else []
    except:
        return []

@app.template_filter('regex_replace')
def regex_replace_filter(value, pattern, replacement):
    """Replace text using regex pattern"""
    import re
    try:
        return re.sub(pattern, replacement, str(value))
    except:
        return value

# Helper functions
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def save_file_chunk(chunk_data, chunk_path):
    with open(chunk_path, 'wb') as f:
        f.write(chunk_data)
    return chunk_path

def combine_chunks(chunk_paths, output_path):
    with open(output_path, 'wb') as output:
        for chunk_path in chunk_paths:
            with open(chunk_path, 'rb') as chunk:
                output.write(chunk.read())
            os.remove(chunk_path)  # Remove chunk after combining
    return output_path

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('You do not have permission to access this page')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Moderator required decorator
def moderator_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or (not current_user.is_admin and not current_user.is_moderator):
            flash('You do not have permission to access this page')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Admin routes
@app.route('/admin')
@login_required
@admin_required
def admin_dashboard():
    users = User.query.all()

    # Calculate statistics
    total_projects = sum(len(user.projects) for user in users)
    total_licenses = License.query.count()
    active_keys = License.query.filter_by(is_active=True).count()
    total_storage_mb = sum(user.get_used_storage_mb() for user in users)
    admin_count = sum(1 for user in users if user.is_admin)
    total_email_accounts = EmailAccount.query.count()

    return render_template('admin/dashboard.html',
                         users=users,
                         messages=messages,
                         total_projects=total_projects,
                         total_licenses=total_licenses,
                         active_keys=active_keys,
                         total_storage_mb=total_storage_mb,
                         admin_count=admin_count,
                         total_email_accounts=total_email_accounts)

# New Admin Routes
@app.route('/admin/users')
@login_required
@admin_required
def admin_users():
    """User management page"""
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('admin/users.html', users=users, messages=messages)

@app.route('/admin/projects')
@login_required
@admin_required
def admin_projects():
    """Project management page"""
    projects = Project.query.order_by(Project.created_at.desc()).all()
    licenses = License.query.order_by(License.created_at.desc()).all()
    return render_template('admin/projects.html', projects=projects, licenses=licenses, messages=messages)

@app.route('/admin/email')
@login_required
@admin_required
def admin_email():
    """Email management page"""
    email_accounts = EmailAccount.query.order_by(EmailAccount.created_at.desc()).all()
    return render_template('admin/email.html', email_accounts=email_accounts, messages=messages)

@app.route('/admin/system')
@login_required
@admin_required
def admin_system():
    """System management page"""
    # Get system information
    system_info = {
        'users_count': User.query.count(),
        'projects_count': Project.query.count(),
        'files_count': FileUpload.query.count(),
        'storage_used': sum(user.get_used_storage_mb() for user in User.query.all()),
        'email_accounts': EmailAccount.query.count()
    }
    return render_template('admin/system.html', system_info=system_info, messages=messages)

@app.route('/admin/licenses')
@login_required
@admin_required
def admin_licenses():
    """Admin license management page"""
    licenses = LuxLicense.query.order_by(LuxLicense.created_at.desc()).limit(50).all()

    # Calculate stats
    total_licenses = LuxLicense.query.count()
    active_licenses = LuxLicense.query.filter_by(is_active=True).count()
    used_licenses = LuxLicense.query.filter(LuxLicense.used_by.isnot(None)).count()

    # Count expired licenses (if you have expiration logic)
    expired_licenses = 0  # Implement based on your expiration logic

    # Get linked users count for Discord stats
    linked_users_count = User.query.filter(User.discord_id.isnot(None)).count()

    return render_template('admin/licenses.html',
                         licenses=licenses,
                         total_licenses=total_licenses,
                         active_licenses=active_licenses,
                         used_licenses=used_licenses,
                         expired_licenses=expired_licenses,
                         linked_users_count=linked_users_count,
                         messages=messages)

@app.route('/admin/licenses/generate', methods=['POST'])
@login_required
@admin_required
def admin_generate_license():
    """Generate a single Lux license"""
    try:
        project_name = request.form.get('project_name')
        duration_type = request.form.get('duration_type')
        duration_days = request.form.get('duration_days')

        # Generate Lux license (project_name is just for display, not needed for Lux licenses)
        license_obj = LuxLicense(
            is_active=True
        )

        if duration_type == 'custom' and duration_days:
            license_obj.duration_days = int(duration_days)

        db.session.add(license_obj)
        db.session.commit()

        return jsonify({
            'success': True,
            'license_key': license_obj.license_key,
            'project_name': project_name or 'Lux Premium'
        })

    except Exception as e:
        logger.error(f"Error generating Lux license: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/licenses/bulk', methods=['POST'])
@login_required
@admin_required
def admin_bulk_licenses():
    """Generate multiple Lux licenses"""
    try:
        project_name = request.form.get('project_name')
        count = int(request.form.get('count', 1))
        duration_type = request.form.get('duration_type')
        duration_days = request.form.get('duration_days')

        if count > 100:
            return jsonify({'success': False, 'error': 'Maximum 100 licenses per bulk creation'})

        # Generate Lux licenses
        created_licenses = []

        for i in range(count):
            license_obj = LuxLicense(
                is_active=True
            )

            if duration_type == 'custom' and duration_days:
                license_obj.duration_days = int(duration_days)

            db.session.add(license_obj)
            created_licenses.append(license_obj.license_key)

        db.session.commit()

        return jsonify({
            'success': True,
            'count': count,
            'licenses': created_licenses
        })

    except Exception as e:
        logger.error(f"Error creating bulk Lux licenses: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/licenses/<int:license_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_license(license_id):
    """Delete a Lux license"""
    try:
        license_obj = LuxLicense.query.get_or_404(license_id)

        # Check if license is already used
        if license_obj.is_used():
            return jsonify({'success': False, 'error': 'Cannot delete used license'})

        db.session.delete(license_obj)
        db.session.commit()

        return jsonify({'success': True, 'message': 'License deleted successfully'})

    except Exception as e:
        logger.error(f"Error deleting license: {e}")
        return jsonify({'success': False, 'error': str(e)})

# Discord Bot Admin API Routes
@app.route('/admin/discord/status')
@login_required
@admin_required
def admin_discord_status():
    """Get Discord bot status"""
    try:
        # Get linked users count
        linked_users = User.query.filter(User.discord_id.isnot(None)).count()

        # Get pending role updates
        with get_mysql_manager() as mysql_db:
            pending_updates = mysql_db.execute_query("""
                SELECT COUNT(*) as count FROM discord_role_updates WHERE processed = FALSE
            """)
            pending_count = pending_updates[0]['count'] if pending_updates else 0

        # Try to get bot stats from the Discord bot API
        bot_stats = {}
        try:
            import requests
            # Try HTTPS first (port 443), then HTTP (port 3001)
            for url in ['https://localhost:443/stats', 'http://localhost:3001/stats']:
                try:
                    response = requests.get(url, timeout=5, verify=False)
                    if response.status_code == 200:
                        bot_stats = response.json()
                        break
                except:
                    continue
        except:
            pass

        return jsonify({
            'success': True,
            'online': bot_stats.get('bot', {}).get('status') == 'online',
            'servers': 1,
            'linked_users': linked_users,
            'pending_updates': pending_count,
            'bot_stats': bot_stats
        })

    except Exception as e:
        logger.error(f"Error getting Discord status: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/discord/restart', methods=['POST'])
@login_required
@admin_required
def admin_discord_restart():
    """Restart Discord bot"""
    try:
        # Create restart flag file
        restart_flag_path = os.path.join(os.getcwd(), 'discord_bot', 'restart_flag.txt')
        with open(restart_flag_path, 'w') as f:
            f.write(f"Restart requested by {current_user.username} at {datetime.utcnow()}")

        return jsonify({'success': True, 'message': 'Bot restart initiated'})

    except Exception as e:
        logger.error(f"Error restarting Discord bot: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/discord/sync-roles', methods=['POST'])
@login_required
@admin_required
def admin_discord_sync_roles():
    """Sync all Discord roles"""
    try:
        # Get all users with Discord linked
        users_with_discord = User.query.filter(User.discord_id.isnot(None)).all()

        updated_count = 0
        for user in users_with_discord:
            # Queue Lux role update
            if user.is_lux_active():
                update_discord_role(user.discord_id, 'lux', True)
                updated_count += 1
            else:
                update_discord_role(user.discord_id, 'lux', False)

            # Queue Admin role update
            if user.is_admin:
                update_discord_role(user.discord_id, 'admin', True)
                updated_count += 1

        return jsonify({
            'success': True,
            'message': f'Role sync queued for {len(users_with_discord)} users',
            'users_updated': updated_count
        })

    except Exception as e:
        logger.error(f"Error syncing Discord roles: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/system/monitoring')
@login_required
@admin_required
def admin_system_monitoring():
    """Get system monitoring data"""
    try:
        import psutil

        # Get system resource usage
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        return jsonify({
            'success': True,
            'cpu_usage': f"{cpu_percent:.1f}%",
            'memory_usage': f"{memory.percent:.1f}%",
            'disk_usage': f"{disk.percent:.1f}%"
        })
    except ImportError:
        # Fallback if psutil is not available
        return jsonify({
            'success': True,
            'cpu_usage': "N/A",
            'memory_usage': "N/A",
            'disk_usage': "N/A"
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/system/logs')
@login_required
@admin_required
def admin_system_logs():
    """Get system logs"""
    try:
        filter_type = request.args.get('filter', 'all')

        # Simple log simulation - in production you'd read actual log files
        logs = [
            "[INFO] 2024-01-15 10:30:15 - System started successfully",
            "[INFO] 2024-01-15 10:30:16 - Database connection established",
            "[INFO] 2024-01-15 10:30:17 - Email server initialized",
            "[WARNING] 2024-01-15 10:35:22 - High memory usage detected",
            "[INFO] 2024-01-15 10:40:33 - User login: admin",
            "[ERROR] 2024-01-15 10:45:44 - Failed to send email notification",
            "[INFO] 2024-01-15 10:50:55 - File upload completed",
        ]

        if filter_type != 'all':
            logs = [log for log in logs if f"[{filter_type.upper()}]" in log]

        return jsonify({
            'success': True,
            'logs': '\n'.join(logs)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/system/maintenance', methods=['POST'])
@login_required
@admin_required
def admin_system_maintenance():
    """Handle system maintenance actions"""
    try:
        action = request.form.get('action')

        if action == 'backup':
            # Simulate backup creation
            return jsonify({
                'success': True,
                'message': 'Database backup created successfully'
            })
        elif action == 'optimize':
            # Simulate database optimization
            return jsonify({
                'success': True,
                'message': 'Database optimized successfully'
            })
        elif action == 'cleanup-db':
            # Simulate database cleanup
            return jsonify({
                'success': True,
                'message': 'Database cleanup completed'
            })
        elif action == 'cleanup-files':
            # Simulate file cleanup
            return jsonify({
                'success': True,
                'message': 'Orphaned files cleaned up'
            })
        elif action == 'disk-space':
            # Check disk space
            try:
                import psutil
                disk = psutil.disk_usage('/')
                return jsonify({
                    'success': True,
                    'message': f'Disk usage: {disk.percent:.1f}% ({disk.used // (1024**3)} GB used of {disk.total // (1024**3)} GB total)'
                })
            except:
                return jsonify({
                    'success': True,
                    'message': 'Disk space check completed'
                })
        elif action == 'clear-cache':
            # Simulate cache clearing
            return jsonify({
                'success': True,
                'message': 'System cache cleared'
            })
        elif action == 'restart-services':
            # Simulate service restart
            return jsonify({
                'success': True,
                'message': 'Services restarted successfully'
            })
        elif action == 'check-updates':
            # Simulate update check
            return jsonify({
                'success': True,
                'message': 'System is up to date'
            })
        elif action == 'security-scan':
            # Simulate security scan
            return jsonify({
                'success': True,
                'message': 'Security scan completed - no issues found'
            })
        elif action == 'audit-logs':
            # Simulate audit log generation
            return jsonify({
                'success': True,
                'message': 'Audit logs generated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Unknown action'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        })

@app.route('/admin/system/settings', methods=['POST'])
@login_required
@admin_required
def admin_system_settings():
    """Save system settings"""
    try:
        # In a real implementation, you'd save these to a database or config file
        settings = {
            'site_name': request.form.get('site_name'),
            'site_description': request.form.get('site_description'),
            'default_storage_limit': request.form.get('default_storage_limit'),
            'max_file_size': request.form.get('max_file_size'),
            'session_timeout': request.form.get('session_timeout'),
            'backup_retention': request.form.get('backup_retention'),
            'registration_enabled': request.form.get('registration_enabled') == 'on',
            'email_verification': request.form.get('email_verification') == 'on',
            'maintenance_mode': request.form.get('maintenance_mode') == 'on'
        }

        return jsonify({
            'success': True,
            'message': 'System settings saved successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Additional API routes for the new admin panel
@app.route('/admin/system/info')
@login_required
@admin_required
def admin_system_info():
    """Get system information for monitoring"""
    try:
        import psutil

        # Get system information
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Get uptime
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_days = uptime_hours // 24
        uptime_hours = uptime_hours % 24

        if uptime_days > 0:
            uptime_str = f"{uptime_days}d {uptime_hours}h"
        else:
            uptime_str = f"{uptime_hours}h"

        return jsonify({
            'success': True,
            'uptime': uptime_str,
            'cpu_usage': f"{cpu_percent}%",
            'memory_usage': f"{memory.percent}%",
            'disk_usage': f"{disk.percent}%",
            'active_connections': len(psutil.net_connections()),
            'load_average': ', '.join([f"{x:.2f}" for x in psutil.getloadavg()]) if hasattr(psutil, 'getloadavg') else 'N/A'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/analytics/file-types')
@login_required
@admin_required
def admin_analytics_file_types():
    """Get file type statistics"""
    try:
        # Get file type counts
        file_types = db.session.query(
            FileUpload.file_type,
            db.func.count(FileUpload.id).label('count')
        ).group_by(FileUpload.file_type).order_by(db.desc('count')).limit(10).all()

        return jsonify({
            'success': True,
            'file_types': [{'extension': ft.file_type, 'count': ft.count} for ft in file_types]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/analytics/recent-activity')
@login_required
@admin_required
def admin_analytics_recent_activity():
    """Get recent activity"""
    try:
        activities = []

        # Recent user registrations
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
        for user in recent_users:
            activities.append({
                'description': f"New user registered: {user.username}",
                'time': user.created_at.strftime('%Y-%m-%d %H:%M'),
                'icon': 'user-plus',
                'color': 'blue'
            })

        # Recent file uploads
        recent_files = FileUpload.query.order_by(FileUpload.upload_date.desc()).limit(5).all()
        for file in recent_files:
            activities.append({
                'description': f"File uploaded: {file.original_filename}",
                'time': file.upload_date.strftime('%Y-%m-%d %H:%M'),
                'icon': 'upload',
                'color': 'green'
            })

        # Sort by time and limit
        activities.sort(key=lambda x: x['time'], reverse=True)
        activities = activities[:10]

        return jsonify({
            'success': True,
            'activities': activities
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/system/cleanup', methods=['POST'])
@login_required
@admin_required
def admin_system_cleanup():
    """Run system cleanup"""
    try:
        # Implement cleanup logic here
        # For now, just return success
        return jsonify({
            'success': True,
            'message': 'System cleanup completed successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Cleanup failed: {str(e)}'
        })

@app.route('/admin/system/backup', methods=['POST'])
@login_required
@admin_required
def admin_system_backup():
    """Create system backup"""
    try:
        # Implement backup logic here
        return jsonify({
            'success': True,
            'message': 'Backup created successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Backup failed: {str(e)}'
        })

@app.route('/admin/system/cache/clear', methods=['POST'])
@login_required
@admin_required
def admin_clear_cache():
    """Clear system cache"""
    try:
        # Implement cache clearing logic here
        return jsonify({
            'success': True,
            'message': 'Cache cleared successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Cache clear failed: {str(e)}'
        })

@app.route('/admin/user/<int:user_id>')
@login_required
@admin_required
def admin_user_detail(user_id):
    user = User.query.get_or_404(user_id)
    return render_template('admin/user_detail.html', user=user, messages=messages)

@app.route('/admin/user/<int:user_id>/update', methods=['POST'])
@login_required
@admin_required
def admin_update_user(user_id):
    user = User.query.get_or_404(user_id)
    
    # Don't allow admins to modify their own admin status
    if user.id == current_user.id and 'is_admin' in request.form:
        flash('You cannot change your own admin status')
        return redirect(url_for('admin_user_detail', user_id=user_id))
    
    if 'is_admin' in request.form:
        user.is_admin = request.form.get('is_admin') == 'on'
    
    if 'is_moderator' in request.form:
        user.is_moderator = request.form.get('is_moderator') == 'on'
    
    if 'is_banned' in request.form:
        user.is_banned = request.form.get('is_banned') == 'on'
    
    if 'upload_limit_mb' in request.form:
        try:
            user.upload_limit_mb = int(request.form.get('upload_limit_mb'))
        except ValueError:
            flash('Invalid upload limit value')
    
    if 'max_projects' in request.form:
        try:
            user.max_projects = int(request.form.get('max_projects'))
        except ValueError:
            flash('Invalid max projects value')
    
    if 'max_keys_per_project' in request.form:
        try:
            user.max_keys_per_project = int(request.form.get('max_keys_per_project'))
        except ValueError:
            flash('Invalid max keys per project value')

    # Handle Lux Premium status
    old_lux_status = user.is_lux_active()  # Store old status for Discord sync

    if 'is_lux' in request.form:
        user.is_lux = request.form.get('is_lux') == 'on'

        # Handle Lux expiration
        if user.is_lux and 'lux_expires_at' in request.form:
            lux_expires_str = request.form.get('lux_expires_at')
            if lux_expires_str:
                try:
                    user.lux_expires_at = datetime.strptime(lux_expires_str, '%Y-%m-%dT%H:%M')
                except ValueError:
                    flash('Invalid Lux expiration date format')
            else:
                user.lux_expires_at = None  # Permanent
        elif not user.is_lux:
            user.lux_expires_at = None

    db.session.commit()

    # Update Discord role if Lux status changed and user has Discord linked
    new_lux_status = user.is_lux_active()
    if old_lux_status != new_lux_status and user.discord_id:
        update_discord_role(user.discord_id, 'lux', new_lux_status)
        if new_lux_status:
            flash('User updated successfully and Discord Lux role granted!')
        else:
            flash('User updated successfully and Discord Lux role removed!')
    else:
        flash('User updated successfully')
    return redirect(url_for('admin_user_detail', user_id=user_id))

@app.route('/admin/user/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_user(user_id):
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('You cannot delete your own account')
        return redirect(url_for('admin_dashboard'))

    # Delete user's files
    for file in user.files:
        try:
            os.remove(os.path.join(app.config['UPLOAD_FOLDER'], file.filename))
        except:
            pass

    # Delete user's projects and licenses
    for project in user.projects:
        License.query.filter_by(project_id=project.project_id).delete()

    # Delete the user
    db.session.delete(user)
    db.session.commit()

    flash('User and all associated data deleted successfully')
    return redirect(url_for('admin_dashboard'))

@app.route('/admin/users/bulk', methods=['POST'])
@login_required
@admin_required
def admin_bulk_action():
    action = request.form.get('action')
    user_ids = request.form.getlist('user_ids')

    if not user_ids:
        flash('No users selected')
        return redirect(url_for('admin_dashboard'))

    users = User.query.filter(User.id.in_(user_ids)).all()

    if action == 'ban':
        for user in users:
            if user.id != current_user.id:
                user.is_banned = True
        db.session.commit()
        flash(f'Banned {len([u for u in users if u.id != current_user.id])} users')

    elif action == 'unban':
        for user in users:
            user.is_banned = False
        db.session.commit()
        flash(f'Unbanned {len(users)} users')

    elif action == 'make_admin':
        for user in users:
            user.is_admin = True
        db.session.commit()
        flash(f'Made {len(users)} users admins')

    elif action == 'remove_admin':
        for user in users:
            if user.id != current_user.id:
                user.is_admin = False
        db.session.commit()
        flash(f'Removed admin from {len([u for u in users if u.id != current_user.id])} users')

    elif action == 'delete':
        deleted_count = 0
        for user in users:
            if user.id != current_user.id:
                # Delete user's files
                for file in user.files:
                    try:
                        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], file.filename))
                    except:
                        pass

                # Delete user's projects and licenses
                for project in user.projects:
                    License.query.filter_by(project_id=project.project_id).delete()

                db.session.delete(user)
                deleted_count += 1

        db.session.commit()
        flash(f'Deleted {deleted_count} users')

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/bot-settings')
@login_required
@admin_required
def admin_bot_settings():
    """Discord bot settings page"""
    settings = BotSettings.get_settings()
    form = BotSettingsForm()

    # Populate form with current settings
    if settings:
        form.embed_color.data = settings.embed_color
        form.global_footer.data = settings.global_footer
        form.status.data = settings.status
        form.activity_type.data = settings.activity_type
        form.activity_text.data = settings.activity_text
        form.activity_url.data = settings.activity_url
        form.bot_name.data = settings.bot_name
        form.bot_avatar_url.data = settings.bot_avatar_url

    return render_template('admin/bot_settings.html', form=form, settings=settings, messages=messages)

@app.route('/admin/bot-settings/update', methods=['POST'])
@login_required
@admin_required
def admin_update_bot_settings():
    """Update Discord bot settings"""
    settings = BotSettings.get_settings()
    form = BotSettingsForm()

    if form.validate_on_submit():
        # Update settings from form
        settings.embed_color = form.embed_color.data
        settings.global_footer = form.global_footer.data
        settings.status = form.status.data
        settings.activity_type = form.activity_type.data
        settings.activity_text = form.activity_text.data
        settings.activity_url = form.activity_url.data if form.activity_url.data else None
        settings.bot_name = form.bot_name.data if form.bot_name.data else None
        settings.bot_avatar_url = form.bot_avatar_url.data if form.bot_avatar_url.data else None
        settings.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Discord bot settings updated successfully')

        # Try to update the bot if it's running
        try:
            from discord_bot.database import DatabaseManager
            # The bot will pick up the new settings on next restart or command
            flash('Settings saved. Bot will apply changes on next restart or command.')
        except:
            pass

        return redirect(url_for('admin_bot_settings'))
    else:
        # Form validation failed
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Error in {field}: {error}')
        return redirect(url_for('admin_bot_settings'))

@app.route('/admin/bot-settings/reset', methods=['POST'])
@login_required
@admin_required
def admin_reset_bot_settings():
    """Reset Discord bot settings to defaults"""
    settings = BotSettings.get_settings()

    # Reset to defaults
    settings.embed_color = '#00ff00'
    settings.global_footer = 'LXND License Management System'
    settings.status = 'online'
    settings.activity_type = 'watching'
    settings.activity_text = 'LXND License System'
    settings.activity_url = None
    settings.bot_name = None
    settings.bot_avatar_url = None
    settings.updated_at = datetime.utcnow()

    db.session.commit()
    flash('Discord bot settings reset to defaults')
    return redirect(url_for('admin_bot_settings'))

@app.route('/admin/bot-settings/restart', methods=['POST'])
@login_required
@admin_required
def admin_restart_bot():
    """Restart Discord bot"""
    try:
        # Create a restart flag file that the bot can check
        restart_flag_path = os.path.join(os.getcwd(), 'discord_bot', 'restart_flag.txt')
        with open(restart_flag_path, 'w') as f:
            f.write(f"Restart requested by {current_user.username} at {datetime.utcnow()}")

        # Try to send a signal to refresh bot settings
        try:
            from discord_bot.database import DatabaseManager
            # The bot will check for the restart flag and restart itself
            flash('Bot restart signal sent successfully. The bot will restart shortly and apply new settings.')
        except ImportError:
            flash('Bot restart signal sent. If the bot is running, it will restart shortly.')

    except Exception as e:
        flash(f'Error sending restart signal: {str(e)}')

    return redirect(url_for('admin_bot_settings'))

@app.route('/admin/bot-settings/status')
@login_required
@admin_required
def admin_bot_status():
    """Get Discord bot status"""
    try:
        # Check if restart flag exists
        restart_flag_path = os.path.join(os.getcwd(), 'discord_bot', 'restart_flag.txt')
        if os.path.exists(restart_flag_path):
            # Check if restart flag is old (more than 2 minutes)
            import time
            flag_age = time.time() - os.path.getmtime(restart_flag_path)
            if flag_age > 120:  # 2 minutes
                # Remove stale restart flag
                os.remove(restart_flag_path)
                return jsonify({
                    'status': 'offline',
                    'message': 'Bot restart timed out - flag cleared'
                })
            else:
                return jsonify({
                    'status': 'restarting',
                    'message': f'Bot is restarting... ({int(120 - flag_age)}s remaining)'
                })

        # Check bot process status
        process_status = get_bot_process_status()

        if process_status['status'] == 'running':
            return jsonify({
                'status': 'online',
                'message': f'Bot is running (PID: {process_status["pid"]})',
                'pid': process_status['pid'],
                'memory_mb': process_status.get('memory_mb'),
                'cpu_percent': process_status.get('cpu_percent')
            })
        elif process_status['status'] == 'stopped':
            return jsonify({
                'status': 'offline',
                'message': 'Bot is not running'
            })
        else:
            return jsonify({
                'status': 'unknown',
                'message': 'Unable to determine bot status'
            })

    except Exception as e:
        return jsonify({
            'status': 'unknown',
            'message': f'Unable to determine bot status: {str(e)}'
        })

@app.route('/admin/bot-settings/clear-restart-flag', methods=['POST'])
@login_required
@admin_required
def admin_clear_restart_flag():
    """Clear the restart flag manually"""
    try:
        restart_flag_path = os.path.join(os.getcwd(), 'discord_bot', 'restart_flag.txt')
        if os.path.exists(restart_flag_path):
            os.remove(restart_flag_path)
            flash('Restart flag cleared successfully')
        else:
            flash('No restart flag found')
    except Exception as e:
        flash(f'Error clearing restart flag: {str(e)}')

    return redirect(url_for('admin_bot_settings'))

@app.route('/admin/bot-settings/start', methods=['POST'])
@login_required
@admin_required
def admin_start_bot():
    """Start Discord bot"""
    try:
        status = get_bot_process_status()
        if status['status'] == 'running':
            flash('Discord bot is already running')
        else:
            start_discord_bot()
            flash('Discord bot start command sent')
    except Exception as e:
        flash(f'Error starting bot: {str(e)}')

    return redirect(url_for('admin_bot_settings'))

@app.route('/admin/bot-settings/stop', methods=['POST'])
@login_required
@admin_required
def admin_stop_bot():
    """Stop Discord bot"""
    try:
        status = get_bot_process_status()
        if status['status'] == 'stopped':
            flash('Discord bot is not running')
        else:
            stop_discord_bot()
            flash('Discord bot stop command sent')
    except Exception as e:
        flash(f'Error stopping bot: {str(e)}')

    return redirect(url_for('admin_bot_settings'))

# Email Management Routes (duplicate removed - using the one in New Admin Routes section)

@app.route('/admin/email/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_email_settings():
    """Email server settings"""
    settings = EmailSettings.get_settings()
    form = EmailSettingsForm()

    if request.method == 'GET':
        # Populate form with current settings
        form.smtp_server.data = settings.smtp_server
        form.smtp_port.data = settings.smtp_port
        form.smtp_use_tls.data = 'true' if settings.smtp_use_tls else 'false'
        form.smtp_use_ssl.data = 'true' if settings.smtp_use_ssl else 'false'
        form.imap_server.data = settings.imap_server
        form.imap_port.data = settings.imap_port
        form.imap_use_ssl.data = 'true' if settings.imap_use_ssl else 'false'
        form.email_domain.data = settings.email_domain
        form.default_storage_gb.data = settings.default_storage_gb
        form.max_attachment_mb.data = settings.max_attachment_mb

    if form.validate_on_submit():
        # Update settings
        settings.smtp_server = form.smtp_server.data
        settings.smtp_port = form.smtp_port.data
        settings.smtp_use_tls = form.smtp_use_tls.data == 'true'
        settings.smtp_use_ssl = form.smtp_use_ssl.data == 'true'
        settings.imap_server = form.imap_server.data
        settings.imap_port = form.imap_port.data
        settings.imap_use_ssl = form.imap_use_ssl.data == 'true'
        settings.email_domain = form.email_domain.data
        settings.default_storage_gb = form.default_storage_gb.data
        settings.max_attachment_mb = form.max_attachment_mb.data
        settings.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Email settings updated successfully')
        return redirect(url_for('admin_email_settings'))

    return render_template('admin/email_settings.html', form=form, settings=settings, messages=messages)

@app.route('/admin/email/recalculate-storage')
@login_required
@admin_required
def admin_recalculate_storage():
    """Recalculate storage for all email accounts"""
    try:
        accounts = EmailAccount.query.all()
        updated_count = 0

        for account in accounts:
            old_storage = account.storage_used_mb
            account.recalculate_storage()
            if old_storage != account.storage_used_mb:
                updated_count += 1

        db.session.commit()
        flash(f'Storage recalculated for {updated_count} accounts', 'success')

    except Exception as e:
        flash(f'Error recalculating storage: {e}', 'error')

    return redirect(url_for('admin_email_accounts'))

@app.route('/admin/email/accounts')
@login_required
@admin_required
def admin_email_accounts():
    """Manage email accounts"""
    accounts = EmailAccount.query.all()
    return render_template('admin/email_accounts.html', accounts=accounts, messages=messages)

@app.route('/admin/email/accounts/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_email_account():
    """Create new email account"""
    form = EmailAccountForm()

    if form.validate_on_submit():
        # Check if email already exists
        existing = EmailAccount.query.filter_by(email_address=form.email_address.data).first()
        if existing:
            flash('Email address already exists')
            return render_template('admin/create_email_account.html', form=form, messages=messages)

        # Create new account
        account = EmailAccount(
            user_id=current_user.id,  # Admin creates for themselves initially
            email_address=form.email_address.data,
            display_name=form.display_name.data,
            storage_limit_mb=form.storage_limit_gb.data * 1024,
            is_verified=True  # Admin-created accounts are pre-verified
        )
        account.set_password(form.password.data)

        db.session.add(account)
        db.session.commit()

        flash(f'Email account {form.email_address.data} created successfully')
        return redirect(url_for('admin_email_accounts'))

    return render_template('admin/create_email_account.html', form=form, messages=messages)

@app.route('/admin/email/accounts/<int:account_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_email_account(account_id):
    """Delete email account"""
    account = EmailAccount.query.get_or_404(account_id)

    # Delete all messages first
    EmailMessage.query.filter_by(account_id=account_id).delete()

    # Delete account
    db.session.delete(account)
    db.session.commit()

    flash(f'Email account {account.email_address} deleted successfully')
    return redirect(url_for('admin_email_accounts'))

# Alternative URL patterns for email account management
@app.route('/admin/email/account/<int:account_id>')
@login_required
@admin_required
def admin_view_email_account(account_id):
    """View email account details"""
    account = EmailAccount.query.get_or_404(account_id)
    user = account.get_user()

    # Get recent messages
    recent_messages = EmailMessage.query.filter_by(account_id=account_id)\
                                       .order_by(EmailMessage.received_at.desc())\
                                       .limit(10).all()

    return render_template('admin/view_email_account.html',
                         account=account,
                         user=user,
                         recent_messages=recent_messages,
                         messages=messages)

@app.route('/admin/email/account/<int:account_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit_email_account(account_id):
    """Edit email account"""
    account = EmailAccount.query.get_or_404(account_id)
    form = EmailAccountForm(obj=account)

    if form.validate_on_submit():
        account.email_address = form.email_address.data
        account.display_name = form.display_name.data
        account.storage_limit_mb = form.storage_limit_mb.data
        account.is_active = form.is_active.data

        if form.password.data:
            account.set_password(form.password.data)

        db.session.commit()
        flash(f'Email account {account.email_address} updated successfully')
        return redirect(url_for('admin_view_email_account', account_id=account_id))

    return render_template('admin/edit_email_account.html', form=form, account=account, messages=messages)

@app.route('/admin/email/account/new', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_email_account_alt():
    """Alternative URL for creating new email account"""
    return admin_create_email_account()

@app.route('/admin/email/account/<int:account_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_email_account_alt(account_id):
    """Alternative URL for deleting email account"""
    return admin_delete_email_account(account_id)

@app.route('/admin/email/account/<int:account_id>/reset-password', methods=['POST'])
@login_required
@admin_required
def admin_reset_email_password(account_id):
    """Reset email account password"""
    account = EmailAccount.query.get_or_404(account_id)

    # Generate new random password
    import secrets
    import string
    new_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))

    # Update password
    account.set_password(new_password)
    db.session.commit()

    return jsonify({
        'success': True,
        'new_password': new_password,
        'message': f'Password reset for {account.email_address}'
    })









@app.route('/admin/email/notifications', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_email_notifications():
    """Admin email notification templates"""
    form = EmailNotificationForm()

    # Get all saved templates for the template selector
    saved_templates = EmailTemplate.query.order_by(EmailTemplate.created_at.desc()).all()

    if form.validate_on_submit():
        try:
            # Parse recipients
            recipients = [email.strip() for email in form.recipients.data.split('\n') if email.strip()]

            if not recipients:
                flash('No valid recipients found')
                return render_template('admin/email_notifications.html', form=form, messages=messages)

            # Get admin email account for sending
            admin_account = EmailAccount.query.filter_by(email_address='<EMAIL>').first()
            if not admin_account:
                flash('Admin email account not found. <NAME_EMAIL> first.')
                return render_template('admin/email_notifications.html', form=form, messages=messages)

            # Template variables for replacement
            template_vars = {
                'site_name': 'LXND Cloud',
                'admin_email': '<EMAIL>',
                'support_url': 'https://lxnd.cloud/support',
                'current_date': datetime.now().strftime('%Y-%m-%d'),
                'current_year': datetime.now().year
            }

            # Process each recipient
            sent_count = 0
            failed_count = 0

            for recipient_email in recipients:
                try:
                    # Add recipient-specific variables
                    recipient_vars = template_vars.copy()
                    recipient_vars['user_email'] = recipient_email
                    recipient_vars['user_name'] = recipient_email.split('@')[0]  # Use email prefix as name

                    # Replace placeholders in subject and body
                    processed_subject = form.subject.data
                    processed_body_text = form.body_text.data
                    processed_body_html = form.body_html.data if form.body_html.data else None

                    for var_name, var_value in recipient_vars.items():
                        placeholder = f'{{{{{var_name}}}}}'
                        processed_subject = processed_subject.replace(placeholder, str(var_value))
                        processed_body_text = processed_body_text.replace(placeholder, str(var_value))
                        if processed_body_html:
                            processed_body_html = processed_body_html.replace(placeholder, str(var_value))

                    # Send email
                    email_service = EmailService()
                    success, message = email_service.send_email(
                        from_account=admin_account,
                        to_addresses=[recipient_email],
                        subject=processed_subject,
                        body_text=processed_body_text,
                        body_html=processed_body_html
                    )

                    if success:
                        sent_count += 1
                        logger.info(f"Notification sent to {recipient_email}")
                    else:
                        failed_count += 1
                        logger.error(f"Failed to send notification to {recipient_email}: {message}")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error sending to {recipient_email}: {e}")

            # Show results
            if sent_count > 0:
                flash(f'Successfully sent {sent_count} notification(s)')
            if failed_count > 0:
                flash(f'Failed to send {failed_count} notification(s)', 'error')

            return redirect(url_for('admin_email_notifications'))

        except Exception as e:
            logger.error(f"Error in email notifications: {e}")
            flash(f'Error sending notifications: {e}', 'error')

    return render_template('admin/email_notifications.html', form=form, messages=messages, saved_templates=saved_templates)

# Integrated Mailserver Management Routes
@app.route('/admin/email/mailserver')
@login_required
@admin_required
def admin_mailserver():
    """Integrated mailserver management dashboard"""
    try:
        import mailserver
        status = mailserver.get_mailserver_status()
        return render_template('admin/mailserver.html', status=status, messages=messages)
    except ImportError:
        flash('Integrated mailserver module not available')
        return redirect(url_for('admin_email_dashboard'))

@app.route('/admin/email/mailserver/start', methods=['POST'])
@login_required
@admin_required
def admin_start_mailserver():
    """Start the integrated mailserver"""
    try:
        import mailserver
        success, message = mailserver.start_mailserver()
        if success:
            flash('Integrated mailserver started successfully')
        else:
            flash(f'Failed to start mailserver: {message}', 'error')
    except ImportError:
        flash('Integrated mailserver module not available', 'error')
    except Exception as e:
        flash(f'Error starting mailserver: {str(e)}', 'error')

    return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/stop', methods=['POST'])
@login_required
@admin_required
def admin_stop_mailserver():
    """Stop the integrated mailserver"""
    try:
        import mailserver
        success, message = mailserver.stop_mailserver()
        if success:
            flash('Integrated mailserver stopped successfully')
        else:
            flash(f'Failed to stop mailserver: {message}', 'error')
    except ImportError:
        flash('Integrated mailserver module not available', 'error')
    except Exception as e:
        flash(f'Error stopping mailserver: {str(e)}', 'error')

    return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/status')
@login_required
@admin_required
def admin_mailserver_status():
    """Get mailserver status (API endpoint)"""
    try:
        import mailserver
        status = mailserver.get_mailserver_status()
        return jsonify(status)
    except ImportError:
        return jsonify({'error': 'Mailserver module not available', 'running': False}), 500
    except Exception as e:
        return jsonify({'error': str(e), 'running': False}), 500

@app.route('/admin/email/mailserver/dns')
@login_required
@admin_required
def admin_mailserver_dns():
    """DNS configuration for mailserver"""
    try:
        import mailserver
        dns_records = mailserver.get_dns_records_for_domain()
        return render_template('admin/mailserver_dns.html', dns_records=dns_records, messages=messages)
    except ImportError:
        flash('Mailserver module not available', 'error')
        return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/generate-dkim', methods=['POST'])
@login_required
@admin_required
def admin_generate_dkim():
    """Generate DKIM keys"""
    try:
        import mailserver
        success, result = mailserver.generate_dkim_keys()
        if success:
            flash('DKIM keys generated successfully')
        else:
            flash(f'Failed to generate DKIM keys: {result}', 'error')
    except ImportError:
        flash('Mailserver module not available', 'error')
    except Exception as e:
        flash(f'Error generating DKIM keys: {str(e)}', 'error')

    return redirect(url_for('admin_mailserver_dns'))

@app.route('/admin/email/mailserver/test-receiving')
@login_required
@admin_required
def admin_test_email_receiving():
    """Test email receiving functionality"""
    try:
        import mailserver
        success, message = mailserver.test_email_receiving()

        # Get recent emails for debugging
        recent_emails = mailserver.get_recent_emails(5)

        return render_template('admin/mailserver_test.html',
                             test_result={'success': success, 'message': message},
                             recent_emails=recent_emails,
                             messages=messages)
    except ImportError:
        flash('Mailserver module not available', 'error')
        return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/restart-port25', methods=['POST'])
@login_required
@admin_required
def admin_restart_mailserver_port25():
    """Restart mailserver on port 25 for external email receiving"""
    try:
        import mailserver

        # Stop current mailserver
        mailserver.stop_mailserver()

        # Start on port 25
        success, message = mailserver.start_mailserver(port=25, use_tls=True)

        if success:
            flash('Mailserver restarted on port 25 for external email receiving')
        else:
            flash(f'Failed to restart on port 25: {message}', 'error')
    except ImportError:
        flash('Mailserver module not available', 'error')
    except Exception as e:
        flash(f'Error restarting mailserver: {str(e)}', 'error')

    return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/test-dns')
@login_required
@admin_required
def admin_test_dns():
    """Test DNS configuration for email domain"""
    try:
        import mailserver

        # Test DNS configuration
        dns_results = mailserver.test_dns_configuration()

        # Test port connectivity
        port_accessible, port_message = mailserver.check_port_connectivity()

        return render_template('admin/mailserver_dns_test.html',
                             dns_results=dns_results,
                             port_test={'accessible': port_accessible, 'message': port_message},
                             messages=messages)
    except ImportError:
        flash('Mailserver module not available', 'error')
        return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/test-dns-custom', methods=['POST'])
@login_required
@admin_required
def admin_test_dns_custom():
    """Test DNS configuration for custom domain"""
    try:
        import mailserver

        domain = request.form.get('domain', '').strip()
        if not domain:
            flash('Please enter a domain name', 'error')
            return redirect(url_for('admin_test_dns'))

        # Test DNS configuration for custom domain
        dns_results = mailserver.test_dns_configuration(domain)

        # Test port connectivity
        port_accessible, port_message = mailserver.check_port_connectivity()

        return render_template('admin/mailserver_dns_test.html',
                             dns_results=dns_results,
                             port_test={'accessible': port_accessible, 'message': port_message},
                             tested_domain=domain,
                             messages=messages)
    except ImportError:
        flash('Mailserver module not available', 'error')
        return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/mailserver/diagnose')
@login_required
@admin_required
def admin_diagnose_email_receiving():
    """Diagnose email receiving issues"""
    try:
        import mailserver

        # Run comprehensive diagnosis
        diagnosis = mailserver.diagnose_email_receiving_issues()

        return render_template('admin/mailserver_diagnosis.html',
                             diagnosis=diagnosis,
                             messages=messages)
    except ImportError:
        flash('Mailserver module not available', 'error')
        return redirect(url_for('admin_mailserver'))

@app.route('/admin/email/load_template/<int:template_id>', methods=['GET'])
@login_required
@admin_required
def admin_load_template_to_notification(template_id):
    """Load a template into the notification form"""
    template = EmailTemplate.query.get_or_404(template_id)

    # Redirect to the notification page with template_id parameter
    return redirect(url_for('admin_email_notifications', template_id=template.id))

@app.route('/admin/email/templates')
@login_required
@admin_required
def admin_email_templates():
    """Manage email templates"""
    templates = EmailTemplate.query.order_by(EmailTemplate.created_at.desc()).all()
    return render_template('admin/email_templates.html', templates=templates, messages=messages)

@app.route('/admin/email/templates/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_email_template():
    """Create new email template"""
    form = EmailTemplateForm()

    if form.validate_on_submit():
        # Check if template name already exists
        existing = EmailTemplate.query.filter_by(name=form.name.data).first()
        if existing:
            flash('Template name already exists')
            return render_template('admin/create_email_template.html', form=form, messages=messages)

        # If this is a welcome template, deactivate other welcome templates
        if form.template_type.data == 'welcome' and form.is_active.data:
            EmailTemplate.query.filter_by(template_type='welcome', is_active=True).update({'is_active': False})

        # Create new template
        template = EmailTemplate(
            name=form.name.data,
            description=form.description.data,
            template_type=form.template_type.data,
            subject=form.subject.data,
            body_text=form.body_text.data,
            body_html=form.body_html.data,
            is_active=form.is_active.data
        )

        db.session.add(template)
        db.session.commit()

        flash(f'Email template "{form.name.data}" created successfully')
        return redirect(url_for('admin_email_templates'))

    return render_template('admin/create_email_template.html', form=form, messages=messages)

@app.route('/admin/email/templates/<int:template_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit_email_template(template_id):
    """Edit email template"""
    template = EmailTemplate.query.get_or_404(template_id)
    form = EmailTemplateForm(obj=template)

    if form.validate_on_submit():
        # Check if template name already exists (excluding current template)
        existing = EmailTemplate.query.filter(
            EmailTemplate.name == form.name.data,
            EmailTemplate.id != template_id
        ).first()
        if existing:
            flash('Template name already exists')
            return render_template('admin/edit_email_template.html', form=form, template=template, messages=messages)

        # If this is a welcome template, deactivate other welcome templates
        if form.template_type.data == 'welcome' and form.is_active.data:
            EmailTemplate.query.filter(
                EmailTemplate.template_type == 'welcome',
                EmailTemplate.is_active == True,
                EmailTemplate.id != template_id
            ).update({'is_active': False})

        # Update template
        template.name = form.name.data
        template.description = form.description.data
        template.template_type = form.template_type.data
        template.subject = form.subject.data
        template.body_text = form.body_text.data
        template.body_html = form.body_html.data
        template.is_active = form.is_active.data
        template.updated_at = datetime.utcnow()

        db.session.commit()

        flash(f'Email template "{template.name}" updated successfully')
        return redirect(url_for('admin_email_templates'))

    return render_template('admin/edit_email_template.html', form=form, template=template, messages=messages)

@app.route('/admin/email/templates/<int:template_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_email_template(template_id):
    """Delete email template"""
    template = EmailTemplate.query.get_or_404(template_id)
    template_name = template.name

    db.session.delete(template)
    db.session.commit()

    flash(f'Email template "{template_name}" deleted successfully')
    return redirect(url_for('admin_email_templates'))

@app.route('/admin/email/templates/<int:template_id>/preview')
@login_required
@admin_required
def admin_preview_email_template(template_id):
    """Preview email template with sample data"""
    template = EmailTemplate.query.get_or_404(template_id)

    # Sample variables for preview
    sample_vars = {
        'user_name': 'John Doe',
        'user_email': '<EMAIL>',
        'site_name': 'LXND',
        'admin_email': '<EMAIL>',
        'support_url': 'https://lxnd.cloud/support',
        'current_date': datetime.utcnow().strftime('%Y-%m-%d')
    }

    rendered = template.render_template(sample_vars)

    return render_template('admin/preview_email_template.html',
                         template=template,
                         rendered=rendered,
                         sample_vars=sample_vars,
                         messages=messages)

# Alternative URL patterns for template management
@app.route('/admin/email/template/<int:template_id>/preview')
@login_required
@admin_required
def admin_preview_email_template_alt(template_id):
    """Alternative URL for template preview"""
    return admin_preview_email_template(template_id)

@app.route('/email/template/<int:template_id>/edit')
@login_required
@admin_required
def admin_edit_email_template_alt(template_id):
    """Alternative URL for template editing"""
    return admin_edit_email_template(template_id)

@app.route('/admin/email/template/new')
@login_required
@admin_required
def admin_create_email_template_alt():
    """Alternative URL for creating new template"""
    return admin_create_email_template()



# Email API Endpoints
@app.route('/api/email/send', methods=['POST'])
@login_required
def api_send_email():
    """API endpoint to send email"""
    try:
        data = request.get_json()

        # Get sender account
        account_id = data.get('account_id')
        account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
        if not account:
            return jsonify({'error': 'Email account not found'}), 404

        # Validate required fields
        to_addresses = data.get('to', [])
        if isinstance(to_addresses, str):
            to_addresses = [to_addresses]

        if not to_addresses:
            return jsonify({'error': 'Recipients required'}), 400

        subject = data.get('subject', '')
        body_text = data.get('body_text', '')
        body_html = data.get('body_html', '')

        if not (body_text or body_html):
            return jsonify({'error': 'Email body required'}), 400

        # Send email
        email_service = EmailService()
        success, message = email_service.send_email(
            from_account=account,
            to_addresses=to_addresses,
            subject=subject,
            body_text=body_text,
            body_html=body_html,
            attachments=data.get('attachments', [])
        )

        if success:
            return jsonify({'message': 'Email sent successfully'})
        else:
            return jsonify({'error': message}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/email/check/<int:account_id>', methods=['POST'])
@login_required
def api_check_email(account_id):
    """API endpoint to check for new emails"""
    try:
        # Get account
        account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
        if not account:
            return jsonify({'error': 'Email account not found'}), 404

        # Check for new emails from external server
        email_service = EmailService()
        success, result = email_service.check_email(account)

        if success:
            new_count = len(result) if isinstance(result, list) else 0
            return jsonify({
                'message': f'Email check completed. Found {new_count} new messages.',
                'new_messages': new_count
            })
        else:
            return jsonify({'error': str(result)}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/email/messages/<int:account_id>')
@login_required
def api_get_messages(account_id):
    """API endpoint to get email messages"""
    try:
        # Get account
        account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first()
        if not account:
            return jsonify({'error': 'Email account not found'}), 404

        # Get messages
        folder = request.args.get('folder', 'INBOX')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        messages = EmailMessage.query.filter_by(
            account_id=account_id,
            folder=folder,
            is_deleted=False
        ).order_by(EmailMessage.received_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'messages': [{
                'id': msg.id,
                'subject': msg.subject,
                'sender': msg.sender,
                'recipients': msg.get_recipients_list(),
                'body_text': msg.body_text,
                'body_html': msg.body_html,
                'attachments': msg.get_attachments_list(),
                'is_read': msg.is_read,
                'is_starred': msg.is_starred,
                'size_mb': msg.get_size_mb(),
                'sent_at': msg.sent_at.isoformat() if msg.sent_at else None,
                'received_at': msg.received_at.isoformat()
            } for msg in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/email/message/<int:message_id>/read', methods=['POST'])
@login_required
def api_mark_read(message_id):
    """API endpoint to mark message as read"""
    try:
        message = EmailMessage.query.join(EmailAccount).filter(
            EmailMessage.id == message_id,
            EmailAccount.user_id == current_user.id
        ).first()

        if not message:
            return jsonify({'error': 'Message not found'}), 404

        message.is_read = True
        db.session.commit()

        return jsonify({'message': 'Message marked as read'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/email/message/<int:message_id>/star', methods=['POST'])
@login_required
def api_toggle_star(message_id):
    """API endpoint to toggle message star"""
    try:
        message = EmailMessage.query.join(EmailAccount).filter(
            EmailMessage.id == message_id,
            EmailAccount.user_id == current_user.id
        ).first()

        if not message:
            return jsonify({'error': 'Message not found'}), 404

        message.is_starred = not message.is_starred
        db.session.commit()

        return jsonify({
            'message': 'Message starred' if message.is_starred else 'Message unstarred',
            'is_starred': message.is_starred
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# User Email Routes
@app.route('/email')
@login_required
def user_email_dashboard():
    """User email dashboard"""
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()

    # If user has no email account, redirect to create one
    if not email_accounts:
        return redirect(url_for('user_create_email_account'))

    # Get account limits for display
    current_count = len(email_accounts)
    max_accounts = current_user.get_max_email_accounts()
    can_create_more = current_user.can_create_email_account()

    # Format max accounts for display
    max_accounts_display = "∞" if max_accounts == float('inf') else int(max_accounts)
    is_unlimited = max_accounts == float('inf')

    # Show the email dashboard instead of redirecting to inbox
    return render_template('email/dashboard.html',
                         email_accounts=email_accounts,
                         current_count=current_count,
                         max_accounts=max_accounts,
                         max_accounts_display=max_accounts_display,
                         is_unlimited=is_unlimited,
                         can_create_more=can_create_more,
                         messages=messages)

@app.route('/email/compose', methods=['GET', 'POST'])
@login_required
def user_compose_email():
    """Compose new email"""
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()
    form = ComposeEmailForm()

    # Set choices for from_account field
    form.from_account.choices = [(account.id, account.email_address) for account in email_accounts]

    if form.validate_on_submit():
        # Get sender account
        from_account = EmailAccount.query.filter_by(
            id=form.from_account.data,
            user_id=current_user.id
        ).first()

        if not from_account:
            flash('Invalid sender account selected', 'error')
            return render_template('email/compose.html', form=form, email_accounts=email_accounts, messages=messages)

        # Parse recipients
        to_addresses = [addr.strip() for addr in form.to.data.split(',') if addr.strip()]
        cc_addresses = [addr.strip() for addr in form.cc.data.split(',') if addr.strip() and form.cc.data]
        bcc_addresses = [addr.strip() for addr in form.bcc.data.split(',') if addr.strip() and form.bcc.data]

        # Handle file attachments
        attachments = []
        if form.attachments.data:
            for file in request.files.getlist('attachments'):
                if file and file.filename:
                    # Check file size (25MB limit)
                    file.seek(0, 2)  # Seek to end
                    file_size = file.tell()
                    file.seek(0)  # Reset to beginning

                    if file_size > 25 * 1024 * 1024:  # 25MB
                        flash(f'File {file.filename} is too large (max 25MB)')
                        continue

                    # Read file data
                    file_data = file.read()
                    import base64

                    attachments.append({
                        'filename': file.filename,
                        'data': base64.b64encode(file_data).decode('utf-8'),
                        'content_type': file.content_type or 'application/octet-stream',
                        'size': file_size
                    })

        # Send email via integrated mailserver
        try:
            import mailserver

            # Send to each recipient
            all_success = True
            error_messages = []

            for to_address in to_addresses:
                success, message = mailserver.send_email_via_mailserver(
                    from_address=from_account.email_address,
                    to_address=to_address,
                    subject=form.subject.data,
                    body=form.body.data
                )

                if not success:
                    all_success = False
                    error_messages.append(f"Failed to send to {to_address}: {message}")

            success = all_success
            message = "All emails sent successfully" if all_success else "; ".join(error_messages)

        except Exception as e:
            success = False
            message = f"Error sending email: {e}"

        if success:
            flash('Email sent successfully!')
            return redirect(url_for('user_email_inbox', account_id=from_account.id))
        else:
            flash(f'Failed to send email: {message}')

    return render_template('email/compose.html', form=form, email_accounts=email_accounts, messages=messages)

@app.route('/email/reply/<int:message_id>', methods=['GET', 'POST'])
@login_required
def user_reply_email(message_id):
    """Reply to an email"""
    # Get the original message
    original_message = EmailMessage.query.filter_by(
        id=message_id,
        is_deleted=False
    ).join(EmailAccount).filter(EmailAccount.user_id == current_user.id).first_or_404()

    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()
    form = ComposeEmailForm()

    # Set choices for from_account field
    form.from_account.choices = [(account.id, account.email_address) for account in email_accounts]

    # Pre-fill form with reply data
    if request.method == 'GET':
        form.subject.data = original_message.get_reply_subject()
        form.to.data = original_message.sender

        # Add quoted original message to body
        quoted_body = f"\n\n--- Original Message ---\n"
        quoted_body += f"From: {original_message.sender}\n"
        quoted_body += f"Date: {original_message.received_at.strftime('%Y-%m-%d %H:%M:%S') if original_message.received_at else 'Unknown'}\n"
        quoted_body += f"Subject: {original_message.subject or '(no subject)'}\n\n"
        quoted_body += f"{original_message.body_text or ''}"

        form.body.data = quoted_body

    if form.validate_on_submit():
        try:
            # Get sender account
            from_account = EmailAccount.query.filter_by(
                id=form.from_account.data,
                user_id=current_user.id
            ).first()

            if not from_account:
                flash('Invalid sender account selected', 'error')
                return redirect(url_for('user_reply_email', message_id=message_id))

            # Send the reply via mailserver
            try:
                import mailserver
                success, message = mailserver.send_email(
                    from_address=from_account.email_address,
                    to_address=form.to.data,
                    subject=form.subject.data,
                    body=form.body.data,
                    reply_to_message_id=original_message.message_id
                )
            except ImportError:
                success, message = False, "Mail server not available"

            if success:
                flash('Reply sent successfully!')
                return redirect(url_for('user_email_inbox', account_id=from_account.id))
            else:
                flash(f'Failed to send reply: {message}', 'error')

        except Exception as e:
            flash(f'Error sending reply: {str(e)}', 'error')

    return render_template('email/reply.html',
                         form=form,
                         email_accounts=email_accounts,
                         original_message=original_message,
                         messages=messages)

@app.route('/email/inbox/<int:account_id>')
@login_required
def user_email_inbox(account_id):
    """View email inbox - shows both received and sent emails"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first_or_404()

    page = request.args.get('page', 1, type=int)
    folder = request.args.get('folder', 'all')  # all, inbox, sent

    # Build query based on folder filter
    query = EmailMessage.query.filter_by(
        account_id=account_id,
        is_deleted=False
    )

    if folder == 'inbox':
        query = query.filter_by(folder='INBOX')
        folder_title = "Inbox"
    elif folder == 'sent':
        query = query.filter_by(folder='SENT')
        folder_title = "Sent"
    else:
        # Show all emails (both inbox and sent)
        query = query.filter(EmailMessage.folder.in_(['INBOX', 'SENT']))
        folder_title = "All Mail"

    # Order by the most recent timestamp (either sent_at or received_at)
    # Use COALESCE to get the most recent timestamp from either field
    from sqlalchemy import func
    email_messages = query.order_by(
        func.coalesce(EmailMessage.sent_at, EmailMessage.received_at).desc(),
        EmailMessage.id.desc()  # Secondary sort by ID for consistency
    ).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('email/inbox.html',
                         account=account,
                         email_messages=email_messages,
                         messages=messages,
                         ui_messages=messages,
                         current_folder=folder,
                         folder_title=folder_title)

# Sent emails functionality removed - integrated into inbox view

@app.route('/email/<int:account_id>/message/<int:message_id>')
@login_required
def user_view_email(account_id, message_id):
    """View a specific email message"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first_or_404()
    message = EmailMessage.query.filter_by(
        id=message_id,
        account_id=account_id
    ).first_or_404()

    # Mark message as read
    if not message.is_read:
        message.is_read = True
        db.session.commit()

    return render_template('email/view_message.html', account=account, message=message, messages=messages)

@app.route('/email/smtp-setup')
@login_required
def user_smtp_setup():
    """SMTP setup guide for users"""
    accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()

    # Get server configuration
    try:
        import mailserver
        server_config = {
            'smtp_server': mailserver.SERVER_IP,
            'smtp_port': mailserver.SMTP_PORT,
            'smtp_port_submission': mailserver.SMTP_PORT_SUBMISSION,
            'smtp_port_ssl': mailserver.SMTP_PORT_SSL,
            'domain': mailserver.MAIL_DOMAIN,
            'tls_enabled': mailserver.TLS_ENABLED,
            'starttls_enabled': mailserver.STARTTLS_ENABLED
        }
    except ImportError:
        server_config = {
            'smtp_server': '*************',
            'smtp_port': 25,
            'smtp_port_submission': 587,
            'smtp_port_ssl': 465,
            'domain': 'lxnd.cloud',
            'tls_enabled': True,
            'starttls_enabled': True
        }

    return render_template('email/smtp_setup.html',
                         accounts=accounts,
                         server_config=server_config,
                         messages=messages)

@app.route('/email/<int:account_id>/message/<int:message_id>/attachment/<int:attachment_id>')
@login_required
def download_attachment(account_id, message_id, attachment_id):
    """Download email attachment"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first_or_404()
    message = EmailMessage.query.filter_by(id=message_id, account_id=account_id).first_or_404()

    try:
        attachments = message.get_attachments_list()
        if attachment_id >= len(attachments):
            flash('Attachment not found')
            return redirect(url_for('user_view_email', account_id=account_id, message_id=message_id))

        attachment = attachments[attachment_id]

        # Create response with attachment data
        from flask import Response
        import base64

        # Decode attachment data
        file_data = base64.b64decode(attachment['data'])

        response = Response(
            file_data,
            mimetype=attachment.get('content_type', 'application/octet-stream'),
            headers={
                'Content-Disposition': f'attachment; filename="{attachment["filename"]}"'
            }
        )

        return response

    except Exception as e:
        logger.error(f"Error downloading attachment: {e}")
        flash('Error downloading attachment')
        return redirect(url_for('user_view_email', account_id=account_id, message_id=message_id))

@app.route('/email/<int:account_id>/message/<int:message_id>/mark-read', methods=['POST'])
@login_required
def mark_email_read(account_id, message_id):
    """Mark email as read"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first_or_404()
    message = EmailMessage.query.filter_by(
        id=message_id,
        account_id=account_id
    ).first_or_404()

    message.is_read = True
    db.session.commit()

    return jsonify({'success': True})

@app.route('/email/<int:account_id>/message/<int:message_id>/mark-unread', methods=['POST'])
@login_required
def mark_email_unread(account_id, message_id):
    """Mark email as unread"""
    account = EmailAccount.query.filter_by(id=account_id, user_id=current_user.id).first_or_404()
    message = EmailMessage.query.filter_by(
        id=message_id,
        account_id=account_id
    ).first_or_404()

    message.is_read = False
    db.session.commit()

    return jsonify({'success': True})

@app.route('/email/create', methods=['GET', 'POST'])
@login_required
def user_create_email_account():
    """User creates their own email account"""
    # Check if user can create another email account
    if not current_user.can_create_email_account():
        current_count = EmailAccount.query.filter_by(user_id=current_user.id).count()
        max_accounts = current_user.get_max_email_accounts()

        if max_accounts == float('inf'):
            flash('Error checking account limits')
        else:
            flash(f'You have reached your email account limit ({current_count}/{int(max_accounts)}). Upgrade to Lux for more accounts!')
        return redirect(url_for('user_email_dashboard'))

    form = UserEmailAccountForm()

    if form.validate_on_submit():
        # Check if email already exists
        existing = EmailAccount.query.filter_by(email_address=form.email_address.data).first()
        if existing:
            flash('Email address already exists')
            return render_template('email/create_account.html', form=form, messages=messages)

# Using external mail servers for all email functionality

        # Create new account with default settings
        email_settings = EmailSettings.get_settings()
        account = EmailAccount(
            user_id=current_user.id,
            email_address=form.email_address.data,
            display_name=form.display_name.data or current_user.username,
            storage_limit_mb=email_settings.default_storage_gb * 1024,  # Convert GB to MB
            is_verified=True,  # Auto-verify user-created accounts
            is_active=True
        )
        account.set_password(form.password.data)

        db.session.add(account)
        db.session.commit()

        flash(f'Email account {form.email_address.data} created successfully!')
        return redirect(url_for('user_email_dashboard'))

    return render_template('email/create_account.html', form=form, messages=messages)

@app.route('/admin/users/search')
@login_required
@admin_required
def admin_search_users():
    query = request.args.get('q', '')
    filter_type = request.args.get('filter', 'all')

    users_query = User.query

    if query:
        users_query = users_query.filter(
            db.or_(
                User.username.contains(query),
                User.email.contains(query)
            )
        )

    if filter_type == 'admin':
        users_query = users_query.filter(User.is_admin == True)
    elif filter_type == 'moderator':
        users_query = users_query.filter(User.is_moderator == True)
    elif filter_type == 'banned':
        users_query = users_query.filter(User.is_banned == True)
    elif filter_type == 'regular':
        users_query = users_query.filter(
            db.and_(User.is_admin == False, User.is_moderator == False)
        )

    users = users_query.all()

    return jsonify({
        'users': [{
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'is_admin': user.is_admin,
            'is_moderator': user.is_moderator,
            'is_banned': user.is_banned,
            'created_at': user.created_at.strftime('%Y-%m-%d'),
            'projects_count': len(user.projects),
            'storage_used': round(user.get_used_storage_mb(), 2)
        } for user in users]
    })

@app.route('/api/admin/user/lux', methods=['POST'])
@login_required
@admin_required
def api_give_lux_status():
    """API endpoint to give Lux status to a user (for Discord bot)"""
    try:
        data = request.get_json()
        username = data.get('username')
        duration_days = data.get('duration_days')

        if not username:
            return jsonify({'error': 'Username is required'}), 400

        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Store old status for comparison
        old_lux_status = user.is_lux_active()

        # Grant Lux status
        user.is_lux = True
        if duration_days:
            user.lux_expires_at = datetime.utcnow() + timedelta(days=duration_days)
        else:
            user.lux_expires_at = None  # Permanent

        db.session.commit()

        # Update Discord role if user has Discord linked
        new_lux_status = user.is_lux_active()
        if user.discord_id and new_lux_status:
            update_discord_role(user.discord_id, 'lux', True)

        return jsonify({
            'success': True,
            'message': f'Lux status granted to {username}',
            'duration': f'{duration_days} days' if duration_days else 'Permanent',
            'discord_updated': bool(user.discord_id)
        })

    except Exception as e:
        logger.error(f"Error giving Lux status via API: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Routes
@app.route('/')
def index():
    return render_template('landing.html', messages=messages)

# File upload routes
@app.route('/files/dashboard')
@login_required
def files_dashboard():
    files = FileUpload.query.filter_by(user_id=current_user.id).order_by(FileUpload.upload_date.desc()).all()
    form = FileUploadForm()
    return render_template('files_dashboard.html', files=files, form=form, messages=messages)

@app.route('/files/upload', methods=['POST'])
@login_required
def upload_file():
    form = FileUploadForm()
    if form.validate_on_submit():
        file = form.file.data
        if file and allowed_file(file.filename):
            # Check if user is banned
            if current_user.is_banned:
                flash('Your account has been suspended')
                return redirect(url_for('files_dashboard'))
            
            # Get file data
            file_data = file.read()
            file_size = len(file_data)
            
            # Check if user has enough storage
            if not current_user.can_upload(file_size):
                flash('You have reached your storage limit')
                return redirect(url_for('files_dashboard'))
            
            # Generate a secure filename with UUID to prevent collisions
            original_filename = secure_filename(file.filename)
            file_ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
            unique_filename = f"{uuid.uuid4().hex}.{file_ext}" if file_ext else f"{uuid.uuid4().hex}"
            
            # Create a temporary directory for chunks
            temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp', str(uuid.uuid4()))
            os.makedirs(temp_dir, exist_ok=True)
            
            # Split into chunks
            chunk_size = 1024 * 1024  # 1MB chunks
            chunks = [file_data[i:i+chunk_size] for i in range(0, file_size, chunk_size)]
            
            # Save chunks in parallel
            chunk_paths = []
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = []
                for i, chunk in enumerate(chunks):
                    chunk_path = os.path.join(temp_dir, f"chunk_{i}")
                    chunk_paths.append(chunk_path)
                    futures.append(executor.submit(save_file_chunk, chunk, chunk_path))
                
                # Wait for all chunks to be saved
                concurrent.futures.wait(futures)
            
            # Combine chunks
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            combine_chunks(chunk_paths, file_path)
            
            # Clean up temp directory
            try:
                os.rmdir(temp_dir)
            except:
                pass
            
            # Create file record in database
            file_upload = FileUpload(
                filename=unique_filename,
                original_filename=original_filename,
                file_size=file_size,
                file_type=file_ext,
                user_id=current_user.id,
                public=form.public.data == 'public'
            )
            db.session.add(file_upload)
            db.session.commit()
            
            flash('File uploaded successfully')
        else:
            flash('Invalid file type')
    else:
        flash('Error uploading file')
    
    return redirect(url_for('files_dashboard'))

@app.route('/files/upload/ajax', methods=['POST'])
@login_required
def upload_file_ajax():
    """AJAX file upload endpoint that uses session authentication"""
    try:
        # Check if user is banned
        if current_user.is_banned:
            return jsonify({'error': 'Account suspended'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed'}), 400

        # Get file data
        file_data = file.read()
        file_size = len(file_data)

        # Check storage limit
        if not current_user.can_upload(file_size):
            return jsonify({
                'error': 'Storage limit exceeded',
                'current_usage_mb': current_user.get_used_storage_mb(),
                'limit_mb': current_user.upload_limit_mb,
                'file_size_mb': file_size / (1024 * 1024)
            }), 413

        # Generate unique filename
        original_filename = secure_filename(file.filename)
        file_ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
        unique_filename = f"{uuid.uuid4().hex}.{file_ext}" if file_ext else f"{uuid.uuid4().hex}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        # Save file
        with open(file_path, 'wb') as f:
            f.write(file_data)

        # Get public parameter (default to private)
        is_public = request.form.get('public', 'false').lower() == 'true'

        # Create database record
        file_upload = FileUpload(
            filename=unique_filename,
            original_filename=original_filename,
            file_size=file_size,
            file_type=file_ext,
            user_id=current_user.id,
            public=is_public
        )

        db.session.add(file_upload)
        db.session.commit()

        return jsonify({
            'message': 'File uploaded successfully',
            'file': {
                'id': file_upload.id,
                'file_id': file_upload.file_id,
                'filename': file_upload.filename,
                'original_filename': file_upload.original_filename,
                'file_size': file_upload.file_size,
                'file_type': file_upload.file_type,
                'upload_date': file_upload.upload_date.isoformat(),
                'public': file_upload.public,
                'download_url': url_for('download_file', file_id=file_upload.file_id, _external=True)
            }
        }), 201

    except Exception as e:
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        return jsonify({'error': 'Upload failed', 'details': str(e)}), 500

@app.route('/files/download/<string:file_id>')
def download_file(file_id):
    file_upload = FileUpload.query.filter_by(file_id=file_id).first_or_404()
    
    # Check if user has permission to download
    if not file_upload.public and (not current_user.is_authenticated or file_upload.user_id != current_user.id):
        flash('You do not have permission to access this file')
        return redirect(url_for('index'))
    
    # Increment download count
    file_upload.download_count += 1
    db.session.commit()
    
    return send_from_directory(
        app.config['UPLOAD_FOLDER'],
        file_upload.filename,
        as_attachment=True,
        download_name=file_upload.original_filename
    )

@app.route('/files/delete/<string:file_id>', methods=['POST'])
@login_required
def delete_file(file_id):
    file_upload = FileUpload.query.filter_by(file_id=file_id).first_or_404()
    
    if file_upload.user_id != current_user.id:
        flash('You do not have permission to delete this file')
        return redirect(url_for('files_dashboard'))
    
    # Delete the file from filesystem
    try:
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], file_upload.filename))
    except:
        pass  # File might not exist
    
    # Delete from database
    db.session.delete(file_upload)
    db.session.commit()
    
    flash('File deleted successfully')
    return redirect(url_for('files_dashboard'))

@app.route('/license/api-docs')
def api_docs():
    return render_template('api_docs.html', messages=messages)

@app.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            if user.is_banned:
                flash('Your account has been suspended')
                return render_template('login.html', form=form, messages=messages)

            login_user(user)

            # Check if there's a pending Discord verification
            after_login_action = session.pop('after_login_action', None)
            if after_login_action == 'discord_verify':
                return redirect(url_for('discord_auth'))

            next_page = request.args.get('next')
            return redirect(next_page or url_for('dashboard'))
        flash('Invalid username or password')
    return render_template('login.html', form=form, messages=messages)

@app.route('/register', methods=['GET', 'POST'])
def register():
    form = RegisterForm()
    if form.validate_on_submit():
        if User.query.filter_by(username=form.username.data).first():
            flash('Username already exists')
            return render_template('register.html', form=form, messages=messages)
        if User.query.filter_by(email=form.email.data).first():
            flash('Email already exists')
            return render_template('register.html', form=form, messages=messages)
        
        user = User(username=form.username.data, email=form.email.data)
        user.set_password(form.password.data)
        user.generate_api_token()

        # Automatically make 'luxend' user an admin
        if form.username.data.lower() == 'luxend':
            user.is_admin = True

        db.session.add(user)
        db.session.commit()

        # Send welcome email
        try:
            success, message = send_welcome_email(user.email, user.username)
            if success:
                flash('Registration successful! A welcome email has been sent to your email address.')
            else:
                flash('Registration successful! However, we could not send a welcome email.')
                logger.warning(f"Failed to send welcome email to {user.email}: {message}")
        except Exception as e:
            flash('Registration successful! However, we could not send a welcome email.')
            logger.error(f"Error sending welcome email to {user.email}: {e}")

        return redirect(url_for('login'))
    return render_template('register.html', form=form, messages=messages)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    projects = Project.query.filter_by(user_id=current_user.id).all()
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()
    websites = Website.query.filter_by(user_id=current_user.id).all()
    shortened_urls = ShortenedUrl.query.filter_by(user_id=current_user.id).all()
    form = FileUploadForm()

    # Calculate statistics for dashboard
    total_storage_mb = current_user.get_used_storage_mb()
    total_projects = len(projects)
    total_licenses = sum(len(project.licenses) for project in projects)

    # Website statistics
    stats = {
        'websites': len(websites),
        'email_accounts': len(email_accounts),
        'shortened_urls': len(shortened_urls),
        'storage_used': total_storage_mb
    }

    return render_template('dashboard.html',
                         user=current_user,
                         projects=projects,
                         email_accounts=email_accounts,
                         websites=websites,
                         stats=stats,
                         form=form,
                         messages=messages,
                         total_storage_mb=total_storage_mb,
                         total_projects=total_projects,
                         total_licenses=total_licenses)

@app.route('/profile')
@login_required
def profile():
    # Get user's email accounts for the email password form
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()

    # Create forms
    account_password_form = ChangeAccountPasswordForm()
    email_password_form = ChangeEmailPasswordForm()

    # Populate email account choices
    email_password_form.email_account.choices = [(account.id, account.email_address) for account in email_accounts]

    return render_template('profile.html',
                         user=current_user,
                         email_accounts=email_accounts,
                         account_password_form=account_password_form,
                         email_password_form=email_password_form,
                         messages=messages)

# Discord OAuth2 Routes
@app.route('/oauth/discord/auth')
def discord_bot_oauth():
    """Handle Discord OAuth2 through Dashboard"""
    discord_user_id = request.args.get('discord_user_id')
    state = request.args.get('state')

    # If coming from Discord bot command, store the discord_user_id in session
    if discord_user_id:
        session['pending_discord_verification'] = {
            'discord_user_id': discord_user_id,
            'timestamp': datetime.utcnow().isoformat()
        }

    # If user is logged in, proceed with Discord OAuth
    if current_user.is_authenticated:
        return redirect(url_for('discord_auth'))
    else:
        # Store the intent to verify after login
        session['after_login_action'] = 'discord_verify'
        flash('Please log in to your LXND account to complete Discord verification.')
        return redirect(url_for('login'))

@app.route('/auth/discord')
@login_required
def discord_auth():
    """Initiate Discord OAuth2 flow"""
    # Check if this is from bot verification
    pending_verification = session.get('pending_discord_verification')
    if pending_verification:
        session['bot_verification_context'] = True

    params = {
        'client_id': app.config['DISCORD_CLIENT_ID'],
        'redirect_uri': app.config['DISCORD_REDIRECT_URI'],
        'response_type': 'code',
        'scope': 'identify',
        'state': current_user.id  # Use user ID as state for security
    }

    discord_auth_url = f"https://discord.com/api/oauth2/authorize?{urlencode(params)}"
    return redirect(discord_auth_url)

@app.route('/auth/discord/callback')
@login_required
def discord_callback():
    """Handle Discord OAuth2 callback"""
    code = request.args.get('code')
    state = request.args.get('state')

    # Verify state matches current user
    if not state or int(state) != current_user.id:
        flash('Invalid Discord authentication state')
        return redirect(url_for('profile'))

    if not code:
        flash('Discord authentication failed')
        return redirect(url_for('profile'))

    try:
        # Exchange code for access token
        token_data = {
            'client_id': app.config['DISCORD_CLIENT_ID'],
            'client_secret': app.config['DISCORD_CLIENT_SECRET'],
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': app.config['DISCORD_REDIRECT_URI']
        }

        token_response = requests.post(
            'https://discord.com/api/oauth2/token',
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )

        if token_response.status_code != 200:
            flash('Failed to get Discord access token')
            return redirect(url_for('profile'))

        token_json = token_response.json()
        access_token = token_json.get('access_token')

        # Get user info from Discord
        user_response = requests.get(
            'https://discord.com/api/users/@me',
            headers={'Authorization': f'Bearer {access_token}'}
        )

        if user_response.status_code != 200:
            flash('Failed to get Discord user information')
            return redirect(url_for('profile'))

        discord_user = user_response.json()

        # Check if this is a bot verification context
        bot_verification = session.pop('bot_verification_context', False)
        pending_verification = session.pop('pending_discord_verification', None)

        # If coming from bot verification, validate Discord user ID matches
        if bot_verification and pending_verification:
            expected_discord_id = pending_verification['discord_user_id']
            if discord_user['id'] != expected_discord_id:
                flash('Discord account mismatch. Please try verification again.')
                return redirect(url_for('profile'))

        # Update user with Discord info
        current_user.discord_id = discord_user['id']
        current_user.discord_username = f"{discord_user['username']}#{discord_user['discriminator']}"
        current_user.discord_avatar = discord_user.get('avatar')
        current_user.discord_linked_at = datetime.utcnow()

        db.session.commit()

        # Update Discord roles
        update_discord_role(current_user.discord_id, 'verification', True)
        if current_user.is_lux_active():
            update_discord_role(current_user.discord_id, 'lux', True)
        else:
            update_discord_role(current_user.discord_id, 'lux', False)

        if bot_verification:
            flash('Discord account linked successfully via bot verification!')
        else:
            flash('Discord account linked successfully!')

    except Exception as e:
        logger.error(f"Discord OAuth error: {e}")
        flash('An error occurred during Discord authentication')

    return redirect(url_for('profile'))



@app.route('/oauth/discord/auth')
def discord_bot_oauth():
    """Handle Discord OAuth2 through Dashboard"""
    discord_user_id = request.args.get('discord_user_id')
    state = request.args.get('state')

    # If coming from Discord bot command, store the discord_user_id in session
    if discord_user_id:
        session['pending_discord_verification'] = {
            'discord_user_id': discord_user_id,
            'timestamp': datetime.utcnow().isoformat()
        }

    # If user is logged in, proceed with Discord OAuth
    if current_user.is_authenticated:
        return redirect(url_for('discord_auth'))
    else:
        # Store the intent to verify after login
        session['after_login_action'] = 'discord_verify'
        flash('Please log in to your LXND account to complete Discord verification.')
        return redirect(url_for('login'))

@app.route('/auth/discord/bot-callback')
def discord_bot_callback():
    """Handle callback from Discord bot verification"""
    try:
        # Get pending verification from session
        pending = session.get('pending_discord_verification')
        if not pending:
            flash('No pending Discord verification found.')
            return redirect(url_for('profile'))

        # Check if user is logged in
        if not current_user.is_authenticated:
            flash('Please log in to complete Discord verification.')
            return redirect(url_for('login'))

        # Check if user already has Discord linked
        if current_user.discord_id:
            flash('Your account is already linked to Discord.')
            return redirect(url_for('profile'))

        discord_user_id = pending['discord_user_id']

        # Check if this Discord ID is already linked to another account
        existing_user = User.query.filter_by(discord_id=discord_user_id).first()
        if existing_user:
            flash('This Discord account is already linked to another LXND account.')
            return redirect(url_for('profile'))

        # We need to get Discord user info, so redirect to normal OAuth flow
        # but store the bot verification context
        session['bot_verification_context'] = True
        return redirect(url_for('discord_auth'))

    except Exception as e:
        logger.error(f"Error in Discord bot callback: {e}")
        flash('An error occurred during Discord verification.')
        return redirect(url_for('profile'))

@app.route('/api/discord/link', methods=['POST'])
@login_required
def api_discord_link():
    """API endpoint to link Discord account from bot"""
    try:
        data = request.get_json()
        discord_id = data.get('discord_id')
        discord_username = data.get('discord_username')
        discord_avatar = data.get('discord_avatar')

        if not discord_id:
            return jsonify({'error': 'Missing discord_id'}), 400

        # Check if Discord account is already linked
        existing_user = User.query.filter_by(discord_id=discord_id).first()
        if existing_user:
            return jsonify({'error': 'Discord account already linked'}), 409

        # Update current user with Discord info
        current_user.discord_id = discord_id
        current_user.discord_username = discord_username
        current_user.discord_avatar = discord_avatar
        current_user.discord_linked_at = datetime.utcnow()

        db.session.commit()

        # Update Discord roles
        update_discord_role(current_user.discord_id, 'verification', True)
        if current_user.is_lux_active():
            update_discord_role(current_user.discord_id, 'lux', True)

        return jsonify({
            'success': True,
            'message': 'Discord account linked successfully',
            'user': {
                'id': current_user.id,
                'username': current_user.username,
                'discord_id': current_user.discord_id,
                'discord_username': current_user.discord_username
            }
        })

    except Exception as e:
        logger.error(f"Error in API Discord link: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/auth/discord/unlink', methods=['POST'])
@login_required
def discord_unlink():
    """Unlink Discord account from LXND account"""
    try:
        if not current_user.discord_id:
            flash('No Discord account is linked to your LXND account.')
            return redirect(url_for('profile'))

        discord_id = current_user.discord_id

        # Remove all Discord roles
        update_discord_role(discord_id, 'verification', False)
        update_discord_role(discord_id, 'lux', False)

        # Clear Discord info from user account
        current_user.discord_id = None
        current_user.discord_username = None
        current_user.discord_avatar = None
        current_user.discord_linked_at = None

        db.session.commit()

        flash('Discord account has been unlinked successfully.')
        logger.info(f"Discord account unlinked for user {current_user.username} (Discord ID: {discord_id})")

    except Exception as e:
        logger.error(f"Error unlinking Discord account: {e}")
        flash('An error occurred while unlinking your Discord account.')

    return redirect(url_for('profile'))

def update_discord_role(discord_id, role_type, add_role=True):
    """Update Discord role for user"""
    try:
        logger.info(f"update_discord_role called: discord_id={discord_id}, role_type={role_type}, add_role={add_role}")

        if not app.config['DISCORD_BOT_TOKEN']:
            logger.warning("DISCORD_BOT_TOKEN not configured")
            return False

        # Store role update in database for bot to process
        role_update_data = {
            'discord_id': discord_id,
            'role_type': role_type,
            'action': 'add' if add_role else 'remove',
            'timestamp': datetime.utcnow(),
            'processed': False
        }

        logger.info(f"Creating role update: {role_update_data}")

        # Insert into role_updates table
        with get_mysql_manager() as mysql_db:
            mysql_db.execute_query("""
                CREATE TABLE IF NOT EXISTS discord_role_updates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    discord_id VARCHAR(20) NOT NULL,
                    role_type VARCHAR(50) NOT NULL,
                    action VARCHAR(10) NOT NULL,
                    timestamp DATETIME NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    processed_at DATETIME NULL
                )
            """)

            mysql_db.execute_query("""
                INSERT INTO discord_role_updates (discord_id, role_type, action, timestamp)
                VALUES (%s, %s, %s, %s)
            """, (discord_id, role_type, role_update_data['action'], role_update_data['timestamp']))

            logger.info(f"Successfully inserted role update into database for Discord ID {discord_id}")

        return True

    except Exception as e:
        logger.error(f"Error updating Discord role: {e}")
        return False

@app.route('/reset_token', methods=['POST'])
@login_required
def reset_token():
    current_user.generate_api_token()
    db.session.commit()
    flash('API token has been reset')
    return redirect(url_for('profile'))

@app.route('/change_account_password', methods=['POST'])
@login_required
def change_account_password():
    form = ChangeAccountPasswordForm()

    if form.validate_on_submit():
        # Verify current password
        if not check_password_hash(current_user.password_hash, form.current_password.data):
            flash('Current password is incorrect', 'error')
            return redirect(url_for('profile'))

        # Update password
        current_user.set_password(form.new_password.data)
        db.session.commit()

        flash('Account password changed successfully!')
        return redirect(url_for('profile'))

    # If form validation fails, show errors
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{field}: {error}', 'error')

    return redirect(url_for('profile'))

@app.route('/change_email_password', methods=['POST'])
@login_required
def change_email_password():
    form = ChangeEmailPasswordForm()

    # Get user's email accounts for form choices
    email_accounts = EmailAccount.query.filter_by(user_id=current_user.id).all()
    form.email_account.choices = [(account.id, account.email_address) for account in email_accounts]

    if form.validate_on_submit():
        # Get the selected email account
        email_account = EmailAccount.query.filter_by(
            id=form.email_account.data,
            user_id=current_user.id
        ).first()

        if not email_account:
            flash('Email account not found', 'error')
            return redirect(url_for('profile'))

        # Verify current email password
        if not check_password_hash(email_account.password_hash, form.current_password.data):
            flash('Current email password is incorrect', 'error')
            return redirect(url_for('profile'))

        # Update email password
        email_account.set_password(form.new_password.data)
        db.session.commit()

        # Update password in mailserver if available
        try:
            import mailserver
            success = mailserver.update_email_password(email_account.email_address, form.new_password.data)
            if success:
                flash(f'Email password for {email_account.email_address} changed successfully!')
            else:
                flash(f'Email password updated in database, but mailserver update failed', 'warning')
        except ImportError:
            flash(f'Email password for {email_account.email_address} changed successfully!')

        return redirect(url_for('profile'))

    # If form validation fails, show errors
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{field}: {error}', 'error')

    return redirect(url_for('profile'))

@app.route('/license/create_project', methods=['GET', 'POST'])
@login_required
def create_project():
    # Check if user is banned
    if current_user.is_banned:
        flash('Your account has been suspended')
        return redirect(url_for('dashboard'))
    
    # Check if user can create more projects
    if not current_user.can_create_project():
        flash(f'You have reached your limit of {current_user.max_projects} projects')
        return redirect(url_for('dashboard'))
    
    form = ProjectForm()
    if form.validate_on_submit():
        project = Project(
            name=form.name.data,
            description=form.description.data,
            user_id=current_user.id
        )
        db.session.add(project)
        db.session.commit()
        flash('Project created successfully')
        return redirect(url_for('dashboard'))
    return render_template('create_project.html', form=form, messages=messages)

@app.route('/license/project/<project_id>')
@login_required
def project_detail(project_id):
    project = Project.query.filter_by(project_id=project_id, user_id=current_user.id).first_or_404()
    licenses = License.query.filter_by(project_id=project_id).all()
    form = LicenseForm()
    return render_template('project_detail.html', project=project, licenses=licenses, form=form, messages=messages)

@app.route('/license/create_license/<project_id>', methods=['POST'])
@login_required
def create_license(project_id):
    project = Project.query.filter_by(project_id=project_id, user_id=current_user.id).first_or_404()

    # Check if user is banned
    if current_user.is_banned:
        flash('Your account has been suspended')
        return redirect(url_for('dashboard'))

    # Check if user can create more licenses
    if not current_user.can_create_license(project_id):
        flash(f'You have reached your limit of {current_user.max_keys_per_project} license keys for this project')
        return redirect(url_for('project_detail', project_id=project_id))

    # Get form data
    custom_key = request.form.get('custom_key', '').strip()
    duration_days = request.form.get('duration_days')

    # Validate custom key if provided
    if custom_key:
        if len(custom_key) < 3 or len(custom_key) > 64:
            flash('Custom key must be between 3 and 64 characters')
            return redirect(url_for('project_detail', project_id=project_id))

        # Check if custom key already exists
        if License.query.filter_by(license_key=custom_key).first():
            flash('Custom key already exists')
            return redirect(url_for('project_detail', project_id=project_id))

    try:
        # Create license with custom key if provided
        license_key = custom_key if custom_key else None
        license = License(license_key=license_key, project_id=project_id)

        # Set duration if specified
        if duration_days:
            try:
                duration_days = int(duration_days)
                if duration_days > 0:
                    license.expires_at = datetime.utcnow() + timedelta(days=duration_days)
                    license.duration_days = duration_days
            except ValueError:
                flash('Invalid duration value')
                return redirect(url_for('project_detail', project_id=project_id))

        db.session.add(license)
        db.session.commit()
        flash('License key created successfully')

    except Exception as e:
        db.session.rollback()
        flash(f'Error creating license: {str(e)}')
    
    return redirect(url_for('project_detail', project_id=project_id))

@app.route('/toggle_license/<int:license_id>', methods=['POST'])
@login_required
def toggle_license(license_id):
    license = db.session.get(License, license_id)
    if not license:
        flash('License not found')
        return redirect(url_for('dashboard'))
    project = Project.query.filter_by(project_id=license.project_id, user_id=current_user.id).first_or_404()
    license.is_active = not license.is_active
    db.session.commit()
    flash(f'License key {"activated" if license.is_active else "deactivated"}')
    return redirect(url_for('project_detail', project_id=license.project_id))

@app.route('/delete_license/<int:license_id>', methods=['POST'])
@login_required
def delete_license(license_id):
    license = db.session.get(License, license_id)
    if not license:
        flash('License not found')
        return redirect(url_for('dashboard'))

    # Verify the user owns the project
    project = Project.query.filter_by(project_id=license.project_id, user_id=current_user.id).first_or_404()

    # Check if user is banned
    if current_user.is_banned:
        flash('Your account has been suspended')
        return redirect(url_for('dashboard'))

    # Delete the license
    db.session.delete(license)
    db.session.commit()

    flash('License key deleted successfully')
    return redirect(url_for('project_detail', project_id=license.project_id))

@app.route('/delete_licenses_bulk/<project_id>', methods=['POST'])
@login_required
def delete_licenses_bulk(project_id):
    # Verify the user owns the project
    project = Project.query.filter_by(project_id=project_id, user_id=current_user.id).first_or_404()

    # Check if user is banned
    if current_user.is_banned:
        flash('Your account has been suspended')
        return redirect(url_for('dashboard'))

    license_ids = request.form.getlist('license_ids')
    if not license_ids:
        flash('No license keys selected')
        return redirect(url_for('project_detail', project_id=project_id))

    # Get licenses that belong to this project
    licenses = License.query.filter(
        License.id.in_(license_ids),
        License.project_id == project_id
    ).all()

    deleted_count = 0
    for license in licenses:
        db.session.delete(license)
        deleted_count += 1

    db.session.commit()

    flash(f'Successfully deleted {deleted_count} license key(s)')
    return redirect(url_for('project_detail', project_id=project_id))

@app.route('/project/<project_id>/create_bulk_licenses', methods=['POST'])
@login_required
def create_bulk_licenses(project_id):
    project = Project.query.filter_by(project_id=project_id, user_id=current_user.id).first_or_404()

    if current_user.is_banned:
        flash('Your account has been suspended')
        return redirect(url_for('dashboard'))

    try:
        count = int(request.form.get('count', 1))
        duration_type = request.form.get('duration_type', 'unlimited')
        duration_value = request.form.get('duration_value', 0)

        # Validate count
        if count < 1 or count > 100:
            flash('You can create between 1 and 100 license keys at once')
            return redirect(url_for('project_detail', project_id=project_id))

        # Check if user can create this many licenses
        current_licenses = License.query.filter_by(project_id=project_id).count()
        if current_user.max_keys_per_project > 0:
            if current_licenses + count > current_user.max_keys_per_project:
                flash(f'Cannot create {count} licenses. You can only have {current_user.max_keys_per_project} licenses per project.')
                return redirect(url_for('project_detail', project_id=project_id))

        created_licenses = []
        for i in range(count):
            license_key = secrets.token_hex(16)
            license = License(license_key=license_key, project_id=project_id)

            # Set expiration if specified
            if duration_type != 'unlimited' and duration_value:
                try:
                    duration_value = int(duration_value)
                    if duration_type == 'days':
                        license.expires_at = datetime.utcnow() + timedelta(days=duration_value)
                        license.duration_days = duration_value
                    elif duration_type == 'months':
                        license.expires_at = datetime.utcnow() + timedelta(days=duration_value * 30)
                        license.duration_days = duration_value * 30
                    elif duration_type == 'years':
                        license.expires_at = datetime.utcnow() + timedelta(days=duration_value * 365)
                        license.duration_days = duration_value * 365
                except ValueError:
                    pass

            db.session.add(license)
            created_licenses.append(license)

        db.session.commit()
        flash(f'Successfully created {count} license keys')

        # Store created license IDs in session for download
        session['bulk_created_licenses'] = [lic.id for lic in created_licenses]

        return redirect(url_for('project_detail', project_id=project_id))

    except Exception as e:
        db.session.rollback()
        flash('Error creating license keys. Please try again.')
        return redirect(url_for('project_detail', project_id=project_id))

@app.route('/project/<project_id>/download_licenses/<format>')
@login_required
def download_licenses(project_id, format):
    project = Project.query.filter_by(project_id=project_id, user_id=current_user.id).first_or_404()

    # Get license IDs from session or all project licenses
    license_ids = session.get('bulk_created_licenses', [])
    if license_ids:
        licenses = License.query.filter(License.id.in_(license_ids), License.project_id == project_id).all()
        # Clear the session data
        session.pop('bulk_created_licenses', None)
    else:
        licenses = License.query.filter_by(project_id=project_id).all()

    if not licenses:
        flash('No license keys found')
        return redirect(url_for('project_detail', project_id=project_id))

    if format == 'txt':
        # Generate TXT file with just the keys
        content = '\n'.join([license.license_key for license in licenses])
        response = make_response(content)
        response.headers['Content-Type'] = 'text/plain'
        response.headers['Content-Disposition'] = f'attachment; filename="{project.name}_license_keys.txt"'
        return response

    elif format == 'csv':
        # Generate CSV file with full info
        import io
        import csv

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['License Key', 'Status', 'Created Date', 'Expires Date', 'Days Until Expiry', 'Last Checked'])

        # Write license data
        for license in licenses:
            status = 'Active' if license.is_active else 'Inactive'
            if license.is_expired():
                status = 'Expired'

            expires_date = license.expires_at.strftime('%Y-%m-%d %H:%M:%S') if license.expires_at else 'Never'
            days_until_expiry = license.days_until_expiry() if license.expires_at else 'N/A'
            last_checked = license.last_checked.strftime('%Y-%m-%d %H:%M:%S') if license.last_checked else 'Never'

            writer.writerow([
                license.license_key,
                status,
                license.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                expires_date,
                days_until_expiry,
                last_checked
            ])

        content = output.getvalue()
        output.close()

        response = make_response(content)
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename="{project.name}_license_keys.csv"'
        return response

    else:
        flash('Invalid download format')
        return redirect(url_for('project_detail', project_id=project_id))

@app.route('/api/license/check/<project_id>/<license_key>')
def check_license(project_id, license_key):
    project = Project.query.filter_by(project_id=project_id).first()
    if not project:
        return jsonify({'valid': False, 'error': 'Project not found'}), 404
    
    license = License.query.filter_by(license_key=license_key, project_id=project_id).first()
    if not license:
        return jsonify({'valid': False, 'error': 'License key not found'}), 404
    
    license.last_checked = datetime.utcnow()
    db.session.commit()
    
    response_data = {
        'valid': license.is_valid(),
        'project_id': project_id,
        'license_key': license_key,
        'created_at': license.created_at.isoformat(),
        'last_checked': license.last_checked.isoformat(),
        'is_active': license.is_active,
        'is_expired': license.is_expired()
    }
    if license.expires_at:
        response_data['expires_at'] = license.expires_at.isoformat()
        response_data['days_until_expiry'] = license.days_until_expiry()
    else:
        response_data['expires_at'] = None
        response_data['days_until_expiry'] = None
    return jsonify(response_data)

@app.route('/api/license/add/<project_id>/<api_token>/<license_key>', methods=['POST'])
def add_license(project_id, api_token, license_key):
    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'success': False, 'error': 'Invalid API token'}), 401
    project = Project.query.filter_by(project_id=project_id, user_id=user.id).first()
    if not project:
        return jsonify({'success': False, 'error': 'Project not found or access denied'}), 404
    if License.query.filter_by(license_key=license_key).first():
        return jsonify({'success': False, 'error': 'License key already exists'}), 400
    
    data = request.get_json() or {}
    duration_days = data.get('duration_days')
    license = License(license_key=license_key, project_id=project_id)
    if duration_days and isinstance(duration_days, int) and duration_days > 0:
        license.expires_at = datetime.utcnow() + timedelta(days=duration_days)
        license.duration_days = duration_days
    db.session.add(license)
    db.session.commit()
    return jsonify({
        'success': True,
        'project_id': project_id,
        'license_key': license_key,
        'created_at': license.created_at.isoformat(),
        'expires_at': license.expires_at.isoformat() if license.expires_at else None
    })


# File Upload API Routes
@app.route('/api/files', methods=['GET'])
def api_get_files():
    """Get all files for the authenticated user"""
    api_token = request.headers.get('Authorization')
    if not api_token:
        return jsonify({'error': 'API token required'}), 401

    if api_token.startswith('Bearer '):
        api_token = api_token[7:]

    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'error': 'Invalid API token'}), 401

    if user.is_banned:
        return jsonify({'error': 'Account suspended'}), 403

    files = FileUpload.query.filter_by(user_id=user.id).all()

    files_data = []
    for file in files:
        files_data.append({
            'id': file.id,
            'file_id': file.file_id,
            'filename': file.filename,
            'original_filename': file.original_filename,
            'file_size': file.file_size,
            'file_type': file.file_type,
            'upload_date': file.upload_date.isoformat(),
            'download_count': file.download_count,
            'public': file.public,
            'download_url': url_for('download_file', file_id=file.file_id, _external=True)
        })

    return jsonify({
        'files': files_data,
        'total_files': len(files_data),
        'total_size_bytes': sum(f['file_size'] for f in files_data)
    })

@app.route('/api/files/upload', methods=['POST'])
def api_upload_file():
    """Upload a file via API"""
    api_token = request.headers.get('Authorization')
    if not api_token:
        return jsonify({'error': 'API token required'}), 401

    if api_token.startswith('Bearer '):
        api_token = api_token[7:]

    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'error': 'Invalid API token'}), 401

    if user.is_banned:
        return jsonify({'error': 'Account suspended'}), 403

    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    try:
        # Get file data
        file_data = file.read()
        file_size = len(file_data)

        # Check storage limit
        if not user.can_upload(file_size):
            return jsonify({
                'error': 'Storage limit exceeded',
                'current_usage_mb': user.get_used_storage_mb(),
                'limit_mb': user.upload_limit_mb,
                'file_size_mb': file_size / (1024 * 1024)
            }), 413

        # Generate unique filename
        original_filename = secure_filename(file.filename)
        file_ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
        unique_filename = f"{uuid.uuid4().hex}.{file_ext}" if file_ext else f"{uuid.uuid4().hex}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        # Save file
        with open(file_path, 'wb') as f:
            f.write(file_data)

        # Get public parameter (default to private)
        is_public = request.form.get('public', 'false').lower() == 'true'

        # Create database record
        file_upload = FileUpload(
            filename=unique_filename,
            original_filename=original_filename,
            file_size=file_size,
            file_type=file_ext,
            user_id=user.id,
            public=is_public
        )

        db.session.add(file_upload)
        db.session.commit()

        return jsonify({
            'message': 'File uploaded successfully',
            'file': {
                'id': file_upload.id,
                'file_id': file_upload.file_id,
                'filename': file_upload.filename,
                'original_filename': file_upload.original_filename,
                'file_size': file_upload.file_size,
                'file_type': file_upload.file_type,
                'upload_date': file_upload.upload_date.isoformat(),
                'public': file_upload.public,
                'download_url': url_for('download_file', file_id=file_upload.file_id, _external=True)
            }
        }), 201

    except Exception as e:
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        return jsonify({'error': 'Upload failed', 'details': str(e)}), 500

@app.route('/api/files/<file_id>', methods=['GET'])
def api_get_file(file_id):
    """Get specific file information"""
    api_token = request.headers.get('Authorization')
    if not api_token:
        return jsonify({'error': 'API token required'}), 401

    if api_token.startswith('Bearer '):
        api_token = api_token[7:]

    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'error': 'Invalid API token'}), 401

    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=user.id).first()
    if not file_upload:
        return jsonify({'error': 'File not found'}), 404

    return jsonify({
        'file': {
            'id': file_upload.id,
            'file_id': file_upload.file_id,
            'filename': file_upload.filename,
            'original_filename': file_upload.original_filename,
            'file_size': file_upload.file_size,
            'file_type': file_upload.file_type,
            'upload_date': file_upload.upload_date.isoformat(),
            'download_count': file_upload.download_count,
            'public': file_upload.public,
            'download_url': url_for('download_file', file_id=file_upload.file_id, _external=True)
        }
    })

@app.route('/api/files/<file_id>', methods=['DELETE'])
def api_delete_file(file_id):
    """Delete a file via API"""
    api_token = request.headers.get('Authorization')
    if not api_token:
        return jsonify({'error': 'API token required'}), 401

    if api_token.startswith('Bearer '):
        api_token = api_token[7:]

    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'error': 'Invalid API token'}), 401

    if user.is_banned:
        return jsonify({'error': 'Account suspended'}), 403

    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=user.id).first()
    if not file_upload:
        return jsonify({'error': 'File not found'}), 404

    try:
        # Delete physical file
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file_upload.filename)
        if os.path.exists(file_path):
            os.remove(file_path)

        # Delete database record
        db.session.delete(file_upload)
        db.session.commit()

        return jsonify({'message': 'File deleted successfully'}), 200

    except Exception as e:
        return jsonify({'error': 'Delete failed', 'details': str(e)}), 500

@app.route('/api/files/<file_id>/toggle-visibility', methods=['POST'])
def api_toggle_file_visibility(file_id):
    """Toggle file visibility (public/private) via API"""
    api_token = request.headers.get('Authorization')
    if not api_token:
        return jsonify({'error': 'API token required'}), 401

    if api_token.startswith('Bearer '):
        api_token = api_token[7:]

    user = User.query.filter_by(api_token=api_token).first()
    if not user:
        return jsonify({'error': 'Invalid API token'}), 401

    if user.is_banned:
        return jsonify({'error': 'Account suspended'}), 403

    file_upload = FileUpload.query.filter_by(file_id=file_id, user_id=user.id).first()
    if not file_upload:
        return jsonify({'error': 'File not found'}), 404

    # Toggle visibility
    file_upload.public = not file_upload.public
    db.session.commit()

    return jsonify({
        'message': f'File visibility changed to {"public" if file_upload.public else "private"}',
        'file': {
            'id': file_upload.id,
            'file_id': file_upload.file_id,
            'filename': file_upload.filename,
            'original_filename': file_upload.original_filename,
            'public': file_upload.public,
            'download_url': url_for('download_file', file_id=file_upload.file_id, _external=True)
        }
    }), 200

@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html', messages=messages), 404

# Initialize the database and admin
def initialize_admin():
    try:
        # Always make 'luxend' user an admin if they exist
        luxend_user = User.query.filter_by(username='luxend').first()
        if luxend_user and not luxend_user.is_admin:
            luxend_user.is_admin = True
            db.session.commit()
            print(f"User 'luxend' has been made an admin.")

        # Make the first user an admin if no admin exists
        admin = User.query.filter_by(is_admin=True).first()
        if not admin:
            first_user = User.query.order_by(User.id).first()
            if first_user:
                first_user.is_admin = True
                db.session.commit()
                print(f"User '{first_user.username}' has been made an admin.")
    except Exception as e:
        print(f"Error initializing admin: {e}")


# Public folder route
PUBLIC_FOLDER = os.path.join(os.getcwd(), 'public')

@app.route('/public/<path:filename>')
def serve_public(filename):
    return send_from_directory(PUBLIC_FOLDER, filename)

# Discord Bot Integration
def start_discord_bot():
    """Start the Discord bot in a separate process"""
    global bot_process
    try:
        if bot_process and bot_process.poll() is None:
            print("Discord bot is already running")
            return

        print("Starting Discord bot...")
        bot_script = os.path.join(os.getcwd(), 'discord-bot', 'index.js')
        bot_process = subprocess.Popen([
            'node', bot_script
        ], cwd=os.path.join(os.getcwd(), 'discord-bot'))
        print(f"Discord bot started with PID: {bot_process.pid}")

    except Exception as e:
        print(f"Error starting Discord bot: {e}")
        import traceback
        traceback.print_exc()

def stop_discord_bot():
    """Stop the Discord bot process"""
    global bot_process
    try:
        if bot_process and bot_process.poll() is None:
            print("Stopping Discord bot...")

            # First try graceful shutdown by creating stop flag
            stop_flag_path = os.path.join('discord_bot', 'stop_flag.txt')
            try:
                with open(stop_flag_path, 'w') as f:
                    f.write('stop')
                print("Stop flag created, waiting for graceful shutdown...")

                # Wait for graceful shutdown
                bot_process.wait(timeout=5)
                print("Discord bot stopped gracefully")
            except subprocess.TimeoutExpired:
                print("Graceful shutdown timeout, terminating process...")
                bot_process.terminate()
                try:
                    bot_process.wait(timeout=5)
                    print("Discord bot terminated")
                except subprocess.TimeoutExpired:
                    print("Bot didn't stop gracefully, forcing kill...")
                    bot_process.kill()
                    bot_process.wait()
                    print("Discord bot killed")

            bot_process = None

            # Clean up stop flag
            if os.path.exists(stop_flag_path):
                os.remove(stop_flag_path)

        else:
            print("Discord bot is not running")
    except Exception as e:
        print(f"Error stopping Discord bot: {e}")
        # Force cleanup
        if bot_process:
            try:
                bot_process.kill()
                bot_process = None
            except:
                pass

def get_bot_process_status():
    """Get the status of the Discord bot process"""
    global bot_process
    try:
        if bot_process is None:
            return {'status': 'stopped', 'pid': None}

        if bot_process.poll() is None:
            # Process is running
            try:
                process = psutil.Process(bot_process.pid)
                return {
                    'status': 'running',
                    'pid': bot_process.pid,
                    'memory_mb': round(process.memory_info().rss / 1024 / 1024, 2),
                    'cpu_percent': process.cpu_percent()
                }
            except psutil.NoSuchProcess:
                bot_process = None
                return {'status': 'stopped', 'pid': None}
        else:
            # Process has terminated
            bot_process = None
            return {'status': 'stopped', 'pid': None}
    except Exception as e:
        print(f"Error checking bot status: {e}")
        return {'status': 'unknown', 'pid': None}

# SSL startup logic
def start_http():
    """HTTP server that redirects to HTTPS"""
    from flask import Flask, redirect, request

    redirect_app = Flask(__name__)

    @redirect_app.route('/', defaults={'path': ''})
    @redirect_app.route('/<path:path>')
    def redirect_to_https(path):
        return redirect(f'https://{request.host}/{path}', code=301)

    redirect_app.run(debug=False, host='0.0.0.0', port=80)

class SSLManager:
    """Manages SSL certificates for different domains"""

    def __init__(self):
        self.certificates = {}
        self.load_certificates()

    def load_certificates(self):
        """Load all available SSL certificates"""
        import os

        print("🔒 Loading SSL certificates...")
        self.certificates = {}

        # Load Let's Encrypt certificates
        letsencrypt_dir = "/etc/letsencrypt/live"
        if os.path.exists(letsencrypt_dir):
            for domain_dir in os.listdir(letsencrypt_dir):
                cert_path = os.path.join(letsencrypt_dir, domain_dir, "fullchain.pem")
                key_path = os.path.join(letsencrypt_dir, domain_dir, "privkey.pem")

                if os.path.exists(cert_path) and os.path.exists(key_path):
                    self.certificates[domain_dir] = {
                        'cert': cert_path,
                        'key': key_path,
                        'type': 'letsencrypt'
                    }
                    print(f"  ✓ Let's Encrypt: {domain_dir}")

        # Load self-signed certificates
        ssl_dir = "/root/lxnd/ssl"
        if os.path.exists(ssl_dir):
            for subdomain_dir in os.listdir(ssl_dir):
                if os.path.isdir(os.path.join(ssl_dir, subdomain_dir)):
                    domain = f"{subdomain_dir}.lxnd.cloud"
                    cert_path = os.path.join(ssl_dir, subdomain_dir, "cert.pem")
                    key_path = os.path.join(ssl_dir, subdomain_dir, "key.pem")

                    if os.path.exists(cert_path) and os.path.exists(key_path):
                        # Only add if not already covered by Let's Encrypt
                        if domain not in self.certificates:
                            self.certificates[domain] = {
                                'cert': cert_path,
                                'key': key_path,
                                'type': 'self-signed'
                            }
                            print(f"  ✓ Self-signed: {domain}")

        print(f"📋 Loaded {len(self.certificates)} SSL certificates")
        return len(self.certificates) > 0

    def get_wildcard_certificate_path(self):
        """Get wildcard certificate paths (Let's Encrypt or self-signed)"""
        # Try Let's Encrypt wildcard certificate first
        letsencrypt_cert = "/etc/letsencrypt/live/lxnd.cloud/fullchain.pem"
        letsencrypt_key = "/etc/letsencrypt/live/lxnd.cloud/privkey.pem"

        if os.path.exists(letsencrypt_cert) and os.path.exists(letsencrypt_key):
            return {
                'cert': letsencrypt_cert,
                'key': letsencrypt_key,
                'type': 'letsencrypt_wildcard'
            }

        # Try self-signed wildcard certificate
        wildcard_cert = "/root/lxnd/ssl/wildcard/cert.pem"
        wildcard_key = "/root/lxnd/ssl/wildcard/key.pem"

        if os.path.exists(wildcard_cert) and os.path.exists(wildcard_key):
            return {
                'cert': wildcard_cert,
                'key': wildcard_key,
                'type': 'self_signed_wildcard'
            }

        return None

    def get_certificate_for_domain(self, domain):
        """Get certificate info for specific domain - always returns wildcard certificate"""
        # Always use wildcard certificate for all *.lxnd.cloud domains
        if domain.endswith('.lxnd.cloud') or domain == 'lxnd.cloud':
            return self.get_wildcard_certificate_path()

        # For other domains, fall back to individual certificates if they exist
        if domain in self.certificates:
            return self.certificates[domain]

        return None

    def get_ssl_context(self, domain=None):
        """Get SSL context - uses wildcard certificate for all lxnd.cloud domains"""
        import ssl

        # Always use wildcard certificate for lxnd.cloud domains
        if not domain or domain.endswith('.lxnd.cloud') or domain == 'lxnd.cloud':
            wildcard_cert = self.get_wildcard_certificate_path()
            if wildcard_cert:
                try:
                    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                    context.load_cert_chain(wildcard_cert['cert'], wildcard_cert['key'])
                    print(f"🔒 Using {wildcard_cert['type']} certificate for *.lxnd.cloud")
                    return context
                except Exception as e:
                    print(f"❌ Error loading wildcard SSL certificate: {e}")

            # If no wildcard certificate, create one
            print("🔒 No wildcard certificate found, creating one...")
            return self.create_wildcard_ssl_context()

        # For non-lxnd.cloud domains, fall back to individual certificates
        if domain and domain in self.certificates:
            cert_info = self.certificates[domain]
            try:
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(cert_info['cert'], cert_info['key'])
                print(f"🔒 Using {cert_info['type']} certificate: {cert_info['cert']}")
                return context
            except Exception as e:
                print(f"❌ Error loading SSL certificate: {e}")

        return None

    def generate_wildcard_letsencrypt_certificate(self):
        """Generate Let's Encrypt wildcard certificate for *.lxnd.cloud"""
        try:
            import subprocess
            import os

            print("🔒 Generating Let's Encrypt wildcard SSL certificate for *.lxnd.cloud...")

            # Try to generate wildcard certificate using DNS challenge
            # Note: This requires manual DNS TXT record setup
            cmd = [
                '/snap/bin/certbot', 'certonly',
                '--manual',
                '--preferred-challenges', 'dns',
                '--email', '<EMAIL>',
                '--agree-tos',
                '--no-eff-email',
                '--non-interactive',
                '--domains', '*.lxnd.cloud,lxnd.cloud',
                '--manual-auth-hook', '/root/lxnd/dns_auth_hook.sh',  # Custom DNS hook if available
                '--manual-cleanup-hook', '/root/lxnd/dns_cleanup_hook.sh'
            ]

            print("Attempting Let's Encrypt wildcard certificate generation...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Let's Encrypt stores certificates in /etc/letsencrypt/live/
                ssl_dir = "/etc/letsencrypt/live/lxnd.cloud"

                if os.path.exists(f"{ssl_dir}/fullchain.pem") and os.path.exists(f"{ssl_dir}/privkey.pem"):
                    print("✅ Let's Encrypt wildcard certificate generated successfully!")
                    return True
                else:
                    print("❌ Certificate files not found after generation")
                    return False
            else:
                print(f"❌ Let's Encrypt wildcard generation failed: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Error generating Let's Encrypt wildcard certificate: {e}")
            return False

    def create_wildcard_ssl_context(self):
        """Create a wildcard SSL context that works for all subdomains"""
        import ssl
        import tempfile
        import os
        import subprocess

        # First try to use existing Let's Encrypt wildcard certificate
        letsencrypt_cert = "/etc/letsencrypt/live/lxnd.cloud/fullchain.pem"
        letsencrypt_key = "/etc/letsencrypt/live/lxnd.cloud/privkey.pem"

        if os.path.exists(letsencrypt_cert) and os.path.exists(letsencrypt_key):
            try:
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(letsencrypt_cert, letsencrypt_key)
                print("✅ Using existing Let's Encrypt wildcard certificate")
                return context
            except Exception as e:
                print(f"❌ Error loading Let's Encrypt wildcard certificate: {e}")

        print("🔒 Creating self-signed wildcard SSL certificate for *.lxnd.cloud...")

        try:
            # Create a wildcard certificate that covers all subdomains
            ssl_dir = "/root/lxnd/ssl/wildcard"
            os.makedirs(ssl_dir, exist_ok=True)

            cert_path = f"{ssl_dir}/cert.pem"
            key_path = f"{ssl_dir}/key.pem"

            # Create OpenSSL config for wildcard certificate
            config_content = f"""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = DE
ST = Germany
L = Berlin
O = LXND
CN = *.lxnd.cloud

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = *.lxnd.cloud
DNS.2 = lxnd.cloud
"""

            # Write config to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
                f.write(config_content)
                config_file = f.name

            try:
                # Generate private key
                key_cmd = ['openssl', 'genrsa', '-out', key_path, '2048']
                subprocess.run(key_cmd, capture_output=True, text=True, check=True)

                # Generate certificate signing request
                csr_path = f"{ssl_dir}/cert.csr"
                csr_cmd = [
                    'openssl', 'req', '-new',
                    '-key', key_path,
                    '-out', csr_path,
                    '-config', config_file
                ]
                subprocess.run(csr_cmd, capture_output=True, text=True, check=True)

                # Generate self-signed certificate
                cert_cmd = [
                    'openssl', 'x509', '-req',
                    '-in', csr_path,
                    '-signkey', key_path,
                    '-out', cert_path,
                    '-days', '365',
                    '-extensions', 'v3_req',
                    '-extfile', config_file
                ]

                result = subprocess.run(cert_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    # Set proper permissions
                    subprocess.run(['chmod', '600', key_path])
                    subprocess.run(['chmod', '644', cert_path])

                    # Clean up
                    os.unlink(csr_path)

                    # Create SSL context
                    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                    context.load_cert_chain(cert_path, key_path)

                    print(f"✅ Wildcard SSL certificate created: {cert_path}")
                    return context
                else:
                    print(f"❌ Failed to create wildcard certificate: {result.stderr}")
                    return None

            finally:
                # Clean up config file
                os.unlink(config_file)

        except Exception as e:
            print(f"❌ Error creating wildcard certificate: {e}")
            return None

    def has_certificates(self):
        """Check if any certificates are available"""
        return len(self.certificates) > 0

# Global SSL manager instance
ssl_manager = SSLManager()

def start_https():
    """Start HTTPS server with SSL certificate"""
    ssl_context = ssl_manager.get_ssl_context('lxnd.cloud')

    if ssl_context:
        print("🔒 Starting HTTPS server on port 443...")
        app.run(debug=False, host='0.0.0.0', port=443, ssl_context=ssl_context)
    else:
        print("❌ No SSL certificate found!")
        print("Please generate SSL certificate first or start in HTTP mode.")
        return


# Main entry point
def clear_console():
    """Clear the console screen"""
    import os
    os.system('cls' if os.name == 'nt' else 'clear')

def print_ascii_logo():
    """Print LXND ASCII logo"""
    logo = """
    ██╗     ██╗  ██╗███╗   ██╗██████╗
    ██║     ╚██╗██╔╝████╗  ██║██╔══██╗
    ██║      ╚███╔╝ ██╔██╗ ██║██║  ██║
    ██║      ██╔██╗ ██║╚██╗██║██║  ██║
    ███████╗██╔╝ ██╗██║ ╚████║██████╔╝
    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═════╝

    LXND Multi-Tool Platform v1.0.0.4
    """
    print(logo)

def print_service_table(services):
    """Print services in a nice table format"""
    print("\n" + "="*60)
    print("| SERVICE                | PORT  | STATUS     | PROTOCOL |")
    print("|" + "-"*58 + "|")

    for service in services:
        name = service['name'].ljust(22)
        port = str(service['port']).ljust(5)
        status = service['status'].ljust(10)
        protocol = service['protocol'].ljust(8)
        print(f"| {name} | {port} | {status} | {protocol} |")

    print("="*60)


# MySQL Administration Routes
@app.route('/admin/mysql')
@login_required
@admin_required
def mysql_admin():
    """MySQL administration dashboard"""
    try:
        with get_mysql_manager() as db:
            # Get database statistics
            stats = db.get_database_stats()

            # Get connection info
            connection_info = {
                'host': db.credentials['mysql']['host'],
                'port': db.credentials['mysql']['port'],
                'database': 'lxnd_main'
            }

            # Get phpMyAdmin URL
            phpmyadmin_url = db.credentials['phpmyadmin']['url']

            # Get recent backups
            backups = get_recent_backups()

            return render_template('admin/mysql_admin.html',
                                 stats=stats,
                                 connection_info=connection_info,
                                 phpmyadmin_url=phpmyadmin_url,
                                 backups=backups,
                                 messages=messages)
    except Exception as e:
        flash(f'MySQL Error: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/admin/mysql', methods=['POST'])
@login_required
@admin_required
def mysql_admin_post():
    """Handle MySQL admin actions"""
    action = request.form.get('action')

    try:
        with get_mysql_manager() as db:
            if action == 'backup':
                backup_file = db.backup_database()
                if backup_file:
                    flash('Backup created successfully', 'success')
                else:
                    flash('Backup failed', 'error')

            elif action == 'optimize':
                # Optimize all tables
                tables = ['users', 'projects', 'files', 'email_accounts', 'emails', 'smtp_servers', 'notification_templates']
                for table in tables:
                    db.execute_query(f"OPTIMIZE TABLE {table}")
                flash('Tables optimized', 'success')

            elif action == 'query':
                sql_query = request.form.get('sql_query', '').strip()
                read_only = request.form.get('read_only') == 'on'

                if not sql_query:
                    flash('No query entered', 'error')
                    return redirect(url_for('mysql_admin'))

                # Check if read-only mode is enabled
                if read_only and not sql_query.upper().startswith('SELECT'):
                    flash('Only SELECT queries are allowed in read-only mode', 'error')
                    return redirect(url_for('mysql_admin'))

                # Execute query
                if sql_query.upper().startswith('SELECT'):
                    result = db.execute_query(sql_query, fetch=True)
                    query_result = {'data': result, 'error': None}
                else:
                    success = db.execute_query(sql_query)
                    if success:
                        query_result = {'data': None, 'affected_rows': 'Unbekannt', 'error': None}
                    else:
                        query_result = {'data': None, 'error': 'Query fehlgeschlagen'}

                # Re-render with query result
                stats = db.get_database_stats()
                connection_info = {
                    'host': db.credentials['mysql']['host'],
                    'port': db.credentials['mysql']['port'],
                    'database': 'lxnd_main'
                }
                phpmyadmin_url = db.credentials['phpmyadmin']['url']
                backups = get_recent_backups()

                return render_template('admin/mysql_admin.html',
                                     stats=stats,
                                     connection_info=connection_info,
                                     phpmyadmin_url=phpmyadmin_url,
                                     backups=backups,
                                     query_result=query_result,
                                     messages=messages)

    except Exception as e:
        flash(f'Error: {str(e)}', 'error')

    return redirect(url_for('mysql_admin'))

def get_recent_backups():
    """Get list of recent database backups"""
    backup_path = '/root/lxnd/backups'
    backups = []

    if os.path.exists(backup_path):
        for filename in os.listdir(backup_path):
            if filename.endswith('.sql'):
                filepath = os.path.join(backup_path, filename)
                stat = os.stat(filepath)
                backups.append({
                    'name': filename,
                    'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    'size': f"{stat.st_size / 1024 / 1024:.2f} MB"
                })

    return sorted(backups, key=lambda x: x['date'], reverse=True)[:10]

@app.route('/admin/mysql/download_backup/<filename>')
@login_required
@admin_required
def download_backup(filename):
    """Download database backup file"""
    backup_path = '/root/lxnd/backups'
    return send_from_directory(backup_path, filename, as_attachment=True)

# Link Shortener Routes
@app.route('/shorten')
def shorten_url_page():
    """Link shortener main page"""
    form = ShortenUrlForm()
    recent_urls = []

    if current_user.is_authenticated:
        recent_urls = ShortenedUrl.query.filter_by(user_id=current_user.id).order_by(
            ShortenedUrl.created_at.desc()
        ).limit(10).all()

    return render_template('shortener/index.html', form=form, recent_urls=recent_urls, messages=messages)

@app.route('/shorten', methods=['POST'])
def create_short_url():
    """Create a new shortened URL"""
    form = ShortenUrlForm()

    if form.validate_on_submit():
        try:
            # Check if custom code is provided and available
            short_code = None
            if form.custom_code.data and form.custom_code.data.strip():
                custom_code = form.custom_code.data.strip()
                # Validate custom code format (alphanumeric, hyphens, underscores only)
                import re
                if not re.match(r'^[a-zA-Z0-9_-]+$', custom_code):
                    flash('Custom code can only contain letters, numbers, hyphens, and underscores.', 'error')
                    return redirect(url_for('shorten_url_page'))

                if ShortenedUrl.query.filter_by(short_code=custom_code).first():
                    flash('Custom code already exists. Please choose another.', 'error')
                    return redirect(url_for('shorten_url_page'))
                short_code = custom_code

            # Create new shortened URL
            shortened_url = ShortenedUrl(
                original_url=form.original_url.data,
                title=form.title.data.strip() if form.title.data and form.title.data.strip() else None,
                description=form.description.data.strip() if form.description.data and form.description.data.strip() else None,
                expires_at=form.expires_at.data if form.expires_at.data else None,
                is_public=form.is_public.data == 'true',
                user_id=current_user.id if current_user.is_authenticated else None,
                short_code=short_code
            )

            # Set password if provided
            if form.password.data and form.password.data.strip():
                shortened_url.set_password(form.password.data.strip())

            db.session.add(shortened_url)
            db.session.commit()

            short_url = shortened_url.get_short_url(request)
            flash(f'URL shortened successfully! Your short URL: {short_url}', 'success')

            return redirect(url_for('shorten_url_page'))

        except Exception as e:
            flash(f'Error creating short URL: {e}', 'error')
            return redirect(url_for('shorten_url_page'))

    # Form validation failed
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{field}: {error}', 'error')

    return redirect(url_for('shorten_url_page'))

@app.route('/s/<short_code>')
def redirect_short_url(short_code):
    """Redirect to original URL"""
    shortened_url = ShortenedUrl.query.filter_by(short_code=short_code).first_or_404()

    # Check if URL is active
    if not shortened_url.is_active:
        flash('This link has been disabled.', 'error')
        return render_template('shortener/error.html', message='Link Disabled', messages=messages), 410

    # Check if URL has expired
    if shortened_url.is_expired():
        flash('This link has expired.', 'error')
        return render_template('shortener/error.html', message='Link Expired', messages=messages), 410

    # Check if password protected
    if shortened_url.password_hash:
        if 'url_passwords' not in session:
            session['url_passwords'] = {}

        if str(shortened_url.id) not in session['url_passwords']:
            return redirect(url_for('url_password_check', short_code=short_code))

    # Record click analytics
    try:
        click = UrlClick(
            url_id=shortened_url.id,
            ip_address=request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr),
            user_agent=request.headers.get('User-Agent'),
            referer=request.headers.get('Referer')
        )

        # Parse user agent for device info (basic)
        user_agent = request.headers.get('User-Agent', '').lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            click.device_type = 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            click.device_type = 'tablet'
        else:
            click.device_type = 'desktop'

        # Basic browser detection
        if 'chrome' in user_agent:
            click.browser = 'Chrome'
        elif 'firefox' in user_agent:
            click.browser = 'Firefox'
        elif 'safari' in user_agent:
            click.browser = 'Safari'
        elif 'edge' in user_agent:
            click.browser = 'Edge'

        db.session.add(click)

        # Update URL statistics
        shortened_url.click_count += 1
        shortened_url.last_clicked = datetime.utcnow()

        db.session.commit()

    except Exception as e:
        # Don't fail redirect if analytics fail
        print(f"Analytics error: {e}")

    # Redirect to original URL
    return redirect(shortened_url.original_url)

@app.route('/s/<short_code>/password', methods=['GET', 'POST'])
def url_password_check(short_code):
    """Password check for protected URLs"""
    shortened_url = ShortenedUrl.query.filter_by(short_code=short_code).first_or_404()

    if not shortened_url.password_hash:
        return redirect(url_for('redirect_short_url', short_code=short_code))

    form = UrlPasswordForm()

    if form.validate_on_submit():
        if shortened_url.check_password(form.password.data):
            # Store password in session
            if 'url_passwords' not in session:
                session['url_passwords'] = {}
            session['url_passwords'][str(shortened_url.id)] = True

            flash('Password correct! Redirecting...', 'success')
            return redirect(url_for('redirect_short_url', short_code=short_code))
        else:
            flash('Incorrect password. Please try again.', 'error')
    elif request.method == 'POST':
        # Form validation failed
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')

    return render_template('shortener/password.html', form=form, url=shortened_url, messages=messages)

@app.route('/admin/shortener')
@login_required
@admin_required
def admin_shortener():
    """Admin shortener dashboard"""
    page = request.args.get('page', 1, type=int)

    # Get all shortened URLs with pagination
    urls = ShortenedUrl.query.order_by(ShortenedUrl.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # Statistics
    total_urls = ShortenedUrl.query.count()
    total_clicks = db.session.query(db.func.sum(ShortenedUrl.click_count)).scalar() or 0
    active_urls = ShortenedUrl.query.filter_by(is_active=True).count()

    return render_template('admin/shortener.html',
                         urls=urls,
                         total_urls=total_urls,
                         total_clicks=total_clicks,
                         active_urls=active_urls,
                         messages=messages)

@app.route('/admin/shortener/<int:url_id>/toggle')
@login_required
@admin_required
def admin_toggle_url(url_id):
    """Toggle URL active status"""
    url = ShortenedUrl.query.get_or_404(url_id)
    url.is_active = not url.is_active
    db.session.commit()

    status = "activated" if url.is_active else "deactivated"
    flash(f'URL {status} successfully', 'success')
    return redirect(url_for('admin_shortener'))

@app.route('/admin/shortener/<int:url_id>/delete')
@login_required
@admin_required
def admin_delete_url(url_id):
    """Delete URL"""
    url = ShortenedUrl.query.get_or_404(url_id)
    db.session.delete(url)
    db.session.commit()

    flash('URL deleted successfully', 'success')
    return redirect(url_for('admin_shortener'))

@app.route('/api/shortener/stats')
@login_required
@admin_required
def api_shortener_stats():
    """API endpoint for shortener statistics"""
    try:
        total_urls = ShortenedUrl.query.count()
        total_clicks = db.session.query(db.func.sum(ShortenedUrl.click_count)).scalar() or 0

        return jsonify({
            'total_urls': total_urls,
            'total_clicks': total_clicks
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/lux/stats')
@login_required
@admin_required
def api_lux_stats():
    """API endpoint for Lux statistics"""
    try:
        total_licenses = LuxLicense.query.count()
        used_licenses = LuxLicense.query.filter(LuxLicense.used_by.isnot(None)).count()
        active_lux_users = User.query.filter_by(is_lux=True).count()

        return jsonify({
            'total_licenses': total_licenses,
            'used_licenses': used_licenses,
            'active_lux_users': active_lux_users
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Lux License Routes
@app.route('/lux')
@login_required
def lux_dashboard():
    """Lux license dashboard"""
    form = LuxLicenseForm()

    return render_template('lux/dashboard.html',
                         form=form,
                         user=current_user,
                         messages=messages)

@app.route('/lux/activate', methods=['POST'])
@login_required
def activate_lux_license():
    """Activate a Lux license"""
    form = LuxLicenseForm()

    if form.validate_on_submit():
        license_key = form.license_key.data.strip().upper()

        # Find license
        license_obj = LuxLicense.query.filter_by(license_key=license_key).first()

        if not license_obj:
            flash('Invalid license key.', 'error')
        elif not license_obj.is_active:
            flash('This license has been deactivated.', 'error')
        elif license_obj.is_used():
            flash('This license has already been used.', 'error')
        else:
            # Use the license
            if license_obj.use_license(current_user):
                db.session.commit()

                if license_obj.duration_days:
                    flash(f'Lux license activated! Valid for {license_obj.duration_days} days.', 'success')
                else:
                    flash('Permanent Lux license activated!', 'success')
            else:
                flash('Failed to activate license.', 'error')

    return redirect(url_for('lux_dashboard'))

@app.route('/admin/lux')
@login_required
@admin_required
def admin_lux_licenses():
    """Admin Lux license management"""
    page = request.args.get('page', 1, type=int)

    licenses = LuxLicense.query.order_by(LuxLicense.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # Add username to licenses for template
    for license in licenses.items:
        if license.used_by:
            user = db.session.get(User, license.used_by)
            license.used_by_username = user.username if user else 'Unknown'
        else:
            license.used_by_username = None

    form = CreateLuxLicenseForm()

    # Statistics
    total_licenses = LuxLicense.query.count()
    used_licenses = LuxLicense.query.filter(LuxLicense.used_by.isnot(None)).count()
    active_lux_users = User.query.filter_by(is_lux=True).count()

    return render_template('admin/lux_licenses.html',
                         licenses=licenses,
                         form=form,
                         total_licenses=total_licenses,
                         used_licenses=used_licenses,
                         active_lux_users=active_lux_users,
                         messages=messages)

@app.route('/admin/lux/create', methods=['POST'])
@login_required
@admin_required
def admin_create_lux_licenses():
    """Create new Lux licenses"""
    form = CreateLuxLicenseForm()

    if form.validate_on_submit():
        try:
            created_licenses = []

            for _ in range(form.quantity.data):
                license_obj = LuxLicense(
                    duration_days=form.duration_days.data if form.duration_days.data else None
                )
                db.session.add(license_obj)
                created_licenses.append(license_obj.license_key)

            db.session.commit()

            flash(f'Created {len(created_licenses)} Lux licenses successfully!', 'success')

        except Exception as e:
            flash(f'Error creating licenses: {e}', 'error')

    return redirect(url_for('admin_lux_licenses'))

@app.route('/admin/lux/<int:license_id>/toggle')
@login_required
@admin_required
def admin_toggle_lux_license(license_id):
    """Toggle Lux license active status"""
    license_obj = LuxLicense.query.get_or_404(license_id)

    if license_obj.is_used():
        flash('Cannot toggle used licenses', 'error')
    else:
        license_obj.is_active = not license_obj.is_active
        db.session.commit()

        status = "activated" if license_obj.is_active else "deactivated"
        flash(f'License {status} successfully', 'success')

    return redirect(url_for('admin_lux_licenses'))

@app.route('/admin/lux/<int:license_id>/delete')
@login_required
@admin_required
def admin_delete_lux_license(license_id):
    """Delete Lux license"""
    license_obj = LuxLicense.query.get_or_404(license_id)

    if license_obj.is_used():
        # If license is used, also revoke Lux status from user
        user = db.session.get(User, license_obj.used_by)
        if user:
            user.is_lux = False
            user.lux_expires_at = None

            # Update Discord role if user has Discord linked
            if user.discord_id:
                update_discord_role(user.discord_id, 'lux', False)
                flash(f'Revoked Lux status from user {user.username} and removed Discord role', 'info')
            else:
                flash(f'Revoked Lux status from user {user.username}', 'info')

    db.session.delete(license_obj)
    db.session.commit()

    flash('License deleted successfully', 'success')
    return redirect(url_for('admin_lux_licenses'))

@app.route('/api/check/<api_key>/<license_key>')
def api_check_license(api_key, license_key):
    """API endpoint to check license status"""
    # Verify API key
    if api_key != 'tBVXQ569ekTY2zlSVLsErw':
        return jsonify({'error': 'Invalid API key'}), 401

    license_obj = LuxLicense.query.filter_by(license_key=license_key.upper()).first()

    if not license_obj:
        return jsonify({
            'valid': False,
            'error': 'License not found'
        })

    if not license_obj.is_active:
        return jsonify({
            'valid': False,
            'error': 'License deactivated'
        })

    if license_obj.is_used():
        user = db.session.get(User, license_obj.used_by)
        return jsonify({
            'valid': True,
            'used': True,
            'used_by': user.username if user else 'Unknown',
            'used_at': license_obj.used_at.isoformat() if license_obj.used_at else None,
            'duration_days': license_obj.duration_days,
            'permanent': license_obj.duration_days is None
        })

    return jsonify({
        'valid': True,
        'used': False,
        'duration_days': license_obj.duration_days,
        'permanent': license_obj.duration_days is None,
        'created_at': license_obj.created_at.isoformat()
    })

# Admin Website Management
@app.route('/admin/websites')
@login_required
@admin_required
def admin_websites():
    """Admin website management"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    query = Website.query.join(User)

    if search:
        query = query.filter(
            db.or_(
                Website.subdomain.contains(search),
                Website.title.contains(search),
                User.username.contains(search)
            )
        )

    websites = query.order_by(Website.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # Get statistics
    total_websites = Website.query.count()
    active_websites = Website.query.filter_by(is_active=True).count()
    sftp_enabled = Website.query.filter_by(sftp_enabled=True).count()

    stats = {
        'total': total_websites,
        'active': active_websites,
        'ssl_enabled': total_websites,  # All websites have wildcard SSL
        'sftp_enabled': sftp_enabled
    }

    return render_template('admin/websites.html', websites=websites, stats=stats, search=search, messages=messages)

@app.route('/admin/websites/<int:website_id>/toggle_status', methods=['POST'])
@login_required
@admin_required
def admin_toggle_website_status(website_id):
    """Toggle website active status"""
    website = Website.query.get_or_404(website_id)
    website.is_active = not website.is_active
    db.session.commit()

    status = "activated" if website.is_active else "deactivated"
    flash(f'Website "{website.subdomain}.lxnd.cloud" has been {status}')
    return redirect(url_for('admin_websites'))

@app.route('/admin/websites/<int:website_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_website(website_id):
    """Admin delete website"""
    website = Website.query.get_or_404(website_id)

    try:
        # Delete all files from filesystem
        website_dir = os.path.join('websites', website.subdomain)
        if os.path.exists(website_dir):
            import shutil
            shutil.rmtree(website_dir)

        # Remove SFTP user if exists
        if website.sftp_username:
            try:
                import subprocess
                subprocess.run(['userdel', '-r', website.sftp_username], capture_output=True)
            except Exception as e:
                print(f"Error removing SFTP user: {e}")

        # Delete from database
        db.session.delete(website)
        db.session.commit()

        flash(f'Website "{website.subdomain}.lxnd.cloud" deleted successfully!')

    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting website: {str(e)}')

    return redirect(url_for('admin_websites'))

# Website Service Routes
@app.route('/websites')
@login_required
def websites():
    """Website management dashboard"""
    user_websites = Website.query.filter_by(user_id=current_user.id).all()
    form = WebsiteForm()
    return render_template('websites.html', websites=user_websites, form=form, messages=messages)

@app.route('/websites/create', methods=['POST'])
@login_required
def create_website():
    """Create a new website"""
    form = WebsiteForm()

    if form.validate_on_submit():
        # Check if subdomain already exists
        existing = Website.query.filter_by(subdomain=form.subdomain.data.lower()).first()
        if existing:
            flash('Subdomain already exists. Please choose a different one.')
            return redirect(url_for('websites'))

        # Check user limits (regular users: 1 website, Lux users: 5 websites, admins: unlimited)
        user_websites_count = Website.query.filter_by(user_id=current_user.id).count()
        max_websites = float('inf') if current_user.is_admin else (5 if current_user.is_lux_active() else 1)

        if user_websites_count >= max_websites:
            flash(f'You have reached your website limit ({max_websites} websites)')
            return redirect(url_for('websites'))

        website = Website(
            user_id=current_user.id,
            subdomain=form.subdomain.data.lower(),
            title=form.title.data,
            description=form.description.data,
            storage_limit_mb=2048 if current_user.is_lux_active() else (10240 if current_user.is_admin else 100)
        )

        # Create website directory
        website_dir = os.path.join('websites', website.subdomain)
        os.makedirs(website_dir, exist_ok=True)

        db.session.add(website)
        db.session.commit()

        # Setup SFTP access immediately
        try:
            # Setup SFTP access
            if website.setup_sftp_access():
                website.sftp_enabled = True
                flash('SFTP access configured successfully!', 'success')
            else:
                website.sftp_enabled = False
                flash('Website created, but SFTP setup failed.', 'warning')

            # SSL automatically enabled via wildcard certificate
            website.ssl_enabled = True  # Wildcard SSL covers all *.lxnd.cloud domains

            db.session.commit()

        except Exception as e:
            flash(f'Website created, but some features failed to initialize: {e}', 'warning')

        flash(f'Website created successfully! Visit: {website.get_url()}')
        return redirect(url_for('website_detail', subdomain=website.subdomain))

    flash('Error creating website. Please check your input.')
    return redirect(url_for('websites'))

@app.route('/websites/<subdomain>')
@login_required
def website_detail(subdomain):
    """Website detail and file management"""
    website = Website.query.filter_by(subdomain=subdomain, user_id=current_user.id).first_or_404()
    files = WebsiteFile.query.filter_by(website_id=website.id).order_by(WebsiteFile.uploaded_at.desc()).all()
    form = WebsiteFileUploadForm()

    # Update storage usage
    website.update_storage_usage()
    db.session.commit()

    return render_template('website_detail.html', website=website, files=files, form=form, messages=messages)

@app.route('/websites/<subdomain>/upload', methods=['POST'])
@login_required
def upload_website_file(subdomain):
    """Upload file to website"""
    from werkzeug.utils import secure_filename

    website = Website.query.filter_by(subdomain=subdomain, user_id=current_user.id).first_or_404()
    form = WebsiteFileUploadForm()

    if form.validate_on_submit():
        file = form.file.data
        if file and file.filename:
            # Check file size (max 10MB per file)
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset to beginning

            if file_size > 10 * 1024 * 1024:  # 10MB
                flash('File too large. Maximum file size is 10MB.')
                return redirect(url_for('website_detail', subdomain=subdomain))

            # Check storage limit
            file_size_mb = file_size / (1024 * 1024)
            if not website.can_upload(file_size_mb):
                flash(f'Not enough storage space. You have {website.storage_limit_mb - website.storage_used_mb:.1f}MB remaining.')
                return redirect(url_for('website_detail', subdomain=subdomain))

            # Secure filename
            filename = secure_filename(file.filename)
            if not filename:
                flash('Invalid filename.')
                return redirect(url_for('website_detail', subdomain=subdomain))

            # Create website directory if it doesn't exist
            website_dir = os.path.join('websites', website.subdomain)
            os.makedirs(website_dir, exist_ok=True)

            # Save file
            file_path = os.path.join(website_dir, filename)
            file.save(file_path)

            # Handle index.html setting
            if form.is_index.data or filename.lower() == 'index.html':
                # Remove index flag from other files
                WebsiteFile.query.filter_by(website_id=website.id, is_index=True).update({'is_index': False})

            # Create database record
            website_file = WebsiteFile(
                website_id=website.id,
                filename=filename,
                original_filename=file.filename,
                file_path=file_path,
                file_size=file_size,
                mime_type=file.content_type,
                is_index=form.is_index.data or filename.lower() == 'index.html'
            )

            db.session.add(website_file)
            website.update_storage_usage()
            db.session.commit()

            flash(f'File "{filename}" uploaded successfully!')
        else:
            flash('No file selected.')

    return redirect(url_for('website_detail', subdomain=subdomain))

@app.route('/websites/<subdomain>/delete/<int:file_id>', methods=['POST'])
@login_required
def delete_website_file(subdomain, file_id):
    """Delete website file"""
    website = Website.query.filter_by(subdomain=subdomain, user_id=current_user.id).first_or_404()
    file_obj = WebsiteFile.query.filter_by(id=file_id, website_id=website.id).first_or_404()

    # Delete physical file
    try:
        if os.path.exists(file_obj.file_path):
            os.remove(file_obj.file_path)
    except Exception as e:
        flash(f'Error deleting file: {e}')
        return redirect(url_for('website_detail', subdomain=subdomain))

    # Delete database record
    db.session.delete(file_obj)
    website.update_storage_usage()
    db.session.commit()

    flash(f'File "{file_obj.filename}" deleted successfully!')
    return redirect(url_for('website_detail', subdomain=subdomain))

@app.route('/websites/<subdomain>/delete', methods=['POST'])
@login_required
def delete_website(subdomain):
    """Delete website and all associated files"""
    website = Website.query.filter_by(subdomain=subdomain, user_id=current_user.id).first_or_404()

    try:
        # Delete all files from filesystem
        website_dir = os.path.join('websites', website.subdomain)
        if os.path.exists(website_dir):
            import shutil
            shutil.rmtree(website_dir)

        # Remove SFTP user if exists
        if website.sftp_username:
            try:
                import subprocess
                subprocess.run(['userdel', '-r', website.sftp_username], capture_output=True)
            except Exception as e:
                print(f"Error removing SFTP user: {e}")

        # SSL certificate removal not needed - using wildcard SSL

        # Delete from database
        db.session.delete(website)
        db.session.commit()

        flash(f'Website "{website.title}" deleted successfully!')

    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting website: {str(e)}')

    return redirect(url_for('websites'))

# SSL generation removed - using wildcard SSL certificate for all domains

@app.route('/websites/<subdomain>/fix_sftp', methods=['POST'])
@login_required
def fix_sftp_permissions(subdomain):
    """Fix SFTP permissions for website"""
    website = Website.query.filter_by(subdomain=subdomain, user_id=current_user.id).first_or_404()

    try:
        # Re-setup SFTP access to fix permissions
        if website.setup_sftp_access():
            website.sftp_enabled = True
            db.session.commit()
            flash('SFTP permissions fixed successfully!')
        else:
            flash('Failed to fix SFTP permissions.')
    except Exception as e:
        flash(f'Error fixing SFTP permissions: {str(e)}')

    return redirect(url_for('website_detail', subdomain=subdomain))

# Subdomain handler for websites
@app.before_request
def handle_subdomain():
    """Handle subdomain requests for websites"""
    host = request.headers.get('Host', '')

    # Check if it's a subdomain request
    if '.lxnd.cloud' in host and not host.startswith('www.') and host != 'lxnd.cloud':
        subdomain = host.split('.')[0]

        # Find website by subdomain
        website = Website.query.filter_by(subdomain=subdomain, is_active=True).first()
        if website:
            # Serve website content
            return serve_website(subdomain)

    return None

def serve_website(subdomain):
    """Serve website content"""
    website = Website.query.filter_by(subdomain=subdomain, is_active=True).first_or_404()

    # Get requested path
    path = request.path.lstrip('/')

    # Handle Let's Encrypt ACME challenge
    if path.startswith('.well-known/acme-challenge/'):
        acme_path = os.path.join('websites', subdomain, path)
        if os.path.exists(acme_path):
            return send_from_directory(os.path.join('websites', subdomain), path)
        else:
            return "Challenge file not found", 404

    if not path:
        # Serve index file
        index_file = WebsiteFile.query.filter_by(website_id=website.id, is_index=True).first()
        if index_file and os.path.exists(index_file.file_path):
            return send_from_directory(os.path.dirname(index_file.file_path), index_file.filename)
        else:
            # Default index.html
            index_path = os.path.join('websites', subdomain, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(os.path.join('websites', subdomain), 'index.html')
            else:
                return f"<h1>Welcome to {website.title}</h1><p>{website.description or 'Website coming soon!'}</p>"
    else:
        # Serve specific file
        file_path = os.path.join('websites', subdomain, path)
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return send_from_directory(os.path.join('websites', subdomain), path)
        else:
            return "File not found", 404

# Legal Pages Routes
@app.route('/impressum')
def impressum():
    """Impressum page"""
    return render_template('legal/impressum.html', messages=messages)

@app.route('/privacy-policy')
def privacy_policy():
    """Privacy policy page"""
    return render_template('legal/privacy_policy.html', messages=messages)

@app.route('/terms-of-service')
def terms_of_service():
    """Terms of service page"""
    return render_template('legal/terms_of_service.html', messages=messages)

@app.route('/cookie-policy')
def cookie_policy():
    """Cookie policy page"""
    return render_template('legal/cookie_policy.html', messages=messages)

def initialize_system():
    """Initialize database and create admin user"""
    print("🔧 Initializing LXND system...")

    with app.app_context():
        # Create database tables
        db.create_all()
        print("  ✓ Database tables created")

        # Create admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            # Check if email already exists
            existing_email = User.query.filter_by(email='<EMAIL>').first()
            if existing_email:
                print("  ✓ Admin user already exists (by email)")
            else:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("  ✓ Admin user created: admin / admin123")
        else:
            print("  ✓ Admin user already exists")

        # Create SSL directory
        import os
        os.makedirs('/root/lxnd/ssl', exist_ok=True)
        print("  ✓ SSL directory ready")

def show_startup_banner():
    """Show LXND startup banner"""
    print("\n" + "="*60)
    print("🚀 LXND - Luxury Cloud Platform")
    print("="*60)
    print("📧 Email Service  | 🌐 Website Hosting | 🔗 URL Shortener")
    print("🔒 SSL Certificates | 📁 SFTP Access | 👥 User Management")
    print("="*60)

def show_ssl_status():
    """Show SSL certificate status"""
    print("\n🔒 SSL Certificate Status:")

    if ssl_manager.has_certificates():
        for domain, cert_info in ssl_manager.certificates.items():
            status_icon = "🟢" if cert_info['type'] == 'letsencrypt' else "🟡"
            print(f"  {status_icon} {domain} ({cert_info['type']})")
    else:
        print("  ❌ No SSL certificates found")
        print("  💡 Generate certificates via Dashboard > Website Management")

def get_startup_mode():
    """Get startup mode from user"""
    print("\n🎛️  Startup Options:")
    print("  1. HTTP only (Port 5000) - Development")
    print("  2. HTTPS only (Port 443) - Production")
    print("  3. Auto-detect - Smart selection")

    while True:
        choice = input("\nSelect mode (1-3) [3]: ").strip() or "3"

        if choice in ['1', '2', '3']:
            return choice
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

def start_ssl_proxy():
    """Start SSL proxy in background"""
    import subprocess
    import time

    print("🔒 Starting SSL Proxy for multi-domain support...")

    try:
        # Start SSL proxy as background process
        proxy_process = subprocess.Popen([
            'python3', 'ssl_proxy_server.py'
        ], cwd='/root/lxnd')

        # Give it a moment to start
        time.sleep(2)

        # Check if it's running
        if proxy_process.poll() is None:
            print("✅ SSL Proxy started successfully")
            return proxy_process
        else:
            print("❌ SSL Proxy failed to start")
            return None
    except Exception as e:
        print(f"❌ Error starting SSL Proxy: {e}")
        return None

def start_application(mode):
    """Start application based on selected mode"""
    print(f"\n🚀 Starting LXND in mode {mode}...")

    if mode == "1":
        # HTTP only
        print("🌐 Starting HTTP server on port 5000...")
        print("📡 Access: http://lxnd.cloud:5000")
        print("🔧 Dashboard: http://lxnd.cloud:5000/admin")
        print("📧 Email: http://lxnd.cloud:5000/email")
        app.run(debug=False, host='0.0.0.0', port=5000)

    elif mode == "2":
        # HTTPS with wildcard SSL certificate
        print("🔒 Starting HTTPS server with wildcard SSL support...")

        # Try to get existing Let's Encrypt certificate first
        ssl_context = ssl_manager.get_ssl_context('lxnd.cloud')

        # If no Let's Encrypt certificate, create wildcard certificate
        if not ssl_context:
            print("🔒 No Let's Encrypt certificate found, creating wildcard certificate...")
            ssl_context = ssl_manager.create_wildcard_ssl_context()

        if ssl_context:
            print("📡 Access: https://lxnd.cloud")
            print("🔧 Dashboard: https://lxnd.cloud/admin")
            print("📧 Email: https://lxnd.cloud/email")
            print("🔗 Wildcard SSL support for all subdomains")
            print("⚠️  Note: Wildcard certificates may show browser warnings")
            print("💡 Generate Let's Encrypt certificates for production use")

            app.run(debug=False, host='0.0.0.0', port=443, ssl_context=ssl_context)
        else:
            print("❌ Failed to create SSL certificate!")
            print("🔄 Falling back to HTTP mode...")
            start_application("1")

    elif mode == "3":
        # Auto-detect
        if ssl_manager.has_certificates():
            print("✅ SSL certificates detected - Starting HTTPS mode with SSL Proxy")
            start_application("2")
        else:
            print("⚠️  No SSL certificates - Starting HTTP mode")
            start_application("1")

if __name__ == '__main__':
    try:
        # Show banner
        show_startup_banner()

        # Initialize system
        initialize_system()

        # Load SSL certificates
        ssl_manager.load_certificates()

        # Show SSL status
        show_ssl_status()

        # Get startup mode
        mode = get_startup_mode()

        # Start application
        start_application(mode)

    except KeyboardInterrupt:
        print("\n\n👋 LXND shutdown complete. Goodbye!")
    except Exception as e:
        print(f"\n❌ Startup error: {e}")
        print("🔄 Please check configuration and try again.")

