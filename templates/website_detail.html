{% extends "base.html" %}

{% block content %}
<div class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-4">
                <a href="{{ url_for('websites') }}" class="text-gray-400 hover:text-white transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-white">{{ website.title }}</h1>
                    <p class="text-gray-400">{{ website.subdomain }}.lxnd.cloud</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ website.get_url() }}" target="_blank"
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    Visit Website
                </a>
                <button onclick="openUploadModal()"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-upload mr-2"></i>
                    Upload File
                </button>
                <button onclick="openDeleteModal()"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-trash mr-2"></i>
                    Delete Website
                </button>
            </div>
        </div>

        <!-- Website Info -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
            <!-- Storage Usage -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-white">Storage Usage</h3>
                    <i class="fas fa-hdd text-blue-400"></i>
                </div>
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-400">Used</span>
                        <span class="text-white font-medium">{{ website.get_storage_used_formatted() }} / {{ (website.get_storage_limit_effective() / 1024)|round(1) }}GB</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-3">
                        <div class="h-3 rounded-full {% if website.get_storage_percent() >= 90 %}bg-red-500{% elif website.get_storage_percent() >= 70 %}bg-yellow-500{% else %}bg-blue-500{% endif %}"
                             style="width: {{ website.get_storage_percent() }}%"></div>
                    </div>
                    <p class="text-xs text-gray-400 mt-1">{{ website.get_storage_percent() }}% used</p>
                </div>
            </div>

            <!-- File Count -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-white">Files</h3>
                    <i class="fas fa-file text-green-400"></i>
                </div>
                <div class="text-3xl font-bold text-white mb-2">{{ files|length }}</div>
                <p class="text-gray-400">Total files uploaded</p>
            </div>

            <!-- Website Status -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-white">Status</h3>
                    {% if website.is_active %}
                        <i class="fas fa-check-circle text-green-400"></i>
                    {% else %}
                        <i class="fas fa-times-circle text-red-400"></i>
                    {% endif %}
                </div>
                <div class="text-xl font-bold {% if website.is_active %}text-green-400{% else %}text-red-400{% endif %} mb-2">
                    {% if website.is_active %}Active{% else %}Inactive{% endif %}
                </div>
                <p class="text-gray-400">Created {{ website.created_at.strftime('%B %d, %Y') }}</p>
            </div>

            <!-- SSL & SFTP -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-white">SSL & SFTP</h3>
                    <i class="fas fa-shield-alt text-purple-400"></i>
                </div>

                <!-- SSL Status -->
                <div class="mb-4 pb-4 border-b border-gray-700/50">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-400">SSL Certificate</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                            <i class="fas fa-shield-alt mr-1"></i>Wildcard SSL Enabled
                        </span>
                    </div>
                    <div class="text-xs text-gray-500">
                        Automatically secured with wildcard SSL certificate for *.lxnd.cloud
                    </div>
                </div>

                <!-- SFTP Status -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-400">SFTP Access</span>
                        {% if website.sftp_enabled and website.sftp_username %}
                            <div class="flex space-x-2">
                                <button onclick="showSftpModal()" class="text-blue-400 hover:text-blue-300 text-sm">
                                    <i class="fas fa-info-circle mr-1"></i>View Details
                                </button>
                            </div>
                        {% else %}
                            <span class="text-red-400"><i class="fas fa-times mr-1"></i>Disabled</span>
                        {% endif %}
                    </div>
                    {% if website.sftp_enabled and website.sftp_username %}
                        <div class="text-xs text-gray-400">
                            Host: lxnd.cloud:22 • User: {{ website.sftp_username }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Files List -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
            <div class="px-6 py-4 border-b border-gray-700/50">
                <h3 class="text-xl font-bold text-white">Website Files</h3>
            </div>
            
            {% if files %}
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-800/50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">File</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Size</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Uploaded</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700/50">
                            {% for file in files %}
                            <tr class="hover:bg-white/5 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-file text-gray-400 mr-3"></i>
                                        <div>
                                            <div class="text-white font-medium">{{ file.filename }}</div>
                                            {% if file.is_index %}
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                                                    <i class="fas fa-home mr-1"></i>
                                                    Index Page
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-gray-300">{{ file.get_size_formatted() }}</td>
                                <td class="px-6 py-4 text-gray-300">{{ file.mime_type or 'Unknown' }}</td>
                                <td class="px-6 py-4 text-gray-300">{{ file.uploaded_at.strftime('%b %d, %Y') }}</td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <a href="{{ website.get_url() }}/{{ file.filename }}" target="_blank"
                                           class="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-blue-500/20 transition-all duration-300">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_website_file', subdomain=website.subdomain, file_id=file.id) }}" 
                                              style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this file?')">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" 
                                                    class="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/20 transition-all duration-300">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-16">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-400/30">
                        <i class="fas fa-file text-blue-300 text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-300 mb-2">No Files Yet</h4>
                    <p class="text-gray-400 mb-6">Upload your first file to get started</p>
                    <button onclick="openUploadModal()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                        <i class="fas fa-upload mr-2"></i>
                        Upload File
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Upload File Modal -->
<div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Upload File</h3>
            <button onclick="closeUploadModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form method="POST" action="{{ url_for('upload_website_file', subdomain=website.subdomain) }}" 
              enctype="multipart/form-data" class="space-y-4">
            {{ form.hidden_tag() }}
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Select File</label>
                {{ form.file(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700") }}
                <p class="text-xs text-gray-400 mt-1">Maximum file size: 10MB</p>
            </div>
            
            <div class="flex items-center">
                {{ form.is_index(class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2") }}
                <label class="ml-2 text-sm text-gray-300">Set as index page (homepage)</label>
            </div>
            
            <div class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <h4 class="text-blue-400 font-medium mb-2">Tips:</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• Upload an index.html file to create your homepage</li>
                    <li>• Supported formats: HTML, CSS, JS, images, and more</li>
                    <li>• Files will be accessible at {{ website.get_url() }}/filename</li>
                </ul>
            </div>
            
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeUploadModal()" 
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" 
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Upload File
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openUploadModal() {
    document.getElementById('uploadModal').classList.remove('hidden');
    document.getElementById('uploadModal').classList.add('flex');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadModal').classList.remove('flex');
}

function showSftpModal() {
    document.getElementById('sftpModal').classList.remove('hidden');
    document.getElementById('sftpModal').classList.add('flex');
}

function closeSftpModal() {
    document.getElementById('sftpModal').classList.add('hidden');
    document.getElementById('sftpModal').classList.remove('flex');
}

function openDeleteModal() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

function openDeleteModal() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

function copySftpInfo(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Show feedback
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.className = button.className.replace('bg-gray-600', 'bg-green-600');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.className = button.className.replace('bg-green-600', 'bg-gray-600');
        }, 2000);
    });
}
</script>

<!-- SFTP Info Modal -->
<div id="sftpModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-lg mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">SFTP Connection Details</h3>
            <button onclick="closeSftpModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        {% if website.get_sftp_info() %}
        {% set sftp = website.get_sftp_info() %}
        <div class="space-y-4">
            <div class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <h4 class="text-blue-400 font-medium mb-2">Connection Information</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Host:</span>
                        <div class="flex items-center space-x-2">
                            <code class="text-white bg-gray-700 px-2 py-1 rounded">{{ sftp.host }}</code>
                            <button onclick="copySftpInfo('{{ sftp.host }}')"
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Port:</span>
                        <div class="flex items-center space-x-2">
                            <code class="text-white bg-gray-700 px-2 py-1 rounded">{{ sftp.port }}</code>
                            <button onclick="copySftpInfo('{{ sftp.port }}')"
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Username:</span>
                        <div class="flex items-center space-x-2">
                            <code class="text-white bg-gray-700 px-2 py-1 rounded">{{ sftp.username }}</code>
                            <button onclick="copySftpInfo('{{ sftp.username }}')"
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Password:</span>
                        <div class="flex items-center space-x-2">
                            <code class="text-white bg-gray-700 px-2 py-1 rounded">{{ sftp.password }}</code>
                            <button onclick="copySftpInfo('{{ sftp.password }}')"
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                <h4 class="text-green-400 font-medium mb-2">How to Connect</h4>
                <div class="text-sm text-gray-300 space-y-2">
                    <p><strong>FileZilla:</strong></p>
                    <ul class="list-disc list-inside ml-4 text-xs">
                        <li>Protocol: SFTP - SSH File Transfer Protocol</li>
                        <li>Host: {{ sftp.host }}</li>
                        <li>Port: {{ sftp.port }}</li>
                        <li>Username: {{ sftp.username }}</li>
                        <li>Password: {{ sftp.password }}</li>
                    </ul>

                    <p><strong>WinSCP:</strong></p>
                    <ul class="list-disc list-inside ml-4 text-xs">
                        <li>File protocol: SFTP</li>
                        <li>Host name: {{ sftp.host }}</li>
                        <li>Port number: {{ sftp.port }}</li>
                        <li>User name: {{ sftp.username }}</li>
                        <li>Password: {{ sftp.password }}</li>
                    </ul>

                    <p><strong>Command Line:</strong></p>
                    <code class="block bg-gray-700 p-2 rounded mt-1 text-xs">
                        sftp {{ sftp.username }}@{{ sftp.host }}
                    </code>
                </div>
            </div>

            <div class="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                <h4 class="text-yellow-400 font-medium mb-2">Important Notes</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>• Upload files directly to your website directory</li>
                    <li>• Changes are immediately visible on your website</li>
                    <li>• Maximum file size: 10MB per file</li>
                    <li>• Total storage limit: {{ (website.get_storage_limit_effective() / 1024)|round(1) }}GB</li>
                    <li>• Upload index.html to create your homepage</li>
                </ul>
            </div>
        </div>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"></i>
            <h4 class="text-lg font-bold text-gray-300 mb-2">SFTP Not Available</h4>
            <p class="text-gray-400">SFTP access is not configured for this website.</p>
        </div>
        {% endif %}

        <div class="flex justify-end pt-4">
            <button onclick="closeSftpModal()"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Delete Website Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Delete Website</h3>
            <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mb-6">
            <div class="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                    <h4 class="text-red-400 font-medium">Warning: This action cannot be undone!</h4>
                </div>
                <p class="text-gray-300 text-sm">
                    This will permanently delete your website "<strong>{{ website.title }}</strong>" and all associated files.
                </p>
            </div>

            <div class="space-y-2 text-sm text-gray-300">
                <p><strong>What will be deleted:</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>All website files ({{ files|length }} files)</li>
                    <li>SFTP access credentials</li>
                    <li>SSL certificate</li>
                    <li>Subdomain: {{ website.subdomain }}.lxnd.cloud</li>
                </ul>
            </div>
        </div>

        <form method="POST" action="{{ url_for('delete_website', subdomain=website.subdomain) }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="flex space-x-3">
                <button type="button" onclick="closeDeleteModal()"
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-trash mr-2"></i>
                    Delete Website
                </button>
            </div>
        </form>
    </div>
</div>

{% endblock %}
