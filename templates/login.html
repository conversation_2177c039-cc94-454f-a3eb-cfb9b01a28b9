{% extends "base.html" %}
{% block title %}Sign In - LXND{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <i class="fas fa-sign-in-alt text-white text-2xl"></i>
            </div>
            <h2 class="text-4xl font-bold text-white mb-2">
                Welcome Back
            </h2>
            <p class="text-gray-400 text-lg">Sign in to your LXND account</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8">
            <form method="POST" action="{{ url_for('login') }}" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Username Field -->
                <div>
                    <label for="{{ form.username.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-user mr-2 text-blue-400"></i>
                        Username
                    </label>
                    {{ form.username(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300", placeholder="Enter your username") }}
                    {% if form.username.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.username.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    <label for="{{ form.password.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-lock mr-2 text-purple-400"></i>
                        Password
                    </label>
                    <div class="relative">
                        {{ form.password(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300", placeholder="Enter your password", id="password-input") }}
                        <button type="button" onclick="togglePassword()" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.password.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2">
                        <label for="remember" class="ml-2 text-sm text-gray-300">Remember me</label>
                    </div>
                    <a href="#" class="text-sm text-blue-400 hover:text-blue-300 transition-colors">
                        Forgot password?
                    </a>
                </div>

                <!-- Submit Button -->
                <div>
                    {{ form.submit(class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30") }}
                </div>
            </form>

            <!-- Divider -->
            <div class="mt-8 pt-6 border-t border-gray-700/50">
                <div class="text-center">
                    <p class="text-gray-400 text-sm">
                        Don't have an account?
                        <a href="{{ url_for('register') }}" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                            Sign up here
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Features Preview -->
        <div class="grid grid-cols-2 gap-4 mt-8">
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-shield-alt text-blue-400 text-2xl mb-2"></i>
                <p class="text-white font-medium text-sm">Secure</p>
                <p class="text-gray-400 text-xs">Protected access</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-rocket text-purple-400 text-2xl mb-2"></i>
                <p class="text-white font-medium text-sm">Fast</p>
                <p class="text-gray-400 text-xs">Quick access</p>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password-input');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('{{ form.username.id }}');
    if (usernameField) {
        usernameField.focus();
    }
});
</script>
{% endblock %}
