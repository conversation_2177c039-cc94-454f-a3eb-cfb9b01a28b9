<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <link rel="icon" type="image/x-icon" href="{{ url_for('download_file', file_id='4X5g5O5pLOf-udTa5gTxcA') }}">
      <meta name="google-adsense-account" content="ca-pub-****************">
    <title>{% block title %}{{ messages['base.html']['site_title'] }}{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <style>
        .license-key {
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <nav class="bg-gray-900 border-b border-gray-800">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="{{ url_for('index') }}" class="text-white font-bold text-xl">{{ messages['base.html']['site_title'] }}</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-4 items-center">
                        {% if current_user.is_authenticated %}
                            <a href="{{ url_for('dashboard') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_dashboard'] }}</a>
                            <a href="{{ url_for('websites') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Websites</a>
                            <a href="{{ url_for('files_dashboard') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_files'] }}</a>
                            <a href="{{ url_for('user_email_dashboard') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_email'] }}</a>
                            <a href="{{ url_for('shorten_url_page') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Shortener</a>
                            <a href="{{ url_for('lux_dashboard') }}" class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-300 hover:to-orange-400 px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-crown mr-1"></i>Lux
                            </a>
                            <a href="{{ url_for('api_docs') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_api_docs'] }}</a>
                            {% if current_user.is_admin %}
                                <a href="{{ url_for('admin_dashboard') }}" class="text-red-400 hover:text-red-300 px-3 py-2 rounded-md text-sm font-medium">
                                    <i class="fas fa-crown mr-1"></i>{{ messages['base.html']['nav_admin'] }}
                                </a>
                            {% endif %}
                        {% else %}
                            <a href="{{ url_for('index') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_home'] }}</a>
                            <a href="{{ url_for('shorten_url_page') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Shortener</a>
                            <a href="{{ url_for('api_docs') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_api_docs'] }}</a>
                        {% endif %}
                    </div>
                </div>
                <div class="hidden sm:ml-6 sm:flex sm:items-center">
                    {% if current_user.is_authenticated %}
                        <div class="ml-3 relative">
                            <div class="flex items-center">
                                <span class="text-gray-300 mr-2">{{ current_user.username }}</span>
                                <a href="{{ url_for('profile') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_profile'] }}</a>
                                <a href="{{ url_for('logout') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_logout'] }}</a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_login'] }}</a>
                        <a href="{{ url_for('register') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">{{ messages['base.html']['nav_register'] }}</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="flex items-center p-4 mb-4 text-sm text-blue-300 border border-blue-600 rounded-lg bg-blue-900" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                        </svg>
                        <span class="sr-only">{{ messages.get('base.html', {}).get('flash_info_sr', 'Info') if messages is mapping else 'Info' }}</span>
                        <div>{{ message }}</div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t border-gray-700 mt-16">
        <div class="max-w-7xl mx-auto px-4 py-12"><script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
     crossorigin="anonymous"></script>
<!-- footer -->
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-****************"
     data-ad-slot="5330830604"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-white font-bold text-xl mb-4">LXND</h3>
                    <p class="text-gray-400 mb-4">
                        Your all-in-one platform for project management, file sharing, email services, and URL shortening.
                    </p>
                    <div class="flex space-x-4">
                        <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white">
                            <i class="fas fa-envelope"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>

                <!-- Services -->
                <div>
                    <h4 class="text-white font-semibold mb-4">Services</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ url_for('websites') if current_user.is_authenticated else url_for('register') }}" class="text-gray-400 hover:text-white">Website Hosting</a></li>
                        <li><a href="{{ url_for('dashboard') if current_user.is_authenticated else url_for('register') }}" class="text-gray-400 hover:text-white">Project Management</a></li>
                        <li><a href="{{ url_for('files_dashboard') if current_user.is_authenticated else url_for('register') }}" class="text-gray-400 hover:text-white">File Sharing</a></li>
                        <li><a href="{{ url_for('shorten_url_page') }}" class="text-gray-400 hover:text-white">URL Shortener</a></li>
                        <li><a href="{{ url_for('user_email_dashboard') if current_user.is_authenticated else url_for('register') }}" class="text-gray-400 hover:text-white">Email Services</a></li>
                        <li><a href="{{ url_for('lux_dashboard') if current_user.is_authenticated else url_for('register') }}" class="text-gray-400 hover:text-white">Lux Premium</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h4 class="text-white font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ url_for('privacy_policy') }}" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="{{ url_for('terms_of_service') }}" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                        <li><a href="{{ url_for('impressum') }}" class="text-gray-400 hover:text-white">Impressum</a></li>
                        <li><a href="{{ url_for('cookie_policy') }}" class="text-gray-400 hover:text-white">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    © 2025 LXND. All rights reserved. |
                    <a href="{{ url_for('impressum') }}" class="hover:text-white">Impressum</a> |
                    <a href="{{ url_for('privacy_policy') }}" class="hover:text-white">Datenschutz</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Cookie Banner -->
    <div id="cookie-banner" class="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 p-4 z-50" style="display: none;">
        <div class="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between">
            <div class="text-gray-300 mb-4 md:mb-0">
                <p>Diese Website verwendet Cookies, um Ihnen die bestmögliche Erfahrung zu bieten.
                <a href="{{ url_for('cookie_policy') }}" class="text-blue-400 hover:text-blue-300 underline">Mehr erfahren</a></p>
            </div>
            <div class="flex space-x-4">
                <button onclick="acceptCookies()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Alle akzeptieren
                </button>
                <button onclick="acceptNecessaryCookies()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                    Nur notwendige
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.js"></script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
     crossorigin="anonymous"></script>

    <script>
    // Cookie Banner Logic
    document.addEventListener('DOMContentLoaded', function() {
        if (!getCookie('cookie_consent')) {
            document.getElementById('cookie-banner').style.display = 'block';
        }
    });

    function acceptCookies() {
        setCookie('cookie_consent', 'all', 365);
        setCookie('analytics_consent', 'true', 365);
        document.getElementById('cookie-banner').style.display = 'none';
    }

    function acceptNecessaryCookies() {
        setCookie('cookie_consent', 'necessary', 365);
        setCookie('analytics_consent', 'false', 365);
        document.getElementById('cookie-banner').style.display = 'none';
    }

    function setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = name + '=' + value + ';expires=' + expires.toUTCString() + ';path=/;SameSite=Lax';
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for(let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
