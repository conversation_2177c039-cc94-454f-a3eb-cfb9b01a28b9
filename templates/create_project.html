{% extends "base.html" %}
{% block title %}Create Project - LXND{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <i class="fas fa-plus text-white text-2xl"></i>
            </div>
            <h2 class="text-4xl font-bold text-white mb-2">
                Create New Project
            </h2>
            <p class="text-gray-400 text-lg">Start building something amazing</p>
        </div>

        <!-- Create Project Form -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8">
            <form method="POST" action="{{ url_for('create_project') }}" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Project Name Field -->
                <div>
                    <label for="{{ form.name.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-folder mr-2 text-blue-400"></i>
                        Project Name
                    </label>
                    {{ form.name(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300", placeholder="Enter project name") }}
                    {% if form.name.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.name.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">Choose a descriptive name for your project</p>
                </div>

                <!-- Project Description Field -->
                <div>
                    <label for="{{ form.description.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-align-left mr-2 text-purple-400"></i>
                        Description
                    </label>
                    {{ form.description(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 resize-none", rows="4", placeholder="Describe your project...") }}
                    {% if form.description.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.description.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">Optional: Add a description to help you remember what this project is about</p>
                </div>


                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 pt-6">
                    <a href="{{ url_for('dashboard') }}" 
                       class="flex-1 bg-gray-600/80 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 text-center border border-gray-500/30">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Cancel
                    </a>
                    {{ form.submit(class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30") }}
                </div>
            </form>
        </div>

        <!-- Project Benefits -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-cloud text-blue-400 text-2xl mb-2"></i>
                <p class="text-white font-medium text-sm">Cloud Storage</p>
                <p class="text-gray-400 text-xs">Secure file hosting</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-key text-green-400 text-2xl mb-2"></i>
                <p class="text-white font-medium text-sm">License Management</p>
                <p class="text-gray-400 text-xs">Generate & track keys</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-chart-line text-purple-400 text-2xl mb-2"></i>
                <p class="text-white font-medium text-sm">Analytics</p>
                <p class="text-gray-400 text-xs">Track usage & stats</p>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus on project name field
document.addEventListener('DOMContentLoaded', function() {
    const nameField = document.getElementById('{{ form.name.id }}');
    if (nameField) {
        nameField.focus();
    }
});

// Character counter for project name
document.getElementById('{{ form.name.id }}').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    
    // You can add a character counter here if needed
    if (currentLength > maxLength - 10) {
        this.style.borderColor = '#f59e0b'; // Warning color
    } else {
        this.style.borderColor = ''; // Reset to default
    }
});
</script>
{% endblock %}
