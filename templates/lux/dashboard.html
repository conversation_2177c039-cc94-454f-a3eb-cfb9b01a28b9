{% extends "base.html" %}
{% block title %}Lux Premium - LXND{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500 mb-2">
            <i class="fas fa-crown mr-3"></i>
            Lux Premium
        </h1>
        <p class="text-gray-400">Unlock premium features and enhanced limits</p>
    </div>

    <!-- Current Status -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
        {% if current_user.is_lux_active() %}
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-crown text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white">Lux Status: Active</h3>
                        {% if current_user.lux_expires_at %}
                            <p class="text-gray-400">Expires in {{ current_user.get_lux_days_remaining() }} days</p>
                        {% else %}
                            <p class="text-gray-400">Permanent Lux License</p>
                        {% endif %}
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-green-400 font-medium">
                        <i class="fas fa-check-circle mr-1"></i>
                        Premium Active
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center">
                <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-crown text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white mb-2">Activate Lux Premium</h3>
                <p class="text-gray-400 mb-6">Enter your license key to unlock premium features</p>
                
                <!-- License Activation Form -->
                <form method="POST" action="{{ url_for('activate_lux_license') }}" class="max-w-md mx-auto">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        {{ form.license_key(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white text-center focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent", placeholder="XXXX-XXXX-XXXX-XXXX") }}
                        {% if form.license_key.errors %}
                            <div class="mt-2 text-sm text-red-400">
                                {% for error in form.license_key.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="flex justify-center">
                        {{ form.submit(class="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-medium px-8 py-3 rounded-lg transition-all") }}
                    </div>
                </form>
            </div>
        {% endif %}
    </div>

    <!-- Premium Features -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Enhanced Limits -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-chart-line text-blue-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-white">Enhanced Limits</h3>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-400">Storage:</span>
                    <div class="text-white">
                        <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">1 TB</span>
                        {% if not current_user.is_lux_active() %}
                            <span class="text-xs text-gray-500 ml-1">(vs {{ (current_user.upload_limit_mb / 1024)|round(1) }} GB)</span>
                        {% endif %}
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-400">Email Accounts:</span>
                    <span class="text-white">
                    <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">3</span>
                    {% if not current_user.is_lux_active() %}
                        <span class="text-xs text-gray-500 ml-1">(vs 1)</span>
                    {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Projects:</span>
                    <span class="text-white">
                        <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">50</span>
                        {% if not current_user.is_lux_active() %}
                            <span class="text-xs text-gray-500 ml-1">(vs {{ current_user.max_projects }})</span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Websites:</span>
                    <span class="text-white">
                        <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">5</span>
                        {% if not current_user.is_lux_active() %}
                            <span class="text-xs text-gray-500 ml-1">(vs 1)</span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Website Storage:</span>
                    <span class="text-white">
                        <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">2 GB</span>
                        {% if not current_user.is_lux_active() %}
                            <span class="text-xs text-gray-500 ml-1">(vs 100 MB)</span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Short URLs:</span>
                    <span class="text-white">
                        <span class="{% if current_user.is_lux_active() %}text-yellow-400{% else %}text-gray-500{% endif %}">1,000</span>
                        {% if not current_user.is_lux_active() %}
                            <span class="text-xs text-gray-500 ml-1">(vs 50)</span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Premium Features -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-star text-purple-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-white">Premium Features</h3>
            </div>
            <div class="space-y-3">
                <div class="flex items-center">
                    <i class="fas fa-crown text-green-400 mr-2"></i>
                    <span class="text-gray-300">Discord Lux Rank</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-headset text-green-400 mr-2"></i>
                    <span class="text-gray-300">Priority Support</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-star text-green-400 mr-2"></i>
                    <span class="text-gray-300">Exclusive Features</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Usage -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-chart-bar text-blue-400 mr-2"></i>
                Current Usage
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Storage Usage -->
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-400">Storage</span>
                        <span class="text-white">{{ current_user.get_storage_used_formatted() }}</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        {% set storage_percent = current_user.get_storage_percent() %}
                        <div class="h-2 rounded-full {% if current_user.is_lux_active() %}bg-yellow-500{% else %}bg-blue-500{% endif %}" 
                             style="width: {{ storage_percent }}%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        {% if current_user.is_lux_active() %}
                            {{ storage_percent }}% of 10 GB
                        {% else %}
                            {{ storage_percent }}% of {{ current_user.upload_limit_mb }} MB
                        {% endif %}
                    </div>
                </div>

                <!-- Projects Usage -->
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-400">Projects</span>
                        <span class="text-white">{{ current_user.projects|length }}</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        {% set project_limit = current_user.get_max_projects_limit() %}
                        {% if project_limit > 0 %}
                            {% set project_percent = (current_user.projects|length / project_limit * 100) %}
                            <div class="h-2 rounded-full {% if current_user.is_lux_active() %}bg-yellow-500{% else %}bg-green-500{% endif %}" 
                                 style="width: {{ project_percent }}%"></div>
                        {% else %}
                            <div class="h-2 rounded-full bg-green-500" style="width: 10%"></div>
                        {% endif %}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        {% if project_limit > 0 %}
                            {{ current_user.projects|length }} of {{ project_limit }}
                        {% else %}
                            Unlimited
                        {% endif %}
                    </div>
                </div>

                <!-- Short URLs Usage -->
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-400">Short URLs</span>
                        <span class="text-white">
                            {{ ShortenedUrl.query.filter_by(user_id=current_user.id).count() if ShortenedUrl else 0 }}
                        </span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        {% set url_limit = current_user.get_shortener_limit() %}
                        {% set url_count = ShortenedUrl.query.filter_by(user_id=current_user.id).count() if ShortenedUrl else 0 %}
                        {% if url_limit > 0 %}
                            {% set url_percent = (url_count / url_limit * 100) %}
                            <div class="h-2 rounded-full {% if current_user.is_lux_active() %}bg-yellow-500{% else %}bg-purple-500{% endif %}" 
                                 style="width: {{ url_percent }}%"></div>
                        {% else %}
                            <div class="h-2 rounded-full bg-purple-500" style="width: 10%"></div>
                        {% endif %}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        {% if url_limit > 0 %}
                            {{ url_count }} of {{ url_limit }}
                        {% else %}
                            Unlimited
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Get Lux Section -->
    {% if not current_user.is_lux_active() %}
    <div class="mt-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-center">
        <h3 class="text-2xl font-bold text-white mb-2">Ready to Go Lux?</h3>
        <p class="text-yellow-100 mb-4">Contact us to get your premium license key</p>
        <a href="https://discord.gg/PTwFDCmS" class="bg-white text-orange-500 font-medium px-6 py-3 rounded-lg hover:bg-gray-100 transition-colors">
            Join Disord & Get Your Key!
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
