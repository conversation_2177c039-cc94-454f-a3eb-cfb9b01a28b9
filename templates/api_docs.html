{% extends "base.html" %}
{% block title %}API Documentation - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- API Documentation Header -->
    <div class="bg-gradient-to-br from-violet-600 via-purple-600 to-indigo-600 rounded-2xl p-8 shadow-2xl border border-violet-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-code text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            API Documentation
                        </h1>
                        <p class="text-violet-100 text-lg">Complete guide to LXND API endpoints</p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                    <div class="text-white text-sm opacity-75 uppercase tracking-wide mb-1">
                        API Version
                    </div>
                    <div class="text-white text-2xl font-bold">
                        v1.0
                    </div>
                    <div class="text-violet-200 text-sm">
                        REST API
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Authentication -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-blue-500/20 border border-blue-400/30">
                        <i class="fas fa-shield-alt text-blue-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Authentication</p>
                        <p class="text-lg font-bold text-white">API Key</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rate Limiting -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-green-500/20 hover:border-green-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-green-500/20 border border-green-400/30">
                        <i class="fas fa-tachometer-alt text-green-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Rate Limit</p>
                        <p class="text-lg font-bold text-white">1000/hour</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Format -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-purple-500/20 border border-purple-400/30">
                        <i class="fas fa-file-code text-purple-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Format</p>
                        <p class="text-lg font-bold text-white">JSON</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- HTTPS Only -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-orange-500/20 hover:border-orange-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-orange-500/20 border border-orange-400/30">
                        <i class="fas fa-lock text-orange-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Security</p>
                        <p class="text-lg font-bold text-white">HTTPS Only</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Navigation Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl sticky top-8">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-list text-violet-400 mr-2"></i>
                        API Endpoints
                    </h3>
                </div>
                <div class="p-6">
                    <nav class="space-y-2">
                        <a href="#authentication" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-key mr-2"></i>Authentication
                        </a>
                        <a href="#license-validation" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-check-circle mr-2"></i>License Validation
                        </a>
                        <a href="#license-management" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-cogs mr-2"></i>License Management
                        </a>
                        <a href="#user-management" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-users mr-2"></i>User Management
                        </a>
                        <a href="#file-operations" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-file mr-2"></i>File Operations
                        </a>
                        <a href="#error-codes" class="nav-link block text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Error Codes
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- API Documentation Content -->
        <div class="lg:col-span-3">
            <div class="space-y-8">
                <!-- Authentication Section -->
                <div id="authentication" class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                    <div class="px-8 py-6 border-b border-gray-700/50">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-key text-blue-400 mr-3"></i>
                            Authentication
                        </h3>
                    </div>
                    <div class="p-8">
                        <p class="text-gray-300 mb-6">All API requests require authentication using an API key. Include your API key in the request headers.</p>
                        
                        <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                            <h4 class="text-lg font-bold text-white mb-4">Request Headers</h4>
                            <pre class="text-green-400 font-mono text-sm overflow-x-auto"><code>Authorization: Bearer YOUR_API_KEY
Content-Type: application/json</code></pre>
                        </div>

                        <div class="mt-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-400 mt-1 mr-3"></i>
                                <div>
                                    <h5 class="text-blue-300 font-medium">Getting Your API Key</h5>
                                    <p class="text-gray-300 text-sm mt-1">You can generate your API key from your dashboard under Account Settings.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- License Validation Section -->
                <div id="license-validation" class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                    <div class="px-8 py-6 border-b border-gray-700/50">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            License Validation
                        </h3>
                    </div>
                    <div class="p-8">
                        <p class="text-gray-300 mb-6">Validate license keys for your projects.</p>
                        
                        <div class="space-y-6">
                            <!-- Validate License Endpoint -->
                            <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                                <div class="flex items-center mb-4">
                                    <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium mr-3">POST</span>
                                    <code class="text-green-400 font-mono">/api/validate-license</code>
                                </div>
                                
                                <h5 class="text-white font-medium mb-3">Request Body:</h5>
                                <pre class="text-green-400 font-mono text-sm overflow-x-auto mb-4"><code>{
  "license_key": "your-license-key",
  "project_id": "your-project-id"
}</code></pre>

                                <h5 class="text-white font-medium mb-3">Response:</h5>
                                <pre class="text-green-400 font-mono text-sm overflow-x-auto"><code>{
  "valid": true,
  "license_key": "your-license-key",
  "project_id": "your-project-id",
  "expires_at": "2024-12-31T23:59:59Z",
  "used_by": "username"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- License Management Section -->
                <div id="license-management" class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                    <div class="px-8 py-6 border-b border-gray-700/50">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-cogs text-purple-400 mr-3"></i>
                            License Management
                        </h3>
                    </div>
                    <div class="p-8">
                        <p class="text-gray-300 mb-6">Create, manage, and revoke license keys.</p>
                        
                        <div class="space-y-6">
                            <!-- Create License -->
                            <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                                <div class="flex items-center mb-4">
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm font-medium mr-3">POST</span>
                                    <code class="text-green-400 font-mono">/api/create-license</code>
                                </div>
                                
                                <h5 class="text-white font-medium mb-3">Request Body:</h5>
                                <pre class="text-green-400 font-mono text-sm overflow-x-auto"><code>{
  "project_id": "your-project-id",
  "duration_days": 365,
  "max_uses": 1
}</code></pre>
                            </div>

                            <!-- List Licenses -->
                            <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                                <div class="flex items-center mb-4">
                                    <span class="bg-yellow-600 text-white px-3 py-1 rounded-lg text-sm font-medium mr-3">GET</span>
                                    <code class="text-green-400 font-mono">/api/licenses/{project_id}</code>
                                </div>
                                
                                <p class="text-gray-300 text-sm">Get all licenses for a specific project.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Codes Section -->
                <div id="error-codes" class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                    <div class="px-8 py-6 border-b border-gray-700/50">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                            Error Codes
                        </h3>
                    </div>
                    <div class="p-8">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
                                <div>
                                    <span class="text-red-400 font-mono font-bold">400</span>
                                    <span class="text-white ml-3">Bad Request</span>
                                </div>
                                <span class="text-gray-400 text-sm">Invalid request parameters</span>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
                                <div>
                                    <span class="text-red-400 font-mono font-bold">401</span>
                                    <span class="text-white ml-3">Unauthorized</span>
                                </div>
                                <span class="text-gray-400 text-sm">Invalid or missing API key</span>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
                                <div>
                                    <span class="text-red-400 font-mono font-bold">404</span>
                                    <span class="text-white ml-3">Not Found</span>
                                </div>
                                <span class="text-gray-400 text-sm">Resource not found</span>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
                                <div>
                                    <span class="text-red-400 font-mono font-bold">429</span>
                                    <span class="text-white ml-3">Too Many Requests</span>
                                </div>
                                <span class="text-gray-400 text-sm">Rate limit exceeded</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.nav-link {
    position: relative;
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0);
}

.nav-link:hover {
    background-color: rgba(139, 92, 246, 0.15);
    transform: translateX(4px);
    box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.1);
}

.nav-link.active {
    background: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(124, 58, 237) 100%);
    color: white;
    transform: translateX(6px);
}

html {
    scroll-behavior: smooth;
}
</style>

<script>
// Smooth scrolling and active navigation
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active state
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Update active state on scroll
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('[id]');
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.pageYOffset >= sectionTop - 200) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
});
</script>
{% endblock %}
