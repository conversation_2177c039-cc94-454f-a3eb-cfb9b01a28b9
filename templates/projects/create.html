{% extends "base.html" %}
{% block title %}Create Project - LXND{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-8">
        <h1 class="text-2xl font-bold text-white mb-6">
            <i class="fas fa-plus mr-3"></i>Create New Project
        </h1>
        
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="mb-6">
                <label for="{{ form.name.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                    Project Name
                </label>
                {{ form.name(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                {% if form.name.errors %}
                    <div class="mt-1 text-red-400 text-sm">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-6">
                <label for="{{ form.description.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                    Description (Optional)
                </label>
                {{ form.description(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", rows="4") }}
                {% if form.description.errors %}
                    <div class="mt-1 text-red-400 text-sm">
                        {% for error in form.description.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="flex space-x-4">
                {{ form.submit(class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors") }}
                <a href="{{ url_for('projects.dashboard') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
