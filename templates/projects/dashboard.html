{% extends "base.html" %}
{% block title %}Projects - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-project-diagram mr-3"></i>
                    Projects
                </h1>
                <p class="text-blue-100">Manage your projects and license keys</p>
            </div>
            <div class="flex space-x-3">
                {% if stats.can_create_project %}
                    <a href="{{ url_for('projects.create_project') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-plus mr-2"></i>New Project
                    </a>
                {% endif %}
                {% if current_user.is_lux_active() %}
                    <a href="{{ url_for('projects.lux_dashboard') }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-crown mr-2"></i>Lux Licenses
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 bg-blue-600 bg-opacity-20 rounded-lg">
                    <i class="fas fa-project-diagram text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Total Projects</p>
                    <p class="text-2xl font-bold text-white">{{ stats.total_projects }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 bg-green-600 bg-opacity-20 rounded-lg">
                    <i class="fas fa-key text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Total Licenses</p>
                    <p class="text-2xl font-bold text-white">{{ stats.total_licenses }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 bg-yellow-600 bg-opacity-20 rounded-lg">
                    <i class="fas fa-check-circle text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Active Licenses</p>
                    <p class="text-2xl font-bold text-white">{{ stats.active_licenses }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 bg-purple-600 bg-opacity-20 rounded-lg">
                    <i class="fas fa-crown text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Lux Status</p>
                    <p class="text-sm font-medium {{ 'text-yellow-400' if current_user.is_lux_active() else 'text-gray-400' }}">
                        {{ current_user.get_lux_status() }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for project in projects %}
        <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-blue-500 transition-all duration-300">
            <!-- Project Header -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-white">{{ project.name }}</h3>
                    {% if project.is_active %}
                        <span class="px-2 py-1 bg-green-600 text-green-100 text-xs rounded-full">
                            <i class="fas fa-check-circle mr-1"></i>Active
                        </span>
                    {% else %}
                        <span class="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full">
                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                        </span>
                    {% endif %}
                </div>
                <p class="text-gray-400 text-sm mb-3">{{ project.description or 'No description' }}</p>
                <div class="text-gray-500 text-xs">
                    Created {{ project.created_at.strftime('%m/%d/%Y') }}
                </div>
            </div>

            <!-- Project Stats -->
            <div class="p-4 bg-gray-750">
                <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                        <div class="text-lg font-bold text-white">{{ project.licenses|length }}</div>
                        <div class="text-xs text-gray-400">Total Keys</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-green-400">
                            {{ project.licenses|selectattr('is_active')|list|length }}
                        </div>
                        <div class="text-xs text-gray-400">Active Keys</div>
                    </div>
                </div>
            </div>

            <!-- Project Actions -->
            <div class="p-4 bg-gray-750 border-t border-gray-700">
                <div class="flex space-x-2">
                    <a href="{{ url_for('projects.project_detail', project_id=project.id) }}" 
                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                        <i class="fas fa-eye mr-1"></i>View
                    </a>
                    <a href="{{ url_for('projects.edit_project', project_id=project.id) }}" 
                       class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Empty State -->
        {% if not projects %}
        <div class="col-span-full text-center py-12">
            <i class="fas fa-project-diagram text-gray-600 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-400 mb-2">No Projects Yet</h3>
            <p class="text-gray-500 mb-6">Create your first project to get started with license management</p>
            {% if stats.can_create_project %}
                <a href="{{ url_for('projects.create_project') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>Create First Project
                </a>
            {% else %}
                <p class="text-red-400">You have reached the maximum number of projects allowed.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<style>
.bg-gray-750 {
    background-color: #374151;
}
</style>
{% endblock %}
