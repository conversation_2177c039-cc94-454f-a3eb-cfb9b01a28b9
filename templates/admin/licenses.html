{% extends "base.html" %}

{% block title %}License Management - LXND Admin{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">License Management</h1>
                <p class="text-gray-400">Create and manage Lux Premium licenses</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="showBulkModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-layer-group mr-2"></i>Bulk Creation
                </button>
                <button onclick="showGenerateModal()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-plus mr-2"></i>Generate License
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500/20">
                        <i class="fas fa-key text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Total Licenses</p>
                        <p class="text-2xl font-bold text-white">{{ total_licenses or 0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-500/20">
                        <i class="fas fa-check-circle text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Active Licenses</p>
                        <p class="text-2xl font-bold text-white">{{ active_licenses or 0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-500/20">
                        <i class="fas fa-user-check text-yellow-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Used Licenses</p>
                        <p class="text-2xl font-bold text-white">{{ used_licenses or 0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-500/20">
                        <i class="fas fa-clock text-red-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Expired Licenses</p>
                        <p class="text-2xl font-bold text-white">{{ expired_licenses or 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- License List -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
            <div class="px-6 py-4 border-b border-gray-700/50">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-bold text-white">Recent Licenses</h3>
                    <div class="flex space-x-3">
                        <input type="text" placeholder="Search licenses..." 
                               class="px-4 py-2 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        <select class="px-4 py-2 bg-gray-800 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="used">Used</option>
                            <option value="expired">Expired</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">License Key</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Project</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Duration</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Used By</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Created</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="license-table-body">
                            {% for license in licenses %}
                            <tr class="border-b border-gray-800 hover:bg-gray-800/50">
                                <td class="py-3 px-4">
                                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 text-sm">{{ license.license_key[:16] }}...</code>
                                </td>
                                <td class="py-3 px-4 text-white">Lux Premium</td>
                                <td class="py-3 px-4 text-gray-300">
                                    {% if license.duration_days %}
                                        {{ license.duration_days }} days
                                    {% else %}
                                        Permanent
                                    {% endif %}
                                </td>
                                <td class="py-3 px-4">
                                    {% if license.is_active %}
                                        <span class="bg-green-500 text-green-100 px-2 py-1 rounded-full text-xs font-medium">Active</span>
                                    {% else %}
                                        <span class="bg-red-500 text-red-100 px-2 py-1 rounded-full text-xs font-medium">Inactive</span>
                                    {% endif %}
                                </td>
                                <td class="py-3 px-4 text-gray-300">
                                    {% if license.used_by %}
                                        {% set user = license.get_user() %}
                                        {{ user.username if user else 'Unknown' }}
                                    {% else %}
                                        <span class="text-gray-500">Not used</span>
                                    {% endif %}
                                </td>
                                <td class="py-3 px-4 text-gray-300">{{ license.created_at.strftime('%Y-%m-%d') if license.created_at else 'N/A' }}</td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-2">
                                        <button onclick="copyLicense('{{ license.license_key }}')" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button onclick="deleteLicense('{{ license.id }}')" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate License Modal -->
<div id="generateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Generate License</h3>
            <button onclick="hideGenerateModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="generateForm" class="space-y-4">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">License Type</label>
                <input type="text" name="project_name" value="Lux Premium" readonly
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
            </div>
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration</label>
                <select name="duration_type" onchange="toggleDurationInput()" 
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    <option value="permanent">Permanent</option>
                    <option value="custom">Custom Duration</option>
                </select>
            </div>
            
            <div id="duration-input" class="hidden">
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration (Days)</label>
                <input type="number" name="duration_days" min="1" max="3650"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                       placeholder="Enter number of days">
            </div>
            
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="hideGenerateModal()" 
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" 
                        class="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Generate
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Creation Modal -->
<div id="bulkModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Bulk License Creation</h3>
            <button onclick="hideBulkModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="bulkForm" class="space-y-4">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">License Type</label>
                <input type="text" name="project_name" value="Lux Premium" readonly
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
            </div>
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Number of Licenses</label>
                <input type="number" name="count" min="1" max="100" required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                       placeholder="Enter number of licenses">
            </div>
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration</label>
                <select name="duration_type" onchange="toggleBulkDurationInput()" 
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    <option value="permanent">Permanent</option>
                    <option value="custom">Custom Duration</option>
                </select>
            </div>
            
            <div id="bulk-duration-input" class="hidden">
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration (Days)</label>
                <input type="number" name="duration_days" min="1" max="3650"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                       placeholder="Enter number of days">
            </div>
            
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="hideBulkModal()" 
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" 
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Create Bulk
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functions
function showGenerateModal() {
    document.getElementById('generateModal').classList.remove('hidden');
    document.getElementById('generateModal').classList.add('flex');
}

function hideGenerateModal() {
    document.getElementById('generateModal').classList.add('hidden');
    document.getElementById('generateModal').classList.remove('flex');
}

function showBulkModal() {
    document.getElementById('bulkModal').classList.remove('hidden');
    document.getElementById('bulkModal').classList.add('flex');
}

function hideBulkModal() {
    document.getElementById('bulkModal').classList.add('hidden');
    document.getElementById('bulkModal').classList.remove('flex');
}

function toggleDurationInput() {
    const select = document.querySelector('select[name="duration_type"]');
    const input = document.getElementById('duration-input');
    
    if (select.value === 'custom') {
        input.classList.remove('hidden');
    } else {
        input.classList.add('hidden');
    }
}

function toggleBulkDurationInput() {
    const select = document.querySelector('#bulkForm select[name="duration_type"]');
    const input = document.getElementById('bulk-duration-input');
    
    if (select.value === 'custom') {
        input.classList.remove('hidden');
    } else {
        input.classList.add('hidden');
    }
}

// Form submissions
document.getElementById('generateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/licenses/generate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('License generated successfully!');
            hideGenerateModal();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating license');
    });
});

document.getElementById('bulkForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/licenses/bulk', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`${data.count} licenses created successfully!`);
            hideBulkModal();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating bulk licenses');
    });
});

function copyLicense(licenseKey) {
    navigator.clipboard.writeText(licenseKey).then(() => {
        // Show feedback
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.className = button.className.replace('text-blue-400', 'text-green-400');
        
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.className = button.className.replace('text-green-400', 'text-blue-400');
        }, 2000);
    });
}

function deleteLicense(licenseId) {
    if (confirm('Are you sure you want to delete this license? This action cannot be undone.')) {
        fetch(`/admin/licenses/${licenseId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('License deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting license');
        });
    }
}
</script>
{% endblock %}
