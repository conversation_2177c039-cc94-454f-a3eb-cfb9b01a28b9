{% extends "base.html" %}
{% block title %}Preview Email Template - {{ template.name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-eye mr-3"></i>
                    Email Template Preview
                </h1>
                <p class="text-blue-100">{{ template.name }} - {{ template.template_type.title() }} Template</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('admin_edit_email_template', template_id=template.id) }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                    <i class="fas fa-edit mr-2"></i>Edit Template
                </a>
                <a href="{{ url_for('admin_email_templates') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Templates
                </a>
            </div>
        </div>
    </div>

    <!-- Template Info -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-1">Template Name</label>
                <p class="text-white">{{ template.name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-1">Type</label>
                <span class="px-2 py-1 bg-indigo-600 bg-opacity-30 text-indigo-300 text-sm rounded">
                    {{ template.template_type.title() }}
                </span>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-1">Status</label>
                {% if template.is_active %}
                    <span class="px-2 py-1 bg-green-600 text-green-100 text-sm rounded">
                        <i class="fas fa-check-circle mr-1"></i>Active
                    </span>
                {% else %}
                    <span class="px-2 py-1 bg-gray-600 text-gray-300 text-sm rounded">
                        <i class="fas fa-pause-circle mr-1"></i>Inactive
                    </span>
                {% endif %}
            </div>
        </div>
        
        {% if template.description %}
        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-400 mb-1">Description</label>
            <p class="text-gray-300">{{ template.description }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Sample Variables -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
            <i class="fas fa-code mr-2"></i>Sample Variables Used
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for key, value in sample_vars.items() %}
            <div class="bg-gray-700 rounded p-3">
                <div class="text-sm text-gray-400">{{ '{' + key + '}' }}</div>
                <div class="text-white">{{ value }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Email Preview -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div class="p-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white">
                <i class="fas fa-envelope mr-2"></i>Email Preview
            </h3>
        </div>
        
        <div class="p-6">
            <!-- Email Container -->
            <div class="bg-white rounded-lg shadow-2xl overflow-hidden max-w-2xl mx-auto">
                <!-- Email Client Header -->
                <div class="bg-gray-100 px-6 py-4 border-b">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-envelope text-white text-sm"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-800">LXND System</div>
                                <div class="text-sm text-gray-600"><EMAIL></div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ sample_vars.current_date }}
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-600 mb-2">
                        <strong>To:</strong> {{ sample_vars.user_name }} &lt;{{ sample_vars.user_email }}&gt;
                    </div>
                    
                    <div class="text-xl font-semibold text-gray-800">
                        {{ rendered.subject }}
                    </div>
                </div>
                
                <!-- Email Body -->
                <div class="email-content">
                    {% if rendered.body_html %}
                        {{ rendered.body_html | safe }}
                    {% elif rendered.body_text %}
                        <div class="p-6 whitespace-pre-wrap font-mono text-sm">{{ rendered.body_text }}</div>
                    {% else %}
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                            <p>No content to display</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Plain Text Version -->
    {% if template.body_text %}
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="p-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white">
                <i class="fas fa-file-alt mr-2"></i>Plain Text Version
            </h3>
        </div>
        <div class="p-6">
            <div class="bg-gray-900 rounded p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap">{{ rendered.body_text or template.body_text }}</div>
        </div>
    </div>
    {% endif %}

    <!-- Actions -->
    <div class="flex justify-center space-x-4">
        <a href="{{ url_for('admin_edit_email_template', template_id=template.id) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-colors">
            <i class="fas fa-edit mr-2"></i>Edit Template
        </a>
        <button onclick="sendTestEmail()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
            <i class="fas fa-paper-plane mr-2"></i>Send Test Email
        </button>
        <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors">
            <i class="fas fa-print mr-2"></i>Print Preview
        </button>
    </div>
</div>

<script>
function sendTestEmail() {
    const email = prompt('Enter email address to send test email:');
    if (email) {
        fetch(`/admin/email/templates/{{ template.id }}/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Test email sent successfully!');
            } else {
                alert('Error sending test email: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending test email');
        });
    }
}
</script>

<style>
/* Email content styling */
.email-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.email-content img {
    max-width: 100%;
    height: auto;
}

.email-content table {
    width: 100%;
    border-collapse: collapse;
}

.email-content td, .email-content th {
    padding: 8px;
    text-align: left;
}

/* Print styles */
@media print {
    .bg-gray-800, .bg-gray-700 {
        background: white !important;
        color: black !important;
    }
    
    .text-white {
        color: black !important;
    }
    
    .border-gray-700 {
        border-color: #ccc !important;
    }
}
</style>
{% endblock %}
