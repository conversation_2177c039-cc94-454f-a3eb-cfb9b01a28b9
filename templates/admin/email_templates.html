{% extends "base.html" %}
{% block title %}Email Templates - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-file-alt mr-3"></i>
                    Email Templates
                </h1>
                <p class="text-indigo-100">Manage and customize email templates with HTML editor and live preview</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('admin_create_email_template') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                    <i class="fas fa-plus mr-2"></i>Create Template
                </a>
                <a href="{{ url_for('admin_email') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Email
                </a>
            </div>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for template in templates %}
        <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-indigo-500 transition-all duration-300">
            <!-- Template Header -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-white">{{ template.name }}</h3>
                    {% if template.is_active %}
                        <span class="px-2 py-1 bg-green-600 text-green-100 text-xs rounded-full">
                            <i class="fas fa-check-circle mr-1"></i>Active
                        </span>
                    {% else %}
                        <span class="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full">
                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                        </span>
                    {% endif %}
                </div>
                <p class="text-gray-400 text-sm mb-3">{{ template.description or 'No description' }}</p>
                <div class="flex items-center space-x-2">
                    <span class="px-2 py-1 bg-indigo-600 bg-opacity-30 text-indigo-300 text-xs rounded">
                        {{ template.template_type.title() }}
                    </span>
                    <span class="text-gray-500 text-xs">
                        Updated {{ template.updated_at.strftime('%m/%d/%Y') if template.updated_at else template.created_at.strftime('%m/%d/%Y') }}
                    </span>
                </div>
            </div>

            <!-- Template Preview -->
            <div class="p-4 bg-gray-750">
                <div class="text-xs text-gray-400 mb-2">Subject Preview:</div>
                <div class="text-sm text-white bg-gray-700 p-2 rounded mb-3 truncate">
                    {{ template.subject or 'No subject' }}
                </div>
                <div class="text-xs text-gray-400 mb-2">Content Preview:</div>
                <div class="text-sm text-gray-300 bg-gray-700 p-2 rounded h-20 overflow-hidden">
                    {% if template.body_html %}
                        {{ template.body_html | striptags | truncate(100) }}
                    {% elif template.body_text %}
                        {{ template.body_text | truncate(100) }}
                    {% else %}
                        <span class="text-gray-500">No content</span>
                    {% endif %}
                </div>
            </div>

            <!-- Template Actions -->
            <div class="p-4 bg-gray-750 border-t border-gray-700">
                <div class="flex space-x-2">
                    <a href="{{ url_for('admin_edit_email_template', template_id=template.id) }}" 
                       class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </a>
                    <a href="{{ url_for('admin_preview_email_template', template_id=template.id) }}" 
                       target="_blank"
                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                        <i class="fas fa-eye mr-1"></i>Preview
                    </a>
                    <button onclick="deleteTemplate({{ template.id }}, '{{ template.name }}')" 
                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Empty State -->
        {% if not templates %}
        <div class="col-span-full text-center py-12">
            <i class="fas fa-file-alt text-gray-600 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-400 mb-2">No Email Templates</h3>
            <p class="text-gray-500 mb-6">Create your first email template to get started</p>
            <a href="{{ url_for('admin_create_email_template') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg">
                <i class="fas fa-plus mr-2"></i>Create First Template
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
            <i class="fas fa-bolt mr-2"></i>Quick Actions
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button onclick="createWelcomeTemplate()" class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-left transition-colors">
                <i class="fas fa-user-plus text-2xl mb-2"></i>
                <div class="font-medium">Welcome Template</div>
                <div class="text-sm opacity-75">For new user registration</div>
            </button>
            <button onclick="createNotificationTemplate()" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-left transition-colors">
                <i class="fas fa-bell text-2xl mb-2"></i>
                <div class="font-medium">Notification Template</div>
                <div class="text-sm opacity-75">For system notifications</div>
            </button>
            <button onclick="createCustomTemplate()" class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-left transition-colors">
                <i class="fas fa-magic text-2xl mb-2"></i>
                <div class="font-medium">Custom Template</div>
                <div class="text-sm opacity-75">For custom purposes</div>
            </button>
        </div>
    </div>
</div>

<script>
function deleteTemplate(templateId, templateName) {
    if (confirm(`Are you sure you want to delete the template "${templateName}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/templates/${templateId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting template');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting template');
        });
    }
}

function createWelcomeTemplate() {
    window.location.href = '/admin/email/templates/create?type=welcome';
}

function createNotificationTemplate() {
    window.location.href = '/admin/email/templates/create?type=notification';
}

function createCustomTemplate() {
    window.location.href = '/admin/email/templates/create?type=custom';
}
</script>

<style>
.bg-gray-750 {
    background-color: #374151;
}
</style>
{% endblock %}
