{% extends "base.html" %}

{% block title %}{{ messages['base.html']['site_title'] }} - Edit Email Account{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">Edit Email Account</h1>
                    <p class="mt-2 text-gray-400">Modify email account settings and configuration</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ url_for('admin_view_email_account', account_id=account.id) }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Account
                    </a>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-gray-800 rounded-lg p-6">
            <form method="POST" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Email Address -->
                <div>
                    <label for="{{ form.email_address.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                    </label>
                    {{ form.email_address(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent") }}
                    {% if form.email_address.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.email_address.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Display Name -->
                <div>
                    <label for="{{ form.display_name.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Display Name
                    </label>
                    {{ form.display_name(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent") }}
                    {% if form.display_name.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.display_name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password -->
                <div>
                    <label for="{{ form.password.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        New Password
                        <span class="text-gray-500 text-xs">(leave blank to keep current password)</span>
                    </label>
                    {{ form.password(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent") }}
                    {% if form.password.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.password.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Storage Limit -->
                <div>
                    <label for="{{ form.storage_limit_mb.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Storage Limit (MB)
                    </label>
                    {{ form.storage_limit_mb(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent") }}
                    {% if form.storage_limit_mb.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.storage_limit_mb.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-400">Current usage: {{ account.storage_used_mb }} MB</p>
                </div>

                <!-- Status Checkboxes -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="flex items-center">
                            {{ form.is_active(class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-700") }}
                            <label for="{{ form.is_active.id }}" class="ml-2 block text-sm text-gray-300">
                                Account Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-400">Allow this account to send and receive emails</p>
                    </div>

                    <div>
                        <div class="flex items-center">
                            {{ form.is_verified(class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-700") }}
                            <label for="{{ form.is_verified.id }}" class="ml-2 block text-sm text-gray-300">
                                Account Verified
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-400">Mark this account as verified</p>
                    </div>
                </div>

                <!-- Current Account Info -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-white mb-3">Current Account Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-400">Owner:</span>
                            <span class="text-white ml-2">{{ account.get_user().username if account.get_user() else 'Unknown' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">Created:</span>
                            <span class="text-white ml-2">{{ account.created_at.strftime('%Y-%m-%d %H:%M') if account.created_at else 'Unknown' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">Last Login:</span>
                            <span class="text-white ml-2">{{ account.last_login.strftime('%Y-%m-%d %H:%M') if account.last_login else 'Never' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">Messages:</span>
                            <span class="text-white ml-2">{{ account.messages|length }} total</span>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-between pt-6">
                    <div class="flex space-x-3">
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-save mr-2"></i>Save Changes
                        </button>
                        <a href="{{ url_for('admin_view_email_account', account_id=account.id) }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="button" onclick="resetPassword({{ account.id }})" 
                                class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-key mr-2"></i>Reset Password
                        </button>
                        <button type="button" onclick="deleteAccount({{ account.id }}, '{{ account.email_address }}')" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-trash mr-2"></i>Delete Account
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function resetPassword(accountId) {
    if (confirm('Are you sure you want to reset the password for this email account?')) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/reset-password`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Password reset successfully. New password: ${data.new_password}`);
            } else {
                alert('Error resetting password');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting password');
        });
    }
}

function deleteAccount(accountId, emailAddress) {
    if (confirm(`Are you sure you want to delete email account "${emailAddress}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '/admin/email';
            } else {
                alert('Error deleting email account');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting email account');
        });
    }
}
</script>
{% endblock %}
