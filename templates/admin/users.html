{% extends "base.html" %}
{% block title %}User Management - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-users mr-3"></i>
                    User Management
                </h1>
                <p class="text-blue-100">Manage user accounts, permissions, and settings</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Total Users
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ users|length }}
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-users text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-bold text-white">{{ users|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-600 bg-opacity-20">
                    <i class="fas fa-shield-alt text-red-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Admins</p>
                    <p class="text-2xl font-bold text-white">{{ users|selectattr('is_admin')|list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20">
                    <i class="fas fa-crown text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Lux Users</p>
                    <p class="text-2xl font-bold text-white">{{ users|selectattr('is_lux')|list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-600 bg-opacity-20">
                    <i class="fas fa-ban text-gray-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Banned</p>
                    <p class="text-2xl font-bold text-white">{{ users|selectattr('is_banned')|list|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Actions -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <input type="text" id="user-search" placeholder="Search users..." 
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="flex space-x-4">
                <select id="user-filter" class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">All Users</option>
                    <option value="admin">Admins</option>
                    <option value="lux">Lux Users</option>
                    <option value="banned">Banned</option>
                    <option value="regular">Regular Users</option>
                </select>
                <button onclick="exportUsers()" class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-xl font-bold text-white">User Accounts</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700" id="users-table">
                    {% for user in users %}
                    <tr class="hover:bg-gray-700 transition-colors user-row" 
                        data-username="{{ user.username|lower }}" 
                        data-email="{{ user.email|lower }}"
                        data-role="{% if user.is_admin %}admin{% elif user.is_lux %}lux{% else %}regular{% endif %}"
                        data-status="{% if user.is_banned %}banned{% else %}active{% endif %}">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                    <span class="text-white font-bold">{{ user.username[0].upper() }}</span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">{{ user.username }}</div>
                                    <div class="text-sm text-gray-400">{{ user.email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if user.is_admin %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-shield-alt mr-1"></i>Admin
                                </span>
                            {% elif user.is_lux %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-crown mr-1"></i>Lux
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-user mr-1"></i>User
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if user.is_banned %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-ban mr-1"></i>Banned
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-300">
                            {{ "%.1f"|format(user.get_used_storage_mb()) }} MB
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-300">
                            {{ user.created_at.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4 text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{{ url_for('admin_user_detail', user_id=user.id) }}" class="text-blue-400 hover:text-blue-300" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button onclick="editUser({{ user.id }})" class="text-yellow-400 hover:text-yellow-300" title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if not user.is_admin or users|selectattr('is_admin')|list|length > 1 %}
                                <button onclick="deleteUser({{ user.id }}, '{{ user.username }}')" class="text-red-400 hover:text-red-300" title="Delete User">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('user-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    filterUsers();
});

document.getElementById('user-filter').addEventListener('change', function(e) {
    filterUsers();
});

function filterUsers() {
    const searchTerm = document.getElementById('user-search').value.toLowerCase();
    const filterValue = document.getElementById('user-filter').value;
    const rows = document.querySelectorAll('.user-row');
    
    rows.forEach(row => {
        const username = row.dataset.username;
        const email = row.dataset.email;
        const role = row.dataset.role;
        const status = row.dataset.status;
        
        const matchesSearch = username.includes(searchTerm) || email.includes(searchTerm);
        const matchesFilter = filterValue === 'all' || 
                             (filterValue === 'admin' && role === 'admin') ||
                             (filterValue === 'lux' && role === 'lux') ||
                             (filterValue === 'banned' && status === 'banned') ||
                             (filterValue === 'regular' && role === 'regular');
        
        if (matchesSearch && matchesFilter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function editUser(userId) {
    window.location.href = `/admin/user/${userId}`;
}

function deleteUser(userId, username) {
    if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/user/${userId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting user');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting user');
        });
    }
}

function exportUsers() {
    window.location.href = '/admin/users/export';
}
</script>
{% endblock %}
