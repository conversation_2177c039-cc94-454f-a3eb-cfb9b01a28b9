{% extends "base.html" %}
{% block title %}Admin Dashboard - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Admin Header -->
    <div class="bg-gradient-to-r from-red-600 to-pink-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-shield-alt mr-3"></i>
                    Admin Dashboard
                </h1>
                <p class="text-red-100">System administration and management center</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Total Users
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ users|length }}
                </div>
                <div class="text-red-200 text-sm">
                    Registered accounts
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Navigation -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- User Management -->
        <a href="{{ url_for('admin_users') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-users text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">User Management</h3>
                            <p class="text-gray-400 text-sm">Manage user accounts and permissions</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-blue-400 transition-colors"></i>
                </div>
            </div>
        </a>

        <!-- Project Management -->
        <a href="{{ url_for('admin_projects') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-green-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-project-diagram text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-green-400 transition-colors">Project Management</h3>
                            <p class="text-gray-400 text-sm">Manage projects and licenses</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-green-400 transition-colors"></i>
                </div>
            </div>
        </a>

        <!-- Email Management -->
        <a href="{{ url_for('admin_email') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-purple-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-envelope text-purple-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-purple-400 transition-colors">Email Management</h3>
                            <p class="text-gray-400 text-sm">Manage email accounts and settings</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-purple-400 transition-colors"></i>
                </div>
            </div>
        </a>

        <!-- License Management -->
        <a href="{{ url_for('admin_licenses') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-yellow-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-key text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">License Management</h3>
                            <p class="text-gray-400 text-sm">Create and manage Lux licenses</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-yellow-400 transition-colors"></i>
                </div>
            </div>
        </a>

        <!-- Website Management -->
        <a href="{{ url_for('admin_websites') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-cyan-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-cyan-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-globe text-cyan-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-cyan-400 transition-colors">Website Management</h3>
                            <p class="text-gray-400 text-sm">Manage user websites and hosting</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-cyan-400 transition-colors"></i>
                </div>
            </div>
        </a>

        <!-- System Management -->
        <a href="{{ url_for('admin_system') }}" class="block bg-gray-800 rounded-lg border border-gray-700 hover:border-yellow-500 transition-all duration-300 hover:shadow-lg group">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20 group-hover:bg-opacity-30 transition-all">
                            <i class="fas fa-cogs text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">System Management</h3>
                            <p class="text-gray-400 text-sm">System settings and maintenance</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-yellow-400 transition-colors"></i>
                </div>
            </div>
        </a>
    </div>

    <!-- System Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-blue-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                        <i class="fas fa-users text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Total Users</p>
                        <p class="text-2xl font-bold text-white">{{ users|length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Projects -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-green-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                        <i class="fas fa-project-diagram text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Active Projects</p>
                        <p class="text-2xl font-bold text-white">{{ total_projects }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Accounts -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-purple-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                        <i class="fas fa-envelope text-purple-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Email Accounts</p>
                        <p class="text-2xl font-bold text-white">{{ total_email_accounts }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Used -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-yellow-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20">
                        <i class="fas fa-hdd text-yellow-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Storage Used</p>
                        <p class="text-2xl font-bold text-white">{{ "%.1f"|format(total_storage_mb / 1024) }} GB</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Users -->
        <div class="bg-gray-800 rounded-lg border border-gray-700">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-user-plus text-blue-400 mr-2"></i>
                    Recent Users
                </h3>
            </div>
            <div class="p-6">
                {% if users %}
                    <div class="space-y-4">
                        {% for user in users[:5] %}
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">{{ user.username[0].upper() }}</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-white font-medium">{{ user.username }}</p>
                                    <p class="text-gray-400 text-sm">{{ user.created_at.strftime('%Y-%m-%d') }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% if user.is_admin %}
                                    <span class="px-2 py-1 bg-red-600 text-red-100 text-xs rounded-full">Admin</span>
                                {% elif user.is_lux %}
                                    <span class="px-2 py-1 bg-yellow-600 text-yellow-100 text-xs rounded-full">Lux</span>
                                {% else %}
                                    <span class="px-2 py-1 bg-gray-600 text-gray-100 text-xs rounded-full">User</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-400 text-center py-8">No users found</p>
                {% endif %}
            </div>
        </div>

        <!-- System Information -->
        <div class="bg-gray-800 rounded-lg border border-gray-700">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-info-circle text-green-400 mr-2"></i>
                    System Information
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Admin Users</span>
                        <span class="text-white font-semibold">{{ admin_count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total Projects</span>
                        <span class="text-white font-semibold">{{ total_projects }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Active Licenses</span>
                        <span class="text-white font-semibold">{{ active_keys }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Email Accounts</span>
                        <span class="text-white font-semibold">{{ total_email_accounts }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Storage Used</span>
                        <span class="text-white font-semibold">{{ "%.1f"|format(total_storage_mb / 1024) }} GB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
