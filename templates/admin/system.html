{% extends "base.html" %}
{% block title %}System Management - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-cogs mr-3"></i>
                    System Management
                </h1>
                <p class="text-yellow-100">System settings, monitoring, and maintenance</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    System Status
                </div>
                <div class="text-white text-2xl font-bold">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    Online
                </div>
            </div>
        </div>
    </div>

    <!-- System Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-users text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-bold text-white">{{ system_info.users_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                    <i class="fas fa-project-diagram text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Projects</p>
                    <p class="text-2xl font-bold text-white">{{ system_info.projects_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                    <i class="fas fa-file text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Files</p>
                    <p class="text-2xl font-bold text-white">{{ system_info.files_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20">
                    <i class="fas fa-hdd text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Storage Used</p>
                    <p class="text-2xl font-bold text-white">{{ "%.1f"|format(system_info.storage_used / 1024) }} GB</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="flex border-b border-gray-700">
            <button onclick="showTab('settings')" id="tab-settings" class="tab-button px-6 py-4 text-sm font-medium text-white bg-yellow-600 border-b-2 border-yellow-600">
                <i class="fas fa-cogs mr-2"></i>System Settings
            </button>
            <button onclick="showTab('monitoring')" id="tab-monitoring" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-chart-line mr-2"></i>Monitoring
            </button>
            <button onclick="showTab('maintenance')" id="tab-maintenance" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-tools mr-2"></i>Maintenance
            </button>
            <button onclick="showTab('logs')" id="tab-logs" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-file-alt mr-2"></i>System Logs
            </button>
            <button onclick="showTab('discord')" id="tab-discord" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fab fa-discord mr-2"></i>Discord Bot
            </button>
        </div>

        <!-- System Settings Tab -->
        <div id="content-settings" class="tab-content">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">System Configuration</h3>
                
                <form id="system-settings-form" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Site Name</label>
                            <input type="text" name="site_name" value="LXND" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Site Description</label>
                            <input type="text" name="site_description" value="Cloud Platform" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Default Storage Limit (GB)</label>
                            <input type="number" name="default_storage_limit" value="10" min="1" max="1000"
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Max File Size (GB)</label>
                            <input type="number" name="max_file_size" value="10" min="1" max="100"
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Session Timeout (minutes)</label>
                            <input type="number" name="session_timeout" value="60" min="5" max="1440"
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Backup Retention (days)</label>
                            <input type="number" name="backup_retention" value="7" min="1" max="90"
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="registration_enabled" checked
                                   class="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500">
                            <span class="ml-2 text-gray-300">Allow User Registration</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="email_verification" checked
                                   class="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500">
                            <span class="ml-2 text-gray-300">Require Email Verification</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="maintenance_mode"
                                   class="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500">
                            <span class="ml-2 text-gray-300">Maintenance Mode</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="px-6 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </form>
            </div>
        </div>

        <!-- Monitoring Tab -->
        <div id="content-monitoring" class="tab-content hidden">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">System Monitoring</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- System Resources -->
                    <div class="bg-gray-700 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-white mb-4">System Resources</h4>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-300">CPU Usage</span>
                                    <span class="text-white font-semibold" id="cpu-usage">Loading...</span>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2">
                                    <div id="cpu-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-300">Memory Usage</span>
                                    <span class="text-white font-semibold" id="memory-usage">Loading...</span>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2">
                                    <div id="memory-bar" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-300">Disk Usage</span>
                                    <span class="text-white font-semibold" id="disk-usage">Loading...</span>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2">
                                    <div id="disk-bar" class="bg-yellow-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Service Status -->
                    <div class="bg-gray-700 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-white mb-4">Service Status</h4>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Web Server</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Running
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Database</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Running
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Email Server</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800" id="email-status">
                                    <i class="fas fa-check-circle mr-1"></i>Running
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Discord Bot</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800" id="bot-status">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>Checking...
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance Tab -->
        <div id="content-maintenance" class="tab-content hidden">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">System Maintenance</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div class="bg-gray-700 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-white mb-4">Database Operations</h4>
                            <div class="space-y-3">
                                <button onclick="createBackup()" class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-database mr-2"></i>Create Database Backup
                                </button>
                                <button onclick="optimizeDatabase()" class="w-full px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-cogs mr-2"></i>Optimize Database
                                </button>
                                <button onclick="cleanupDatabase()" class="w-full px-4 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-broom mr-2"></i>Cleanup Old Data
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-white mb-4">File System</h4>
                            <div class="space-y-3">
                                <button onclick="cleanupFiles()" class="w-full px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-trash mr-2"></i>Cleanup Orphaned Files
                                </button>
                                <button onclick="checkDiskSpace()" class="w-full px-4 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-hdd mr-2"></i>Check Disk Space
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-700 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-white mb-4">System Operations</h4>
                            <div class="space-y-3">
                                <button onclick="clearCache()" class="w-full px-4 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-sync mr-2"></i>Clear System Cache
                                </button>
                                <button onclick="restartServices()" class="w-full px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-power-off mr-2"></i>Restart Services
                                </button>
                                <button onclick="updateSystem()" class="w-full px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-download mr-2"></i>Check for Updates
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-white mb-4">Security</h4>
                            <div class="space-y-3">
                                <button onclick="scanSecurity()" class="w-full px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-shield-alt mr-2"></i>Security Scan
                                </button>
                                <button onclick="auditLogs()" class="w-full px-4 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-search mr-2"></i>Audit Logs
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Logs Tab -->
        <div id="content-logs" class="tab-content hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">System Logs</h3>
                    <div class="flex space-x-2">
                        <select id="log-filter" class="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            <option value="all">All Logs</option>
                            <option value="error">Errors</option>
                            <option value="warning">Warnings</option>
                            <option value="info">Info</option>
                        </select>
                        <button onclick="refreshLogs()" class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                    </div>
                </div>
                
                <div class="bg-gray-900 rounded-lg p-4 h-96 overflow-y-auto">
                    <pre id="system-logs" class="text-gray-300 text-sm font-mono">Loading system logs...</pre>
                </div>
            </div>
        </div>

        <!-- Discord Bot Tab -->
        <div id="content-discord" class="tab-content hidden">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">Discord Bot Management</h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Bot Status -->
                    <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                        <div class="px-6 py-4 border-b border-gray-700/50">
                            <h4 class="text-lg font-bold text-white flex items-center">
                                <i class="fas fa-robot text-indigo-400 mr-2"></i>
                                Bot Status
                            </h4>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-300">Status</span>
                                    <span id="bot-status" class="bg-green-500 text-green-100 px-3 py-1 rounded-full text-sm font-medium">
                                        <i class="fas fa-circle mr-1"></i>Online
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-300">Connected Servers</span>
                                    <span class="text-white font-medium" id="bot-servers">-</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-300">Linked Users</span>
                                    <span class="text-white font-medium" id="linked-users">{{ linked_users_count or 0 }}</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-300">Pending Role Updates</span>
                                    <span class="text-white font-medium" id="pending-updates">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bot Actions -->
                    <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                        <div class="px-6 py-4 border-b border-gray-700/50">
                            <h4 class="text-lg font-bold text-white flex items-center">
                                <i class="fas fa-cogs text-blue-400 mr-2"></i>
                                Bot Actions
                            </h4>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="restartBot()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-redo mr-2"></i>Restart Bot
                                </button>
                                <button onclick="syncAllRoles()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-sync mr-2"></i>Sync All Roles
                                </button>
                                <button onclick="showBotLogs()" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-file-alt mr-2"></i>View Bot Logs
                                </button>
                                <button onclick="testBotConnection()" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-wifi mr-2"></i>Test Connection
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Discord Configuration -->
                <div class="mt-6 bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                    <div class="px-6 py-4 border-b border-gray-700/50">
                        <h4 class="text-lg font-bold text-white flex items-center">
                            <i class="fab fa-discord text-indigo-400 mr-2"></i>
                            Discord Configuration
                        </h4>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-gray-300 text-sm font-medium mb-2">Bot Token</label>
                                <div class="relative">
                                    <input type="password" id="bot-token" value="MTM5NTc0NDk4MDY2ODI1MjIyMA.GtP41j.fn92FIjkkQGAKmIvldiVBOw84fuQAkYTAeOmcM"
                                           class="w-full bg-gray-800 border border-gray-600 rounded-xl px-4 py-3 text-white pr-12" readonly>
                                    <button onclick="toggleTokenVisibility()" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white">
                                        <i class="fas fa-eye" id="token-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-gray-300 text-sm font-medium mb-2">Client ID</label>
                                <input type="text" value="1395744980668252220"
                                       class="w-full bg-gray-800 border border-gray-600 rounded-xl px-4 py-3 text-white" readonly>
                            </div>
                            <div>
                                <label class="block text-gray-300 text-sm font-medium mb-2">OAuth2 Redirect URI</label>
                                <input type="text" value="https://lxnd.cloud/auth/discord/callback"
                                       class="w-full bg-gray-800 border border-gray-600 rounded-xl px-4 py-3 text-white" readonly>
                            </div>
                            <div>
                                <label class="block text-gray-300 text-sm font-medium mb-2">Bot Invite Link</label>
                                <div class="flex">
                                    <input type="text" id="invite-link" value="https://discord.com/api/oauth2/authorize?client_id=1395744980668252220&permissions=8&scope=bot%20applications.commands"
                                           class="flex-1 bg-gray-800 border border-gray-600 rounded-l-xl px-4 py-3 text-white" readonly>
                                    <button onclick="copyInviteLink()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-r-xl transition-all duration-300">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('bg-yellow-600', 'border-yellow-600', 'text-white');
        button.classList.add('text-gray-300');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Add active class to selected tab button
    const activeButton = document.getElementById(`tab-${tabName}`);
    activeButton.classList.add('bg-yellow-600', 'border-yellow-600', 'text-white');
    activeButton.classList.remove('text-gray-300');
    
    // Load tab-specific data
    if (tabName === 'monitoring') {
        loadSystemMonitoring();
    } else if (tabName === 'logs') {
        loadSystemLogs();
    } else if (tabName === 'discord') {
        loadDiscordStatus();
    }
}

function loadSystemMonitoring() {
    fetch('/admin/system/monitoring')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update CPU usage
                document.getElementById('cpu-usage').textContent = data.cpu_usage;
                document.getElementById('cpu-bar').style.width = data.cpu_usage;
                
                // Update Memory usage
                document.getElementById('memory-usage').textContent = data.memory_usage;
                document.getElementById('memory-bar').style.width = data.memory_usage;
                
                // Update Disk usage
                document.getElementById('disk-usage').textContent = data.disk_usage;
                document.getElementById('disk-bar').style.width = data.disk_usage;
            }
        })
        .catch(error => {
            console.error('Error loading monitoring data:', error);
        });
}

function loadSystemLogs() {
    const filter = document.getElementById('log-filter').value;
    
    fetch(`/admin/system/logs?filter=${filter}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('system-logs').textContent = data.logs;
            }
        })
        .catch(error => {
            console.error('Error loading logs:', error);
            document.getElementById('system-logs').textContent = 'Error loading logs';
        });
}

function refreshLogs() {
    loadSystemLogs();
}

// Maintenance functions
function createBackup() {
    if (confirm('Create a database backup? This may take a few minutes.')) {
        performMaintenanceAction('backup');
    }
}

function optimizeDatabase() {
    if (confirm('Optimize the database? This may take a few minutes.')) {
        performMaintenanceAction('optimize');
    }
}

function cleanupDatabase() {
    if (confirm('Cleanup old database records? This action cannot be undone.')) {
        performMaintenanceAction('cleanup-db');
    }
}

function cleanupFiles() {
    if (confirm('Cleanup orphaned files? This action cannot be undone.')) {
        performMaintenanceAction('cleanup-files');
    }
}

function checkDiskSpace() {
    performMaintenanceAction('disk-space');
}

function clearCache() {
    if (confirm('Clear system cache?')) {
        performMaintenanceAction('clear-cache');
    }
}

function restartServices() {
    if (confirm('Restart system services? This may cause brief downtime.')) {
        performMaintenanceAction('restart-services');
    }
}

function updateSystem() {
    performMaintenanceAction('check-updates');
}

function scanSecurity() {
    performMaintenanceAction('security-scan');
}

function auditLogs() {
    performMaintenanceAction('audit-logs');
}

function performMaintenanceAction(action) {
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token() }}');
    formData.append('action', action);
    
    fetch('/admin/system/maintenance', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error performing maintenance action');
    });
}

// System settings form submission
document.getElementById('system-settings-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/system/settings', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('System settings saved successfully');
        } else {
            alert('Error saving settings: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving settings');
    });
});

// Load monitoring data every 30 seconds
setInterval(() => {
    if (!document.getElementById('content-monitoring').classList.contains('hidden')) {
        loadSystemMonitoring();
    }
}, 30000);

// Discord Bot Functions
function loadDiscordStatus() {
    fetch('/admin/discord/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('bot-servers').textContent = data.servers || '-';
                document.getElementById('linked-users').textContent = data.linked_users || '0';
                document.getElementById('pending-updates').textContent = data.pending_updates || '0';

                const statusElement = document.getElementById('bot-status');
                if (data.online) {
                    statusElement.className = 'bg-green-500 text-green-100 px-3 py-1 rounded-full text-sm font-medium';
                    statusElement.innerHTML = '<i class="fas fa-circle mr-1"></i>Online';
                } else {
                    statusElement.className = 'bg-red-500 text-red-100 px-3 py-1 rounded-full text-sm font-medium';
                    statusElement.innerHTML = '<i class="fas fa-circle mr-1"></i>Offline';
                }
            }
        })
        .catch(error => {
            console.error('Error loading Discord status:', error);
        });
}

function restartBot() {
    if (confirm('Restart the Discord bot? This will temporarily disconnect the bot.')) {
        fetch('/admin/discord/restart', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Bot restart initiated');
                    setTimeout(loadDiscordStatus, 5000); // Reload status after 5 seconds
                } else {
                    alert('Error restarting bot: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error restarting bot');
            });
    }
}

function syncAllRoles() {
    if (confirm('Sync all Discord roles? This will update roles for all linked users.')) {
        fetch('/admin/discord/sync-roles', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Role sync initiated for ${data.users_updated} users`);
                    loadDiscordStatus();
                } else {
                    alert('Error syncing roles: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error syncing roles');
            });
    }
}

function showBotLogs() {
    fetch('/admin/discord/logs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const logWindow = window.open('', 'BotLogs', 'width=800,height=600');
                logWindow.document.write(`
                    <html>
                        <head><title>Discord Bot Logs</title></head>
                        <body style="font-family: monospace; background: #1a1a1a; color: #fff; padding: 20px;">
                            <h2>Discord Bot Logs</h2>
                            <pre>${data.logs}</pre>
                        </body>
                    </html>
                `);
            } else {
                alert('Error loading bot logs: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading bot logs');
        });
}

function testBotConnection() {
    fetch('/admin/discord/test', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Bot connection test successful!');
            } else {
                alert('Bot connection test failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error testing bot connection');
        });
}

function toggleTokenVisibility() {
    const tokenInput = document.getElementById('bot-token');
    const eyeIcon = document.getElementById('token-eye');

    if (tokenInput.type === 'password') {
        tokenInput.type = 'text';
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        tokenInput.type = 'password';
        eyeIcon.className = 'fas fa-eye';
    }
}

function copyInviteLink() {
    const inviteLink = document.getElementById('invite-link');
    inviteLink.select();
    document.execCommand('copy');

    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.className = button.className.replace('bg-blue-600 hover:bg-blue-700', 'bg-green-600');

    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.className = button.className.replace('bg-green-600', 'bg-blue-600 hover:bg-blue-700');
    }, 2000);
}
</script>
{% endblock %}
