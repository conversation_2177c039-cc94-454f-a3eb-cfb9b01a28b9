{% extends "base.html" %}
{% block title %}Project Management - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-project-diagram mr-3"></i>
                    Project Management
                </h1>
                <p class="text-green-100">Manage projects, licenses, and user access</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Total Projects
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ projects|length }}
                </div>
            </div>
        </div>
    </div>

    <!-- Project Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                    <i class="fas fa-project-diagram text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Projects</p>
                    <p class="text-2xl font-bold text-white">{{ projects|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-key text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Licenses</p>
                    <p class="text-2xl font-bold text-white">{{ licenses|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20">
                    <i class="fas fa-check-circle text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Licenses</p>
                    <p class="text-2xl font-bold text-white">{{ licenses|selectattr('is_active')|list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                    <i class="fas fa-users text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Used Licenses</p>
                    <p class="text-2xl font-bold text-white">{{ licenses|selectattr('used_by')|list|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="flex border-b border-gray-700">
            <button onclick="showTab('projects')" id="tab-projects" class="tab-button px-6 py-4 text-sm font-medium text-white bg-green-600 border-b-2 border-green-600">
                <i class="fas fa-project-diagram mr-2"></i>Projects
            </button>
            <button onclick="showTab('licenses')" id="tab-licenses" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-key mr-2"></i>Licenses
            </button>
        </div>

        <!-- Projects Tab -->
        <div id="content-projects" class="tab-content">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">Projects</h3>
                    <button onclick="createProject()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>New Project
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Project</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Owner</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Licenses</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for project in projects %}
                            <tr class="hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center">
                                            <i class="fas fa-project-diagram text-white"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-white">{{ project.name }}</div>
                                            <div class="text-sm text-gray-400">{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ project.owner.username }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ project.created_at.strftime('%Y-%m-%d') }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ project.licenses|length }}
                                </td>
                                <td class="px-6 py-4 text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewProject({{ project.id }})" class="text-blue-400 hover:text-blue-300" title="View Project">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editProject({{ project.id }})" class="text-yellow-400 hover:text-yellow-300" title="Edit Project">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteProject({{ project.id }}, '{{ project.name }}')" class="text-red-400 hover:text-red-300" title="Delete Project">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Licenses Tab -->
        <div id="content-licenses" class="tab-content hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">Licenses</h3>
                    <button onclick="createLicense()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>New License
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">License Key</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Project</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Used By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Duration</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for license in licenses %}
                            <tr class="hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-mono text-white">{{ license.license_key[:20] }}...</div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ license.project.name if license.project else 'N/A' }}
                                </td>
                                <td class="px-6 py-4">
                                    {% if license.is_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    <span class="text-gray-500">-</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {% if license.duration_days %}
                                        {{ license.duration_days }} days
                                    {% else %}
                                        <span class="text-yellow-400">Permanent</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewLicense('{{ license.license_key }}')" class="text-blue-400 hover:text-blue-300" title="View License">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editLicense({{ license.id }})" class="text-yellow-400 hover:text-yellow-300" title="Edit License">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteLicense({{ license.id }}, '{{ license.license_key[:10] }}...')" class="text-red-400 hover:text-red-300" title="Delete License">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('bg-green-600', 'border-green-600', 'text-white');
        button.classList.add('text-gray-300');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Add active class to selected tab button
    const activeButton = document.getElementById(`tab-${tabName}`);
    activeButton.classList.add('bg-green-600', 'border-green-600', 'text-white');
    activeButton.classList.remove('text-gray-300');
}

function createProject() {
    window.location.href = '/admin/project/new';
}

function viewProject(projectId) {
    window.location.href = `/admin/project/${projectId}`;
}

function editProject(projectId) {
    window.location.href = `/admin/project/${projectId}/edit`;
}

function deleteProject(projectId, projectName) {
    if (confirm(`Are you sure you want to delete project "${projectName}"? This will also delete all associated licenses.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/project/${projectId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting project');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting project');
        });
    }
}

function createLicense() {
    window.location.href = '/admin/license/new';
}

function viewLicense(licenseKey) {
    window.location.href = `/admin/license/${licenseKey}`;
}

function editLicense(licenseId) {
    window.location.href = `/admin/license/${licenseId}/edit`;
}

function deleteLicense(licenseId, licenseKey) {
    if (confirm(`Are you sure you want to delete license "${licenseKey}"?`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/license/${licenseId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting license');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting license');
        });
    }
}
</script>
{% endblock %}
