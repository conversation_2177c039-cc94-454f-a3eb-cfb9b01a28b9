{% extends "base.html" %}

{% block title %}Admin - Website Management - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Admin Header -->
    <div class="bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-globe mr-3"></i>
                    Website Management
                </h1>
                <p class="text-cyan-100">Manage all user websites and hosting</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Total Websites
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ stats.total }}
                </div>
                <div class="text-cyan-200 text-sm">
                    Active: {{ stats.active }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-cyan-600 bg-opacity-20">
                    <i class="fas fa-globe text-cyan-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Websites</p>
                    <p class="text-2xl font-bold text-white">{{ stats.total }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Websites</p>
                    <p class="text-2xl font-bold text-white">{{ stats.active }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                    <i class="fas fa-shield-alt text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Wildcard SSL</p>
                    <p class="text-2xl font-bold text-white">{{ stats.total }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-server text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">SFTP Enabled</p>
                    <p class="text-2xl font-bold text-white">{{ stats.sftp_enabled }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
        <form method="GET" class="flex space-x-4">
            <div class="flex-1">
                <input type="text" name="search" value="{{ search }}" 
                       placeholder="Search by subdomain, title, or username..."
                       class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500">
            </div>
            <button type="submit" 
                    class="bg-cyan-600 hover:bg-cyan-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-search mr-2"></i>
                Search
            </button>
            {% if search %}
            <a href="{{ url_for('admin_websites') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-times mr-2"></i>
                Clear
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Websites Table -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-xl font-bold text-white">Websites</h3>
        </div>
        
        {% if websites.items %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-900">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Website</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Owner</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Storage</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Features</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    {% for website in websites.items %}
                    <tr class="hover:bg-gray-700 transition-colors">
                        <td class="px-6 py-4">
                            <div>
                                <div class="text-white font-medium">{{ website.title }}</div>
                                <div class="text-sm text-gray-400">
                                    <a href="{{ website.get_url() }}" target="_blank" class="text-cyan-400 hover:text-cyan-300">
                                        {{ website.subdomain }}.lxnd.cloud
                                        <i class="fas fa-external-link-alt ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-white">{{ website.user.username }}</div>
                            <div class="text-sm text-gray-400">
                                {% if website.user.is_admin %}
                                    <span class="text-red-400">Admin</span>
                                {% elif website.user.is_lux_active() %}
                                    <span class="text-yellow-400">Lux</span>
                                {% else %}
                                    <span class="text-gray-400">Regular</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-white">{{ website.get_storage_used_formatted() }}</div>
                            <div class="text-sm text-gray-400">/ {{ (website.get_storage_limit_effective() / 1024)|round(1) }}GB</div>
                            <div class="w-full bg-gray-600 rounded-full h-1 mt-1">
                                <div class="h-1 rounded-full {% if website.get_storage_percent() >= 90 %}bg-red-500{% elif website.get_storage_percent() >= 70 %}bg-yellow-500{% else %}bg-cyan-500{% endif %}"
                                     style="width: {{ website.get_storage_percent() }}%"></div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                                    <i class="fas fa-shield-alt mr-1"></i>Wildcard SSL
                                </span>
                                {% if website.sftp_enabled %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                                        <i class="fas fa-server mr-1"></i>SFTP
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if website.is_active %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-400">
                                    <i class="fas fa-times-circle mr-1"></i>Inactive
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-300">
                            {{ website.created_at.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <form method="POST" action="{{ url_for('admin_toggle_website_status', website_id=website.id) }}" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" 
                                            class="text-yellow-400 hover:text-yellow-300 p-1 rounded transition-colors"
                                            title="{% if website.is_active %}Deactivate{% else %}Activate{% endif %}">
                                        <i class="fas fa-{% if website.is_active %}pause{% else %}play{% endif %}"></i>
                                    </button>
                                </form>
                                <a href="{{ url_for('website_detail', subdomain=website.subdomain) }}" 
                                   class="text-cyan-400 hover:text-cyan-300 p-1 rounded transition-colors"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form method="POST" action="{{ url_for('admin_delete_website', website_id=website.id) }}" 
                                      style="display: inline;" 
                                      onsubmit="return confirm('Are you sure you want to delete this website? This action cannot be undone.')">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" 
                                            class="text-red-400 hover:text-red-300 p-1 rounded transition-colors"
                                            title="Delete Website">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if websites.pages > 1 %}
        <div class="px-6 py-4 border-t border-gray-700">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-400">
                    Showing {{ websites.per_page * (websites.page - 1) + 1 }} to 
                    {{ websites.per_page * (websites.page - 1) + websites.items|length }} of 
                    {{ websites.total }} websites
                </div>
                <div class="flex space-x-2">
                    {% if websites.has_prev %}
                        <a href="{{ url_for('admin_websites', page=websites.prev_num, search=search) }}" 
                           class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded transition-colors">
                            Previous
                        </a>
                    {% endif %}
                    {% if websites.has_next %}
                        <a href="{{ url_for('admin_websites', page=websites.next_num, search=search) }}" 
                           class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded transition-colors">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-16">
            <i class="fas fa-globe text-gray-500 text-4xl mb-4"></i>
            <h3 class="text-xl font-bold text-gray-300 mb-2">No Websites Found</h3>
            <p class="text-gray-400">
                {% if search %}
                    No websites match your search criteria.
                {% else %}
                    No websites have been created yet.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
