{% extends "base.html" %}
{% block title %}Email Management - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-envelope mr-3"></i>
                    Email Management
                </h1>
                <p class="text-purple-100">Manage email accounts, settings, and server configuration</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Email Accounts
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ email_accounts|length }}
                </div>
            </div>
        </div>
    </div>

    <!-- Email Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                    <i class="fas fa-envelope text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Accounts</p>
                    <p class="text-2xl font-bold text-white">{{ email_accounts|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Accounts</p>
                    <p class="text-2xl font-bold text-white">{{ email_accounts|selectattr('is_active')|list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-server text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Server Status</p>
                    <p class="text-2xl font-bold text-green-400">Online</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-600 bg-opacity-20">
                    <i class="fas fa-envelope-open text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Storage Used</p>
                    <p class="text-2xl font-bold text-white">2.1 GB</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="flex border-b border-gray-700">
            <button onclick="showTab('accounts')" id="tab-accounts" class="tab-button px-6 py-4 text-sm font-medium text-white bg-purple-600 border-b-2 border-purple-600">
                <i class="fas fa-users mr-2"></i>Email Accounts
            </button>
            <button onclick="showTab('settings')" id="tab-settings" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-cogs mr-2"></i>Server Settings
            </button>
            <button onclick="showTab('templates')" id="tab-templates" class="tab-button px-6 py-4 text-sm font-medium text-gray-300 hover:text-white">
                <i class="fas fa-file-alt mr-2"></i>Email Templates
            </button>
        </div>

        <!-- Email Accounts Tab -->
        <div id="content-accounts" class="tab-content">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">Email Accounts</h3>
                    <button onclick="createEmailAccount()" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>New Account
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email Address</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Owner</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for account in email_accounts %}
                            <tr class="hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center">
                                            <i class="fas fa-envelope text-white"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-white">{{ account.email_address }}</div>
                                            <div class="text-sm text-gray-400">{{ account.display_name or 'No display name' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {% if account.user %}
                                        {{ account.user.username }}
                                    {% else %}
                                        <span class="text-gray-500">Unknown User</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    {% if account.is_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ account.storage_used_mb or 0 }} MB
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-300">
                                    {{ account.created_at.strftime('%Y-%m-%d') }}
                                </td>
                                <td class="px-6 py-4 text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewEmailAccount({{ account.id }})" class="text-blue-400 hover:text-blue-300" title="View Account">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editEmailAccount({{ account.id }})" class="text-yellow-400 hover:text-yellow-300" title="Edit Account">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="resetPassword({{ account.id }})" class="text-purple-400 hover:text-purple-300" title="Reset Password">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button onclick="deleteEmailAccount({{ account.id }}, '{{ account.email_address }}')" class="text-red-400 hover:text-red-300" title="Delete Account">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Server Settings Tab -->
        <div id="content-settings" class="tab-content hidden">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">Email Server Settings</h3>
                
                <form id="email-settings-form" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">SMTP Server</label>
                            <input type="text" name="smtp_server" value="smtp.lxnd.cloud" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">SMTP Port</label>
                            <input type="number" name="smtp_port" value="587" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">IMAP Server</label>
                            <input type="text" name="imap_server" value="imap.lxnd.cloud" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">IMAP Port</label>
                            <input type="number" name="imap_port" value="993" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Domain</label>
                            <input type="text" name="domain" value="lxnd.cloud" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Max Storage per Account (MB)</label>
                            <input type="number" name="max_storage_mb" value="1024" 
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="enable_tls" checked
                                   class="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-gray-300">Enable TLS/SSL</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="enable_auth" checked
                                   class="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-gray-300">Require Authentication</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="enable_spam_filter"
                                   class="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-gray-300">Enable Spam Filter</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </form>
            </div>
        </div>

        <!-- Email Templates Tab -->
        <div id="content-templates" class="tab-content hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">Email Templates</h3>
                    <button onclick="createTemplate()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>New Template
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-white">Welcome Email</h4>
                            <span class="px-2 py-1 bg-green-600 text-green-100 text-xs rounded-full">Active</span>
                        </div>
                        <p class="text-gray-400 text-sm mb-4">Sent to new users when they register</p>
                        <div class="flex space-x-2">
                            <button onclick="editTemplate('welcome')" class="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button onclick="previewTemplate('welcome')" class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
                                <i class="fas fa-eye mr-1"></i>Preview
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-white">Password Reset</h4>
                            <span class="px-2 py-1 bg-green-600 text-green-100 text-xs rounded-full">Active</span>
                        </div>
                        <p class="text-gray-400 text-sm mb-4">Sent when users request password reset</p>
                        <div class="flex space-x-2">
                            <button onclick="editTemplate('password-reset')" class="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button onclick="previewTemplate('password-reset')" class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
                                <i class="fas fa-eye mr-1"></i>Preview
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-white">Account Notification</h4>
                            <span class="px-2 py-1 bg-gray-600 text-gray-100 text-xs rounded-full">Draft</span>
                        </div>
                        <p class="text-gray-400 text-sm mb-4">General account notifications</p>
                        <div class="flex space-x-2">
                            <button onclick="editTemplate('notification')" class="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button onclick="previewTemplate('notification')" class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
                                <i class="fas fa-eye mr-1"></i>Preview
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('bg-purple-600', 'border-purple-600', 'text-white');
        button.classList.add('text-gray-300');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Add active class to selected tab button
    const activeButton = document.getElementById(`tab-${tabName}`);
    activeButton.classList.add('bg-purple-600', 'border-purple-600', 'text-white');
    activeButton.classList.remove('text-gray-300');
}

function createEmailAccount() {
    window.location.href = '/admin/email/account/new';
}

function viewEmailAccount(accountId) {
    window.location.href = `/admin/email/account/${accountId}`;
}

function editEmailAccount(accountId) {
    window.location.href = `/admin/email/account/${accountId}/edit`;
}

function resetPassword(accountId) {
    if (confirm('Are you sure you want to reset the password for this email account?')) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/reset-password`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Password reset successfully. New password: ${data.new_password}`);
            } else {
                alert('Error resetting password');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting password');
        });
    }
}

function deleteEmailAccount(accountId, emailAddress) {
    if (confirm(`Are you sure you want to delete email account "${emailAddress}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting email account');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting email account');
        });
    }
}

function createTemplate() {
    window.location.href = '/admin/email/template/new';
}

function editTemplate(templateName) {
    window.location.href = `/admin/email/template/${templateName}/edit`;
}

function previewTemplate(templateName) {
    window.open(`/admin/email/template/${templateName}/preview`, '_blank');
}

// Email settings form submission
document.getElementById('email-settings-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/email/settings', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Email settings saved successfully');
        } else {
            alert('Error saving settings: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving settings');
    });
});
</script>
{% endblock %}
