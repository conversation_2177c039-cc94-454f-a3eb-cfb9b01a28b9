{% extends "base.html" %}

{% block title %}{{ messages['base.html']['site_title'] }} - View Email Account{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">Email Account Details</h1>
                    <p class="mt-2 text-gray-400">View and manage email account information</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ url_for('admin_edit_email_account', account_id=account.id) }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Account
                    </a>
                    <a href="{{ url_for('admin_email') }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Email Management
                    </a>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-white mb-4">
                    <i class="fas fa-user mr-2"></i>Account Information
                </h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Email Address</label>
                        <p class="text-white text-lg">{{ account.email_address }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Display Name</label>
                        <p class="text-white">{{ account.display_name or 'Not set' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Owner</label>
                        <p class="text-white">{{ account.user.username if account.user else 'Unknown' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Status</label>
                        {% if account.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Active
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times-circle mr-1"></i>Inactive
                            </span>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Verified</label>
                        {% if account.is_verified %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Verified
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-exclamation-circle mr-1"></i>Unverified
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Storage Information -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-white mb-4">
                    <i class="fas fa-hdd mr-2"></i>Storage Information
                </h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Storage Used</label>
                        <p class="text-white text-lg">{{ account.storage_used_mb }} MB</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Storage Limit</label>
                        <p class="text-white">{{ account.storage_limit_mb }} MB</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Usage Percentage</label>
                        {% set usage_percent = (account.storage_used_mb / account.storage_limit_mb * 100) if account.storage_limit_mb > 0 else 0 %}
                        <div class="w-full bg-gray-700 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ usage_percent }}%"></div>
                        </div>
                        <p class="text-sm text-gray-400 mt-1">{{ "%.1f"|format(usage_percent) }}% used</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Created</label>
                        <p class="text-white">{{ account.created_at.strftime('%Y-%m-%d %H:%M:%S') if account.created_at else 'Unknown' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400">Last Login</label>
                        <p class="text-white">{{ account.last_login.strftime('%Y-%m-%d %H:%M:%S') if account.last_login else 'Never' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Messages -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">
                <i class="fas fa-envelope mr-2"></i>Recent Messages ({{ recent_messages|length }})
            </h2>
            {% if recent_messages %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">From/To</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Folder</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-gray-800 divide-y divide-gray-700">
                            {% for message in recent_messages %}
                            <tr class="hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ message.subject or '(No Subject)' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-300">
                                        {% if message.folder == 'SENT' %}
                                            To: {{ message.recipients[:50] }}...
                                        {% else %}
                                            From: {{ message.sender }}
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        {% if message.folder == 'INBOX' %}bg-blue-100 text-blue-800
                                        {% elif message.folder == 'SENT' %}bg-green-100 text-green-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ message.folder }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                    {{ message.received_at.strftime('%Y-%m-%d %H:%M') if message.received_at else 'Unknown' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if message.is_read %}
                                        <span class="text-gray-400"><i class="fas fa-envelope-open"></i></span>
                                    {% else %}
                                        <span class="text-blue-400"><i class="fas fa-envelope"></i></span>
                                    {% endif %}
                                    {% if message.is_starred %}
                                        <span class="text-yellow-400 ml-2"><i class="fas fa-star"></i></span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-inbox text-4xl text-gray-600 mb-4"></i>
                    <p class="text-gray-400">No messages found for this account</p>
                </div>
            {% endif %}
        </div>

        <!-- Actions -->
        <div class="mt-8 flex justify-between">
            <div class="flex space-x-3">
                <button onclick="resetPassword({{ account.id }})" 
                        class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-key mr-2"></i>Reset Password
                </button>
                <button onclick="toggleAccountStatus({{ account.id }}, {{ 'false' if account.is_active else 'true' }})" 
                        class="bg-{% if account.is_active %}red{% else %}green{% endif %}-600 hover:bg-{% if account.is_active %}red{% else %}green{% endif %}-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-{% if account.is_active %}pause{% else %}play{% endif %} mr-2"></i>
                    {% if account.is_active %}Deactivate{% else %}Activate{% endif %} Account
                </button>
            </div>
            <button onclick="deleteAccount({{ account.id }}, '{{ account.email_address }}')" 
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-trash mr-2"></i>Delete Account
            </button>
        </div>
    </div>
</div>

<script>
function resetPassword(accountId) {
    if (confirm('Are you sure you want to reset the password for this email account?')) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/reset-password`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Password reset successfully. New password: ${data.new_password}`);
            } else {
                alert('Error resetting password');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting password');
        });
    }
}

function deleteAccount(accountId, emailAddress) {
    if (confirm(`Are you sure you want to delete email account "${emailAddress}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        
        fetch(`/admin/email/account/${accountId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '/admin/email';
            } else {
                alert('Error deleting email account');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting email account');
        });
    }
}
</script>
{% endblock %}
