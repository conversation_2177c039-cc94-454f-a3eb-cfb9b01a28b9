{% extends "base.html" %}
{% block title %}{% if template %}Edit{% else %}Create{% endif %} Email Template - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    {% if template %}
                        <i class="fas fa-edit mr-3"></i>
                        Edit Email Template
                    {% else %}
                        <i class="fas fa-plus mr-3"></i>
                        Create Email Template
                    {% endif %}
                </h1>
                <p class="text-indigo-100">
                    {% if template %}
                        {{ template.name }} - {{ template.template_type.title() }} Template
                    {% else %}
                        Create a new email template with HTML editor and live preview
                    {% endif %}
                </p>
            </div>
            <div class="flex space-x-3">
                <button onclick="togglePreview()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                    <i class="fas fa-eye mr-2"></i>Toggle Preview
                </button>
                <a href="{{ url_for('admin_email_templates') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Templates
                </a>
            </div>
        </div>
    </div>

    <!-- Editor Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="editor-layout">
        <!-- Editor Panel -->
        <div class="bg-gray-800 rounded-lg border border-gray-700">
            <div class="p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">
                    <i class="fas fa-code mr-2"></i>Template Editor
                </h3>
            </div>
            
            <form method="POST" id="template-form" class="p-6 space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Template Name -->
                <div>
                    <label for="{{ form.name.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Template Name
                    </label>
                    {{ form.name(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500") }}
                    {% if form.name.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Description -->
                <div>
                    <label for="{{ form.description.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Description
                    </label>
                    {{ form.description(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500") }}
                </div>

                <!-- Template Type -->
                <div>
                    <label for="{{ form.template_type.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Template Type
                    </label>
                    {{ form.template_type(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500") }}
                </div>

                <!-- Subject -->
                <div>
                    <label for="{{ form.subject.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Email Subject
                    </label>
                    {{ form.subject(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500", oninput="updatePreview()") }}
                </div>

                <!-- HTML Content -->
                <div>
                    <label for="{{ form.body_html.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        HTML Content
                    </label>
                    <div class="relative">
                        {{ form.body_html(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 font-mono text-sm", rows="15", oninput="updatePreview()") }}
                        <div class="absolute top-2 right-2">
                            <button type="button" onclick="insertTemplate()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-magic mr-1"></i>Insert Template
                            </button>
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-400">
                        Available variables: {{ '{user_name}' }}, {{ '{user_email}' }}, {{ '{site_name}' }}, {{ '{admin_email}' }}
                    </div>
                </div>

                <!-- Plain Text Content -->
                <div>
                    <label for="{{ form.body_text.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Plain Text Content (Fallback)
                    </label>
                    {{ form.body_text(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 font-mono text-sm", rows="8") }}
                </div>

                <!-- Active Status -->
                <div class="flex items-center">
                    {{ form.is_active(class="mr-2") }}
                    <label for="{{ form.is_active.id }}" class="text-sm text-gray-300">
                        Active Template
                    </label>
                </div>

                <!-- Submit Buttons -->
                <div class="flex space-x-3">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Template
                    </button>
                    <button type="button" onclick="previewInNewTab()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>Preview in New Tab
                    </button>
                </div>
            </form>
        </div>

        <!-- Live Preview Panel -->
        <div class="bg-gray-800 rounded-lg border border-gray-700" id="preview-panel">
            <div class="p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">
                    <i class="fas fa-eye mr-2"></i>Live Preview
                </h3>
            </div>
            
            <div class="p-6">
                <!-- Email Preview -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <!-- Email Header -->
                    <div class="bg-gray-100 px-4 py-3 border-b">
                        <div class="text-sm text-gray-600">
                            <strong>From:</strong> LXND System &lt;<EMAIL>&gt;
                        </div>
                        <div class="text-sm text-gray-600">
                            <strong>To:</strong> John Doe &lt;<EMAIL>&gt;
                        </div>
                        <div class="text-lg font-semibold text-gray-800 mt-2" id="preview-subject">
                            {{ template.subject or 'No Subject' }}
                        </div>
                    </div>
                    
                    <!-- Email Body -->
                    <div class="p-6" id="preview-body">
                        {% if template.body_html %}
                            {{ template.body_html | safe }}
                        {% else %}
                            <p class="text-gray-500">No content to preview</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let previewVisible = true;

function togglePreview() {
    const layout = document.getElementById('editor-layout');
    const panel = document.getElementById('preview-panel');
    
    if (previewVisible) {
        layout.classList.remove('lg:grid-cols-2');
        layout.classList.add('lg:grid-cols-1');
        panel.style.display = 'none';
        previewVisible = false;
    } else {
        layout.classList.remove('lg:grid-cols-1');
        layout.classList.add('lg:grid-cols-2');
        panel.style.display = 'block';
        previewVisible = true;
        updatePreview();
    }
}

function updatePreview() {
    if (!previewVisible) return;
    
    const subject = document.getElementById('{{ form.subject.id }}').value;
    const htmlContent = document.getElementById('{{ form.body_html.id }}').value;
    
    // Update subject
    document.getElementById('preview-subject').textContent = subject || 'No Subject';
    
    // Update body with sample variables
    let processedHtml = htmlContent;
    processedHtml = processedHtml.replace(/\{user_name\}/g, 'John Doe');
    processedHtml = processedHtml.replace(/\{user_email\}/g, '<EMAIL>');
    processedHtml = processedHtml.replace(/\{site_name\}/g, 'LXND');
    processedHtml = processedHtml.replace(/\{admin_email\}/g, '<EMAIL>');
    
    document.getElementById('preview-body').innerHTML = processedHtml || '<p class="text-gray-500">No content to preview</p>';
}

function insertTemplate() {
    const htmlTextarea = document.getElementById('{{ form.body_html.id }}');
    const template = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{site_name} - Email</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to {site_name}</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {user_name}!</h2>
        
        <p>Thank you for joining {site_name}. We're excited to have you on board!</p>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
            <p style="margin: 0;"><strong>Your account details:</strong></p>
            <p style="margin: 5px 0;">Email: {user_email}</p>
        </div>
        
        <p>If you have any questions, feel free to contact us at {admin_email}.</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://lxnd.cloud" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Get Started</a>
        </div>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent by {site_name}<br>
            If you have any questions, contact us at {admin_email}
        </p>
    </div>
</body>
</html>`;
    
    htmlTextarea.value = template.trim();
    updatePreview();
}

function previewInNewTab() {
    const form = document.getElementById('template-form');
    const formData = new FormData(form);
    
    // Create a temporary form to submit to preview endpoint
    const tempForm = document.createElement('form');
    tempForm.method = 'POST';
    tempForm.action = '{{ url_for("admin_preview_email_template", template_id=template.id) }}';
    tempForm.target = '_blank';
    
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        tempForm.appendChild(input);
    }
    
    document.body.appendChild(tempForm);
    tempForm.submit();
    document.body.removeChild(tempForm);
}

// Initialize preview
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});
</script>
{% endblock %}
