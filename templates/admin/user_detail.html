{% extends "base.html" %}
{% block title %}User Details - {{ user.username }} - Admin{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-user mr-3"></i>
                    {{ user.username }}
                </h1>
                <p class="text-blue-100">User account management and settings</p>
            </div>
            <div class="text-right">
                <a href="{{ url_for('admin_users') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-xl font-bold text-white mb-6">User Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Username</label>
                <div class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white">
                    {{ user.username }}
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <div class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white">
                    {{ user.email }}
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Created</label>
                <div class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white">
                    {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Storage Used</label>
                <div class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white">
                    {{ "%.1f"|format(user.get_used_storage_mb()) }} MB
                </div>
            </div>
        </div>
    </div>

    <!-- User Settings -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-xl font-bold text-white mb-6">User Settings</h3>
        
        <form method="POST" action="{{ url_for('admin_update_user', user_id=user.id) }}">
            {{ csrf_token() }}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Permissions -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Permissions</h4>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_admin" {% if user.is_admin %}checked{% endif %}
                                   {% if user.id == current_user.id %}disabled{% endif %}
                                   class="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-gray-300">Administrator</span>
                            {% if user.id == current_user.id %}
                                <span class="ml-2 text-xs text-gray-500">(Cannot modify own admin status)</span>
                            {% endif %}
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="is_moderator" {% if user.is_moderator %}checked{% endif %}
                                   class="rounded border-gray-600 bg-gray-700 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-gray-300">Moderator</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="is_banned" {% if user.is_banned %}checked{% endif %}
                                   class="rounded border-gray-600 bg-gray-700 text-red-600 focus:ring-red-500">
                            <span class="ml-2 text-gray-300">Banned</span>
                        </label>
                    </div>
                </div>
                
                <!-- Lux Premium -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Lux Premium</h4>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_lux" {% if user.is_lux %}checked{% endif %}
                                   class="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500">
                            <span class="ml-2 text-gray-300">Lux Premium Status</span>
                        </label>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Lux Expiration (leave empty for permanent)</label>
                            <input type="datetime-local" name="lux_expires_at" 
                                   value="{% if user.lux_expires_at %}{{ user.lux_expires_at.strftime('%Y-%m-%dT%H:%M') }}{% endif %}"
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Limits -->
            <div class="mb-6">
                <h4 class="text-lg font-semibold text-white mb-4">Limits</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Storage Limit (MB)</label>
                        <input type="number" name="upload_limit_mb" value="{{ user.upload_limit_mb }}" min="0"
                               class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Max Projects</label>
                        <input type="number" name="max_projects" value="{{ user.max_projects }}" min="0"
                               class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Max Keys per Project</label>
                        <input type="number" name="max_keys_per_project" value="{{ user.max_keys_per_project }}" min="0"
                               class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            
            <div class="flex space-x-4">
                <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Save Changes
                </button>
                
                {% if user.id != current_user.id %}
                <button type="button" onclick="deleteUser()" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete User
                </button>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- User Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                    <i class="fas fa-project-diagram text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Projects</p>
                    <p class="text-2xl font-bold text-white">{{ user.projects|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                    <i class="fas fa-file text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Files</p>
                    <p class="text-2xl font-bold text-white">{{ user.files|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                    <i class="fas fa-envelope text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Email Accounts</p>
                    <p class="text-2xl font-bold text-white">{{ user.email_accounts|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Projects -->
    {% if user.projects %}
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white">Recent Projects</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for project in user.projects[:5] %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-project-diagram text-white"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-white font-medium">{{ project.name }}</p>
                            <p class="text-gray-400 text-sm">{{ project.created_at.strftime('%Y-%m-%d') }}</p>
                        </div>
                    </div>
                    <div class="text-gray-400 text-sm">
                        {{ project.licenses|length }} licenses
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function deleteUser() {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone and will delete all associated data.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("admin_delete_user", user_id=user.id) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
