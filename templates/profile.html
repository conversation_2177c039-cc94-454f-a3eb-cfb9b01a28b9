{% extends "base.html" %}
{% block title %}Profile - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Profile Header -->
    <div class="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-8 shadow-2xl border border-emerald-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mr-6 backdrop-blur-sm">
                        <i class="fas fa-user text-white text-3xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            {{ user.username }}
                        </h1>
                        <p class="text-emerald-100 text-lg">{{ user.email }}</p>
                        <div class="flex items-center mt-2 space-x-2">
                            {% if user.is_lux_active() %}
                                <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                    <i class="fas fa-crown mr-1"></i>
                                    Lux Premium
                                </span>
                            {% else %}
                                <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    Standard User
                                </span>
                            {% endif %}
                            {% if user.is_admin %}
                                <span class="bg-red-500 text-red-100 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                    <i class="fas fa-shield-alt mr-1"></i>Administrator
                                </span>
                            {% endif %}
                            {% if user.discord_id %}
                                <span class="bg-indigo-500 text-indigo-100 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                    <i class="fab fa-discord mr-1"></i>Discord Linked
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                    <div class="text-white text-sm opacity-75 uppercase tracking-wide mb-1">
                        Member Since
                    </div>
                    <div class="text-white text-2xl font-bold">
                        {{ user.created_at.strftime('%b %Y') }}
                    </div>
                    <div class="text-emerald-200 text-sm">
                        Active member
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Account Information -->
        <div class="lg:col-span-2">
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl">
                <div class="px-8 py-6 border-b border-gray-700/50">
                    <h3 class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-user-circle text-emerald-400 mr-3"></i>
                        Account Information
                    </h3>
                </div>
                <div class="p-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Basic Info -->
                        <div class="space-y-6">
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30">
                                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                                    <i class="fas fa-id-card text-blue-400 mr-2"></i>
                                    Basic Information
                                </h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Username</span>
                                        <span class="text-white font-bold">{{ user.username }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Email</span>
                                        <span class="text-white">{{ user.email }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">User ID</span>
                                        <span class="text-gray-400 font-mono text-sm">#{{ user.id }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Account Type</span>
                                        {% if user.is_admin %}
                                            <span class="bg-red-500/20 text-red-300 px-2 py-1 rounded-lg text-sm font-medium">
                                                <i class="fas fa-shield-alt mr-1"></i>Administrator
                                            </span>
                                        {% elif user.is_lux_active() %}
                                            <span class="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 text-yellow-300 px-2 py-1 rounded-lg text-sm font-medium">
                                                <i class="fas fa-crown mr-1"></i>Lux Premium
                                            </span>
                                        {% else %}
                                            <span class="bg-gray-500/20 text-gray-300 px-2 py-1 rounded-lg text-sm font-medium">
                                                <i class="fas fa-user mr-1"></i>Standard
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Account Statistics -->
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30">
                                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                                    <i class="fas fa-chart-bar text-purple-400 mr-2"></i>
                                    Account Statistics
                                </h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Projects</span>
                                        <span class="text-white font-bold">{{ user.projects|length }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Files Uploaded</span>
                                        <span class="text-white font-bold">{{ user.files|length }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Storage Used</span>
                                        <span class="text-white font-bold">{{ "%.1f"|format(user.get_used_storage_mb()) }} MB</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Email Accounts</span>
                                        <span class="text-white font-bold">{{ user.email_accounts|length if user.email_accounts else 0 }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security & Settings -->
                        <div class="space-y-6">
                            <!-- Security Settings -->
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30">
                                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                                    <i class="fas fa-shield-alt text-green-400 mr-2"></i>
                                    Security Settings
                                </h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Password</span>
                                        <button onclick="showPasswordForm()" class="bg-blue-600/80 hover:bg-blue-600 text-white px-3 py-1 rounded-lg text-sm transition-all duration-300">
                                            <i class="fas fa-edit mr-1"></i>Change
                                        </button>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Two-Factor Auth</span>
                                        <span class="bg-red-500/20 text-red-300 px-2 py-1 rounded-lg text-sm">
                                            <i class="fas fa-times mr-1"></i>Disabled
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Last Login</span>
                                        <span class="text-gray-400 text-sm">{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Lux Premium Status -->
                            {% if user.is_lux_active() %}
                            <div class="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 backdrop-blur-sm rounded-xl p-6 border border-yellow-400/30">
                                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                                    <i class="fas fa-crown text-yellow-400 mr-2"></i>
                                    Lux Premium Status
                                </h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Status</span>
                                        <span class="bg-green-500/20 text-green-300 px-2 py-1 rounded-lg text-sm">
                                            <i class="fas fa-check mr-1"></i>Active
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-300 font-medium">Benefits</span>
                                        <span class="text-yellow-300">Enhanced Limits</span>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30">
                                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-2"></i>
                                    Upgrade to Lux Premium
                                </h4>
                                <p class="text-gray-300 text-sm mb-4">Unlock premium features and enhanced limits</p>
                                <a href="{{ url_for('projects.lux_dashboard') }}" class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block">
                                    <i class="fas fa-crown mr-2"></i>
                                    Upgrade Now
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Password Change Form (Hidden by default) -->
                    <div id="password-form" class="hidden mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30">
                        <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                            <i class="fas fa-key text-blue-400 mr-2"></i>
                            Change Password
                        </h4>
                        <form method="POST" action="{{ url_for('auth.change_account_password') }}" class="space-y-4">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
                                <input type="password" name="current_password" required 
                                       class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">New Password</label>
                                <input type="password" name="new_password" required 
                                       class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                                <input type="password" name="confirm_password" required 
                                       class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                            </div>
                            <div class="flex space-x-3">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-save mr-2"></i>Update Password
                                </button>
                                <button type="button" onclick="hidePasswordForm()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <a href="{{ url_for('main.dashboard') }}"
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-blue-500/40">
                        <i class="fas fa-home text-blue-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Dashboard</span>
                    </a>
                    <a href="{{ url_for('email.user_email_dashboard') }}"
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-purple-500/40">
                        <i class="fas fa-envelope text-purple-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Email</span>
                    </a>
                    <a href="{{ url_for('files.files_dashboard') }}"
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-green-500/40">
                        <i class="fas fa-folder text-green-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Files</span>
                    </a>
                    {% if not user.is_lux_active() %}
                    <a href="{{ url_for('projects.lux_dashboard') }}"
                       class="w-full flex items-center p-4 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 hover:from-yellow-400/30 hover:to-orange-500/30 rounded-xl transition-all duration-300 group border border-yellow-400/30 hover:border-yellow-400/50">
                        <i class="fas fa-crown text-yellow-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Upgrade to Lux</span>
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Discord Integration -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fab fa-discord text-indigo-400 mr-2"></i>
                        Discord Integration
                    </h3>
                </div>
                <div class="p-6">
                    {% if user.discord_id %}
                        <!-- Discord Linked -->
                        <div class="flex items-center space-x-4 mb-4">
                            {% if user.discord_avatar %}
                                <img src="https://cdn.discordapp.com/avatars/{{ user.discord_id }}/{{ user.discord_avatar }}.png?size=64"
                                     alt="Discord Avatar" class="w-12 h-12 rounded-full">
                            {% else %}
                                <div class="w-12 h-12 rounded-full bg-indigo-600 flex items-center justify-center">
                                    <i class="fab fa-discord text-white text-xl"></i>
                                </div>
                            {% endif %}
                            <div>
                                <p class="text-white font-medium">{{ user.discord_username }}</p>
                                <p class="text-gray-400 text-sm">Linked {{ user.discord_linked_at.strftime('%Y-%m-%d') }}</p>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Status</span>
                                <span class="bg-green-500 text-green-100 px-2 py-1 rounded-full text-xs font-medium">
                                    <i class="fas fa-check-circle mr-1"></i>Connected
                                </span>
                            </div>

                            {% if user.is_lux_active() %}
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Discord Role</span>
                                <span class="bg-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium">
                                    <i class="fas fa-crown mr-1"></i>Lux Premium
                                </span>
                            </div>
                            {% endif %}

                            <form method="POST" action="{{ url_for('discord.unlink_discord') }}" class="mt-4">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300">
                                    <i class="fas fa-unlink mr-2"></i>Unlink Discord
                                </button>
                            </form>
                        </div>
                    {% else %}
                        <!-- Discord Not Linked -->
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-full bg-indigo-600/20 flex items-center justify-center mx-auto mb-4">
                                <i class="fab fa-discord text-indigo-400 text-2xl"></i>
                            </div>
                            <h4 class="text-white font-medium mb-2">Connect Discord</h4>
                            <p class="text-gray-400 text-sm mb-4">Link your Discord account to access bot commands and get automatic role updates.</p>

                            <div class="space-y-3 text-left">
                                <div class="flex items-center text-sm text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2"></i>
                                    Access Discord bot commands
                                </div>
                                <div class="flex items-center text-sm text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2"></i>
                                    Automatic Lux role updates
                                </div>
                                <div class="flex items-center text-sm text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2"></i>
                                    Support ticket integration
                                </div>
                            </div>

                            <a href="{{ url_for('discord.discord_auth') }}" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 inline-block mt-4">
                                <i class="fab fa-discord mr-2"></i>Connect Discord
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Account Summary -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-chart-pie text-emerald-400 mr-2"></i>
                        Account Summary
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Total Projects</span>
                        <span class="text-white font-bold">{{ user.projects|length }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Files Stored</span>
                        <span class="text-white font-bold">{{ user.files|length }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Storage Used</span>
                        <span class="text-white font-bold">{{ user.get_storage_used_formatted() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Email Accounts</span>
                        <span class="text-white font-bold">{{ user.email_accounts|length if user.email_accounts else 0 }}</span>
                    </div>
                    
                    <!-- Storage Progress Bar -->
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-400 mb-2">
                            <span>Storage Usage</span>
                            <span>{{ user.get_storage_used_formatted() }} / {{ (user.get_storage_limit_mb_effective() / 1024)|round(1) }} GB</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full transition-all duration-300" 
                                 style="width: {{ (user.get_used_storage_mb() / user.get_storage_limit_mb_effective() * 100)|round(1) if user.get_storage_limit_mb_effective() > 0 else 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showPasswordForm() {
    document.getElementById('password-form').classList.remove('hidden');
}

function hidePasswordForm() {
    document.getElementById('password-form').classList.add('hidden');
    // Clear form fields
    const form = document.querySelector('#password-form form');
    form.reset();
}
</script>
{% endblock %}
