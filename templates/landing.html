{% extends "base.html" %}

{% block title %}{{ messages['landing.html']['page_title'] }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-gradient py-20">
    <div class="max-w-screen-xl mx-auto px-4 text-center">
        <img src="/static/images/logo.png" alt="LXND Logo" style="width: 200px; display: block; margin: auto;" class="center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">{{ messages['landing.html']['hero_title'] }}</h1>
        <p class="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto">{{ messages['landing.html']['hero_subtitle'] }}</p>
        <!-- Premium Badge -->
        <div class="inline-flex items-center bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
            <i class="fas fa-crown mr-2"></i>
            Now with Lux Premium Features
        </div>

        <div class="flex flex-col sm:flex-row justify-center gap-4">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for('main.dashboard') }}" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-lg px-8 py-4 focus:outline-none">{{ messages['landing.html']['btn_dashboard'] }}</a>
                {% if not current_user.is_lux_active() %}
                    <a href="{{ url_for('lux_dashboard') }}" class="text-white bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 focus:ring-4 focus:ring-yellow-800 font-medium rounded-lg text-lg px-8 py-4 focus:outline-none">
                        <i class="fas fa-crown mr-2"></i>
                        Upgrade to Lux
                    </a>
                {% endif %}
            {% else %}
                <a href="{{ url_for('auth.register') }}" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-lg px-8 py-4 focus:outline-none">{{ messages['landing.html']['btn_get_started'] }}</a>
                <a href="{{ url_for('auth.login') }}" class="text-blue-400 border border-blue-600 hover:bg-blue-900/20 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-lg px-8 py-4 focus:outline-none">{{ messages['landing.html']['btn_login'] }}</a>
            {% endif %}
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-20">
    <div class="max-w-screen-xl mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-5xl font-bold text-white mb-4">{{ messages['landing.html']['services_title'] }}</h2>
            <p class="text-xl text-gray-400 max-w-2xl mx-auto">{{ messages['landing.html']['services_subtitle'] }}</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Website Hosting Card -->
            <div class="bg-gray-800 border border-gray-700 feature-card rounded-lg shadow-lg p-8">
                <div class="text-5xl mb-4">🌐</div>
                <h4 class="text-2xl font-semibold text-white mb-3">Website Hosting</h4>
                <p class="text-gray-400 mb-6">Professional website hosting with SSL certificates and SFTP access</p>
                <ul class="text-gray-400 mb-6 space-y-2">
                    <li><i class="fas fa-check text-green-400 mr-2"></i>Custom subdomains (yourname.lxnd.cloud)</li>
                    <li><i class="fas fa-check text-green-400 mr-2"></i>Free SSL certificates (Let's Encrypt)</li>
                    <li><i class="fas fa-check text-green-400 mr-2"></i>SFTP file upload access</li>
                    <li><i class="fas fa-check text-green-400 mr-2"></i>Up to 5GB storage per website</li>
                    <li><i class="fas fa-check text-green-400 mr-2"></i>Real-time file management</li>
                </ul>
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('websites') }}" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-6 py-3 transition-colors">
                        Manage Websites
                    </a>
                {% else %}
                    <a href="{{ url_for('register') }}" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-6 py-3 transition-colors">
                        Get Started
                    </a>
                {% endif %}
            </div>

            <!-- License Manager Card -->
            <div class="bg-gray-800 border border-gray-700 feature-card rounded-lg shadow-lg p-8">
                <div class="text-5xl mb-4">🔑</div>
                <h4 class="text-2xl font-semibold text-white mb-3">{{ messages['landing.html']['license_manager_title'] }}</h4>
                <p class="text-gray-400 mb-6">{{ messages['landing.html']['license_manager_desc'] }}</p>
                <ul class="text-gray-400 mb-6 space-y-2">
                    {% for feature in messages['landing.html']['license_manager_features'] %}
                    <li>{{ feature }}</li>
                    {% endfor %}
                </ul>
                <a href="{{ url_for('dashboard') if current_user.is_authenticated else url_for('login') }}" class="inline-block text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                    {% if current_user.is_authenticated %}{{ messages['landing.html']['btn_manage_licenses'] }}{% else %}{{ messages['landing.html']['btn_get_started'] }}{% endif %}
                </a>
            </div>
            
            <!-- File Upload Card -->
            <div class="bg-gray-800 border border-gray-700 feature-card rounded-lg shadow-lg p-8">
                <div class="text-5xl mb-4">📁</div>
                <h4 class="text-2xl font-semibold text-white mb-3">{{ messages['landing.html']['file_uploader_title'] }}</h4>
                <p class="text-gray-400 mb-6">{{ messages['landing.html']['file_uploader_desc'] }}</p>
                <ul class="text-gray-400 mb-6 space-y-2">
                    {% for feature in messages['landing.html']['file_uploader_features'] %}
                    <li>{{ feature }}</li>
                    {% endfor %}
                </ul>
                <a href="{{ url_for('files_dashboard') if current_user.is_authenticated else url_for('login') }}" class="inline-block text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                    {% if current_user.is_authenticated %}{{ messages['landing.html']['btn_manage_files'] }}{% else %}{{ messages['landing.html']['btn_get_started'] }}{% endif %}
                </a>
            </div>

            <!-- Link Shortener Card -->
            <div class="bg-gray-800 border border-gray-700 feature-card rounded-lg shadow-lg p-8">
                <div class="text-5xl mb-4">🔗</div>
                <h4 class="text-2xl font-semibold text-white mb-3">Link Shortener</h4>
                <p class="text-gray-400 mb-6">Create short, memorable links with advanced analytics and custom domains.</p>
                <ul class="text-gray-400 mb-6 space-y-2">
                    <li>• Custom short codes</li>
                    <li>• Password protection</li>
                    <li>• Click analytics</li>
                    <li>• Expiration dates</li>
                </ul>
                <a href="{{ url_for('shorten_url_page') }}" class="inline-block text-white bg-purple-600 hover:bg-purple-700 focus:ring-4 focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                    Create Short Link
                </a>
            </div>

            <!-- Email Services Card -->
            <div class="bg-gray-800 border border-gray-700 feature-card rounded-lg shadow-lg p-8">
                <div class="text-5xl mb-4">📧</div>
                <h4 class="text-2xl font-semibold text-white mb-3">Email Services</h4>
                <p class="text-gray-400 mb-6">Professional email adress with SMTP, IMAP, and web interface.</p>
                <ul class="text-gray-400 mb-6 space-y-2">
                    <li>• Custom email addresses</li>
                    <li>• Web-based inbox</li>
                    <li>• SMTP/IMAP support</li>
                    <li>• Email templates</li>
                </ul>
                <a href="{{ url_for('user_email_dashboard') if current_user.is_authenticated else url_for('login') }}" class="inline-block text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-800 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                    {% if current_user.is_authenticated %}Manage Email{% else %}Get Started{% endif %}
                </a>
            </div>

            <!-- Lux Premium Card -->
            <div class="bg-gradient-to-br from-yellow-400 to-orange-500 feature-card rounded-lg shadow-lg p-8 lg:col-span-3">
                <div class="text-center">
                    <div class="text-6xl mb-4">👑</div>
                    <h4 class="text-3xl font-bold text-white mb-3">Lux Premium</h4>
                    <p class="text-yellow-100 mb-6 text-lg">Unlock enhanced limits and premium features</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">Enhanced File Storage</h5>
                            <p class="text-yellow-100 text-sm">Up to 1TB File storage space</p>
                        </div>
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">More Projects</h5>
                            <p class="text-yellow-100 text-sm">Up to 50 projects</p>
                        </div>
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">1000 Short URLs</h5>
                            <p class="text-yellow-100 text-sm">Advanced link management</p>
                        </div>
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">More Custom Websites</h5>
                            <p class="text-yellow-100 text-sm">Three instead of one</p>
                        </div>
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">More Website Storage</h5>
                            <p class="text-yellow-100 text-sm">10GB instead of 100MB</p>
                        </div>
                        <div class="bg-white/10 rounded-lg p-4">
                            <h5 class="text-white font-semibold mb-2">More Email Accounts</h5>
                            <p class="text-yellow-100 text-sm">Create up to 3 @lxnd.cloud email accounts</p>
                        </div>
                    </div>

                    {% if current_user.is_authenticated %}
                        {% if current_user.is_lux_active() %}
                            <div class="inline-flex items-center bg-white/20 text-white px-6 py-3 rounded-lg font-medium">
                                <i class="fas fa-check-circle mr-2"></i>
                                You have Lux Premium!
                            </div>
                        {% else %}
                            <a href="{{ url_for('lux_dashboard') }}" class="inline-block bg-white text-orange-500 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg transition-colors">
                                <i class="fas fa-crown mr-2"></i>
                                Upgrade to Lux
                            </a>
                        {% endif %}
                    {% else %}
                        <a href="{{ url_for('register') }}" class="inline-block bg-white text-orange-500 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg transition-colors">
                            Get Started with Lux
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- API Preview Section -->
<section class="py-20">
    <div class="max-w-screen-xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">{{ messages['landing.html']['api_title'] }}</h2>
                <p class="text-xl text-gray-400 mb-8">{{ messages['landing.html']['api_subtitle'] }}</p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ url_for('api_docs') }}" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-sm px-6 py-3 focus:outline-none">{{ messages['landing.html']['btn_view_api_docs'] }}</a>
                    <a href="{{ url_for('register') }}" class="text-blue-400 border border-blue-600 hover:bg-blue-900/20 focus:ring-4 focus:ring-blue-800 font-medium rounded-lg text-sm px-6 py-3 focus:outline-none">{{ messages['landing.html']['btn_try_now'] }}</a>
                </div>
            </div>
            <div>
                <div class="bg-gray-900 text-white p-6 rounded-lg">
                    <h6 class="text-yellow-400 mb-4 font-medium">{{ messages['landing.html']['api_example_title'] }}</h6>
                    <pre class="text-sm overflow-x-auto"><code><span class="text-green-400">GET</span> <span class="text-blue-400">/api/check/PROJECT_ID/LICENSE_KEY</span>

<span class="text-yellow-400">Response:</span>
<span class="text-white">{
  "valid": true,
  "is_active": true,
  "is_expired": false,
  "expires_at": "2024-12-31T23:59:59",
  "days_until_expiry": 45
}</span></code></pre>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="max-w-screen-xl mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-5xl font-bold mb-6">{{ messages['landing.html']['cta_title'] }}</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">{{ messages['landing.html']['cta_subtitle'] }}</p>
        <a href="{{ url_for('register') }}" class="text-blue-600 bg-white hover:bg-gray-100 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-lg px-8 py-3">{{ messages['landing.html']['btn_create_account'] }}</a>
    </div>
</section>

<!-- Footer -->
<footer class="bg-gray-950 text-gray-400 py-8 border-t border-gray-800">
    <div class="max-w-screen-xl mx-auto px-4 text-center">
        <p>{{ messages['landing.html']['footer_text'] }}</p>
    </div>
</footer>
{% endblock %}

<script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.js"></script>
</html>
