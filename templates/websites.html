{% extends "base.html" %}

{% block content %}
<div class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-globe mr-3 text-blue-400"></i>
                    My Websites
                </h1>
                <p class="text-gray-400">Create and manage your free websites</p>
            </div>
            <button onclick="openCreateModal()" 
                    class="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30">
                <i class="fas fa-plus mr-2"></i>
                Create Website
            </button>
        </div>

        <!-- Website Limits Info -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-bold text-white mb-2">Website Limits</h3>
                    <p class="text-gray-400">
                        {% if current_user.is_admin %}
                            <span class="text-green-400">Unlimited websites</span> • 10GB storage per website • SSL & SFTP included
                        {% elif current_user.is_lux_active() %}
                            <span class="text-yellow-400">Up to 5 websites</span> • 2GB storage per website • SSL & SFTP included
                        {% else %}
                            <span class="text-blue-400">1 website</span> • 100MB storage per website • SSL & SFTP included
                        {% endif %}
                    </p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-white">{{ websites|length }}</div>
                    <div class="text-sm text-gray-400">
                        {% if current_user.is_admin %}
                            Websites
                        {% elif current_user.is_lux_active() %}
                            / 5 Websites
                        {% else %}
                            / 1 Website
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Websites Grid -->
        {% if websites %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for website in websites %}
                <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                    <div class="p-6">
                        <!-- Website Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-white mb-1 group-hover:text-blue-400 transition-colors">
                                    {{ website.title }}
                                </h3>
                                <p class="text-sm text-gray-400 mb-2">{{ website.subdomain }}.lxnd.cloud</p>
                                {% if website.description %}
                                    <p class="text-gray-300 text-sm">{{ website.description[:100] }}{% if website.description|length > 100 %}...{% endif %}</p>
                                {% endif %}
                            </div>
                            <div class="flex items-center space-x-2">
                                {% if website.is_active %}
                                    <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                                {% else %}
                                    <span class="w-3 h-3 bg-red-500 rounded-full"></span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Storage Usage -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-400">Storage</span>
                                <span class="text-sm text-gray-300">{{ website.get_storage_used_formatted() }} / {{ (website.get_storage_limit_effective() / 1024)|round(1) }}GB</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full {% if website.get_storage_percent() >= 90 %}bg-red-500{% elif website.get_storage_percent() >= 70 %}bg-yellow-500{% else %}bg-blue-500{% endif %}"
                                     style="width: {{ website.get_storage_percent() }}%"></div>
                            </div>
                        </div>

                        <!-- Website Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-bold text-white">{{ website.files|length }}</div>
                                <div class="text-xs text-gray-400">Files</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-white">{{ website.created_at.strftime('%b %d') }}</div>
                                <div class="text-xs text-gray-400">Created</div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <a href="{{ url_for('website_detail', subdomain=website.subdomain) }}" 
                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center text-sm font-medium transition-all duration-300">
                                <i class="fas fa-cog mr-1"></i>
                                Manage
                            </a>
                            <a href="{{ website.get_url() }}" target="_blank"
                               class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-center text-sm font-medium transition-all duration-300">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                Visit
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-blue-400/30">
                    <i class="fas fa-globe text-blue-300 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-300 mb-3">No Websites Yet</h3>
                <p class="text-gray-400 mb-8 text-lg">Create your first free website to get started</p>
                <button onclick="openCreateModal()" 
                        class="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30">
                    <i class="fas fa-plus mr-2"></i>
                    Create Your First Website
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Create Website Modal -->
<div id="createModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Create New Website</h3>
            <button onclick="closeCreateModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form method="POST" action="{{ url_for('create_website') }}" class="space-y-4">
            {{ form.hidden_tag() }}
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Subdomain</label>
                {{ form.subdomain(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="mysite") }}
                <p class="text-xs text-gray-400 mt-1">Your website will be available at: <span id="subdomainPreview">mysite</span>.lxnd.cloud</p>
            </div>
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Website Title</label>
                {{ form.title(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="My Awesome Website") }}
            </div>
            
            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Description (Optional)</label>
                {{ form.description(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", rows="3", placeholder="Brief description of your website") }}
            </div>
            
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeCreateModal()" 
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" 
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Create Website
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openCreateModal() {
    document.getElementById('createModal').classList.remove('hidden');
    document.getElementById('createModal').classList.add('flex');
}

function closeCreateModal() {
    document.getElementById('createModal').classList.add('hidden');
    document.getElementById('createModal').classList.remove('flex');
}

// Update subdomain preview
document.querySelector('input[name="subdomain"]').addEventListener('input', function(e) {
    document.getElementById('subdomainPreview').textContent = e.target.value || 'mysite';
});
</script>
{% endblock %}
