{% extends "base.html" %}
{% block title %}File Manager - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Files Header -->
    <div class="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-folder-open mr-3"></i>
                    File Manager
                </h1>
                <p class="text-green-100">Upload, organize, and share your files securely</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Storage Used
                </div>
                <div class="text-white text-2xl font-bold">
                    {{ current_user.get_storage_used_formatted() }}
                </div>
                <div class="text-green-100 text-sm">
                    {% if current_user.get_storage_limit_mb_effective() > 0 %}
                        of {{ (current_user.get_storage_limit_mb_effective() / 1024)|round(1) }} GB
                    {% else %}
                        Unlimited
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Storage Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Files -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-blue-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                        <i class="fas fa-file text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Files</p>
                        <p class="text-2xl font-bold text-white">{{ files|length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Used -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-green-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                        <i class="fas fa-hdd text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Storage Used</p>
                        <p class="text-2xl font-bold text-white">{{ current_user.get_storage_used_formatted() }}</p>
                    </div>
                </div>
                {% if current_user.get_storage_limit_mb_effective() > 0 %}
                    {% set storage_percent = current_user.get_storage_percent() %}
                    <div class="mt-4">
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="h-2 rounded-full {% if storage_percent >= 90 %}bg-red-500{% elif storage_percent >= 70 %}bg-yellow-500{% else %}bg-green-500{% endif %}"
                                 style="width: {{ storage_percent }}%"></div>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">{{ storage_percent }}% used</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Uploads -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-purple-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                        <i class="fas fa-upload text-purple-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Recent Uploads</p>
                        <p class="text-2xl font-bold text-white">
                            {{ files[:7]|length if files else 0 }}
                        </p>
                        <p class="text-xs text-gray-400">Last 7 days</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Types -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-orange-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-600 bg-opacity-20">
                        <i class="fas fa-file-alt text-orange-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">File Types</p>
                        <p class="text-2xl font-bold text-white">
                            {% set extensions = [] %}
                            {% for file in files %}
                                {% if '.' in file.filename %}
                                    {% set ext = file.filename.split('.')[-1].lower() %}
                                    {% if ext not in extensions %}
                                        {% set _ = extensions.append(ext) %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ extensions|length }}
                        </p>
                        <p class="text-xs text-gray-400">Different formats</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- File Upload Section -->
        <div class="lg:col-span-3">
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-cloud-upload-alt text-green-400 mr-2"></i>
                        Upload Files
                    </h3>
                </div>
                <div class="p-6">
                    <div class="border-2 border-dashed border-gray-600 rounded-xl p-12 text-center hover:border-green-500 transition-all duration-300"
                         id="upload-area">
                        <div class="space-y-6">
                            <div class="w-20 h-20 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto">
                                <i class="fas fa-cloud-upload-alt text-green-400 text-3xl"></i>
                            </div>
                            <div>
                                <p class="text-2xl text-white font-bold mb-2">Upload Your Files</p>
                                <p class="text-gray-400">
                                    Drag and drop files here or click to browse
                                </p>
                                <p class="text-gray-500 text-sm mt-2">
                                    {% if current_user.get_storage_limit_mb_effective() > 0 %}
                                        Max file size: 10GB | Available: {{ ((current_user.get_storage_limit_mb_effective() - current_user.get_used_storage_mb()) / 1024)|round(1) }} GB
                                    {% else %}
                                        Max file size: 10GB | Unlimited storage
                                    {% endif %}
                                </p>
                            </div>
                            <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="upload-form">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <input type="file" name="file" multiple class="hidden" id="file-input" accept="*/*">
                                <button type="button" onclick="document.getElementById('file-input').click()"
                                        class="bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-plus mr-2"></i>
                                    Choose Files
                                </button>
                            </form>

                            <!-- Upload Progress Bar -->
                            <div id="upload-progress" class="hidden mt-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-300">Uploading...</span>
                                    <span id="upload-percentage" class="text-sm text-gray-300">0%</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div id="upload-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div class="flex items-center justify-between mt-2">
                                    <span id="upload-speed" class="text-xs text-gray-400">0 MB/s</span>
                                    <span id="upload-eta" class="text-xs text-gray-400">--:--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files List -->
            <div class="mt-8 bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <i class="fas fa-list text-blue-400 mr-2"></i>
                            Your Files
                        </h3>
                        <div class="flex items-center space-x-2">
                            <button class="text-gray-400 hover:text-white p-2" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white p-2" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    {% if files %}
                        <div class="space-y-3">
                            {% for file in files %}
                            <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg hover:bg-gray-650 transition-colors">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-600 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                                        {% set ext = file.filename.split('.')[-1].lower() %}
                                        {% if ext in ['jpg', 'jpeg', 'png', 'gif', 'webp'] %}
                                            <i class="fas fa-image text-blue-400"></i>
                                        {% elif ext in ['pdf'] %}
                                            <i class="fas fa-file-pdf text-red-400"></i>
                                        {% elif ext in ['doc', 'docx'] %}
                                            <i class="fas fa-file-word text-blue-400"></i>
                                        {% elif ext in ['zip', 'rar', '7z'] %}
                                            <i class="fas fa-file-archive text-yellow-400"></i>
                                        {% else %}
                                            <i class="fas fa-file text-gray-400"></i>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h4 class="text-white font-medium">{{ file.filename }}</h4>
                                        <p class="text-gray-400 text-sm">
                                            {{ "%.2f"|format(file.file_size / 1024 / 1024) }} MB • 
                                            {{ file.upload_date.strftime('%Y-%m-%d %H:%M') }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ url_for('download_file', file_id=file.file_id) }}" 
                                       class="text-blue-400 hover:text-blue-300 p-2" title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button onclick="copyLink('{{ url_for('download_file', file_id=file.file_id, _external=True) }}')" 
                                            class="text-green-400 hover:text-green-300 p-2" title="Copy Link">
                                        <i class="fas fa-link"></i>
                                    </button>
                                    <a href="{{ url_for('delete_file', file_id=file.file_id) }}" 
                                       onclick="return confirm('Are you sure you want to delete this file?')"
                                       class="text-red-400 hover:text-red-300 p-2" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-12">
                            <i class="fas fa-folder-open text-gray-600 text-6xl mb-4"></i>
                            <h4 class="text-xl font-medium text-gray-400 mb-2">No files uploaded yet</h4>
                            <p class="text-gray-500 mb-6">Start by uploading your first file</p>
                            <button onclick="document.getElementById('file-input').click()" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                                <i class="fas fa-upload mr-2"></i>
                                Upload Your First File
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-3">
                    <button onclick="document.getElementById('file-input').click()" 
                            class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-upload text-green-400 mr-3"></i>
                        <span class="text-white">Upload Files</span>
                    </button>
                    <a href="{{ url_for('dashboard') }}" 
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-home text-blue-400 mr-3"></i>
                        <span class="text-white">Back to Dashboard</span>
                    </a>
                </div>
            </div>

            <!-- Storage Info -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                        Storage Info
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Used Space</span>
                        <span class="text-white font-medium">{{ current_user.get_storage_used_formatted() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Available</span>
                        <span class="text-white font-medium">
                            {% if current_user.get_storage_limit_mb_effective() > 0 %}
                                {{ ((current_user.get_storage_limit_mb_effective() - current_user.get_used_storage_mb()) / 1024)|round(1) }} GB
                            {% else %}
                                Unlimited
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Total Files</span>
                        <span class="text-white font-medium">{{ files|length }}</span>
                    </div>
                    
                    {% if not current_user.is_lux_active() and current_user.get_storage_limit_mb_effective() > 0 %}
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <a href="{{ url_for('lux_dashboard') }}" 
                           class="w-full flex items-center justify-center p-3 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 rounded-lg transition-all">
                            <i class="fas fa-crown text-white mr-2"></i>
                            <span class="text-white font-medium">Upgrade Storage</span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyLink(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50';
        toast.textContent = 'Link copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

// File upload handling with progress
document.getElementById('file-input').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        // Check file size (10GB limit)
        for (let file of e.target.files) {
            if (file.size > 10 * 1024 * 1024 * 1024) {
                showErrorMessage(`File "${file.name}" is too large. Maximum file size is 10GB.`);
                e.target.value = ''; // Clear the input
                return;
            }
        }
        uploadFilesWithProgress(e.target.files);
    }
});

function uploadFilesWithProgress(files) {
    // Show progress bar and update upload area
    const progressDiv = document.getElementById('upload-progress');
    const progressBar = document.getElementById('upload-progress-bar');
    const progressPercentage = document.getElementById('upload-percentage');
    const uploadSpeed = document.getElementById('upload-speed');
    const uploadEta = document.getElementById('upload-eta');
    const uploadArea = document.getElementById('upload-area');

    progressDiv.classList.remove('hidden');

    // Update upload area to show uploading state
    uploadArea.style.borderColor = '#3B82F6';
    uploadArea.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';

    // Disable file input during upload
    document.getElementById('file-input').disabled = true;

    let totalFiles = files.length;
    let completedFiles = 0;
    let totalSize = 0;
    let uploadedSize = 0;

    // Calculate total size
    for (let file of files) {
        totalSize += file.size;
    }

    const startTime = Date.now();

    // Upload files one by one
    function uploadNextFile(index) {
        if (index >= files.length) {
            // All files uploaded successfully
            progressBar.style.width = '100%';
            progressPercentage.textContent = '100%';

            // Reset upload area
            uploadArea.style.borderColor = '';
            uploadArea.style.backgroundColor = '';
            document.getElementById('file-input').disabled = false;
            document.getElementById('file-input').value = ''; // Clear file input

            // Show success message
            showSuccessMessage(`Successfully uploaded ${totalFiles} file${totalFiles > 1 ? 's' : ''}!`);

            // Hide progress bar after 2 seconds and reload
            setTimeout(() => {
                progressDiv.classList.add('hidden');
                window.location.reload();
            }, 2000);
            return;
        }

        const file = files[index];
        const formData = new FormData();
        const csrfToken = document.querySelector('input[name="csrf_token"]').value;

        formData.append('csrf_token', csrfToken);
        formData.append('file', file);
        formData.append('public', 'false'); // API expects 'true' or 'false'

        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const currentFileProgress = e.loaded;
                const currentTotalUploaded = uploadedSize + currentFileProgress;
                const percentComplete = (currentTotalUploaded / totalSize) * 100;

                progressBar.style.width = percentComplete + '%';
                progressPercentage.textContent = Math.round(percentComplete) + '%';

                // Calculate upload speed and ETA
                const elapsed = (Date.now() - startTime) / 1000; // seconds
                const speed = currentTotalUploaded / elapsed; // bytes per second
                const speedMB = speed / (1024 * 1024); // MB per second
                const remaining = (totalSize - currentTotalUploaded) / speed; // seconds remaining

                uploadSpeed.textContent = speedMB.toFixed(1) + ' MB/s';

                if (remaining < 60) {
                    uploadEta.textContent = Math.round(remaining) + 's';
                } else {
                    const minutes = Math.floor(remaining / 60);
                    const seconds = Math.round(remaining % 60);
                    uploadEta.textContent = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
                }
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 201) {
                // File uploaded successfully (API returns 201 for created)
                uploadedSize += file.size;
                completedFiles++;

                // Upload next file
                uploadNextFile(index + 1);
            } else {
                // Error uploading this file
                progressDiv.classList.add('hidden');
                uploadArea.style.borderColor = '';
                uploadArea.style.backgroundColor = '';
                document.getElementById('file-input').disabled = false;

                // Try to parse error message from response
                let errorMessage = `Failed to upload ${file.name}. Please try again.`;
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.error) {
                        errorMessage = `Failed to upload ${file.name}: ${response.error}`;
                    }
                } catch (e) {
                    // Use default error message
                }

                showErrorMessage(errorMessage);
            }
        });

        xhr.addEventListener('error', function() {
            progressDiv.classList.add('hidden');
            uploadArea.style.borderColor = '';
            uploadArea.style.backgroundColor = '';
            document.getElementById('file-input').disabled = false;
            showErrorMessage(`Failed to upload ${file.name}. Please check your connection and try again.`);
        });

        xhr.open('POST', '/files/upload/ajax');
        xhr.send(formData);
    }

    // Start uploading the first file
    uploadNextFile(0);
}

// Drag and drop functionality
const uploadArea = document.getElementById('upload-area');
const fileInput = document.getElementById('file-input');
const uploadForm = document.getElementById('upload-form');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, preventDefaults, false);
    document.body.addEventListener(eventName, preventDefaults, false);
});

['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, unhighlight, false);
});

uploadArea.addEventListener('drop', handleDrop, false);

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    uploadArea.classList.add('border-green-400', 'bg-green-900', 'bg-opacity-10');
}

function unhighlight(e) {
    uploadArea.classList.remove('border-green-400', 'bg-green-900', 'bg-opacity-10');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    // Check file size (10GB limit)
    for (let file of files) {
        if (file.size > 10 * 1024 * 1024 * 1024) {
            showErrorMessage(`File "${file.name}" is too large. Maximum file size is 10GB.`);
            return;
        }
    }

    uploadFilesWithProgress(files);
}

// Notification functions
function showSuccessMessage(message) {
    showNotification(message, 'success');
}

function showErrorMessage(message) {
    showNotification(message, 'error');
}

function showNotification(message, type) {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.upload-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `upload-notification fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600' : 'bg-red-600'
    } text-white max-w-sm`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-3"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    notification.style.transform = 'translateX(100%)';
    notification.style.transition = 'transform 0.3s ease-out';
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}
</script>
{% endblock %}
