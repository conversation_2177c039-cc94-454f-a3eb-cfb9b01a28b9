{% extends "base.html" %}
{% block title %}Link Error - LXND{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-20">
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-8 text-center">
        <div class="w-16 h-16 bg-red-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
        </div>
        
        <h1 class="text-2xl font-bold text-white mb-2">{{ message or 'Link Error' }}</h1>
        
        {% if message == 'Link Disabled' %}
            <p class="text-gray-400 mb-6">This link has been disabled by the owner.</p>
        {% elif message == 'Link Expired' %}
            <p class="text-gray-400 mb-6">This link has expired and is no longer available.</p>
        {% else %}
            <p class="text-gray-400 mb-6">The link you're looking for is not available.</p>
        {% endif %}

        <div class="space-y-3">
            <a href="{{ url_for('shorten_url_page') }}" 
               class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 rounded-lg transition-colors">
                <i class="fas fa-link mr-2"></i>
                Create Short Link
            </a>
            
            <a href="{{ url_for('index') }}" 
               class="block w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 rounded-lg transition-colors">
                <i class="fas fa-home mr-2"></i>
                Go Home
            </a>
        </div>
    </div>
</div>
{% endblock %}
