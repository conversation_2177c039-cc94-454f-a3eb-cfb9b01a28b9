{% extends "base.html" %}
{% block title %}Password Required - LXND{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-20">
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-8">
        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-yellow-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-lock text-yellow-400 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">Password Required</h1>
            <p class="text-gray-400">This link is password protected</p>
        </div>

        {% if url.title %}
        <div class="mb-4 p-3 bg-gray-700 rounded">
            <h3 class="text-white font-medium">{{ url.title }}</h3>
            {% if url.description %}
                <p class="text-gray-400 text-sm mt-1">{{ url.description }}</p>
            {% endif %}
        </div>
        {% endif %}

        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="mb-6">
                <label for="{{ form.password.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                    Enter Password
                </label>
                {{ form.password(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", autofocus=true) }}
                {% if form.password.errors %}
                    <div class="mt-2 text-sm text-red-400">
                        {% for error in form.password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="flex justify-center">
                {{ form.submit(class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 rounded-lg transition-colors") }}
            </div>
        </form>

        <div class="mt-6 text-center">
            <a href="{{ url_for('shorten_url_page') }}" class="text-gray-400 hover:text-white text-sm">
                <i class="fas fa-arrow-left mr-1"></i>
                Create your own short link
            </a>
        </div>
    </div>
</div>
{% endblock %}
