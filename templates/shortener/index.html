{% extends "base.html" %}
{% block title %}Link Shortener - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Link Shortener Header -->
    <div class="bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl p-8 shadow-2xl border border-blue-500/20">
        <div class="text-center">
            <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                <i class="fas fa-link text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-3">
                Link Shortener
            </h1>
            <p class="text-blue-100 text-lg">Create short, memorable links with advanced analytics</p>
        </div>
    </div>

    <!-- URL Shortener Form -->
    <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8">
        <form method="POST" action="{{ url_for('create_short_url') }}">
            {{ form.hidden_tag() }}
            
            <!-- URL Input -->
            <div class="mb-4">
                <label for="{{ form.original_url.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-globe mr-2"></i>URL to Shorten
                </label>
                {{ form.original_url(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent") }}
                {% if form.original_url.errors %}
                    <div class="mt-1 text-sm text-red-400">
                        {% for error in form.original_url.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Advanced Options (Collapsible) -->
            <div class="mb-4">
                <button type="button" onclick="toggleAdvanced()" class="text-blue-400 hover:text-blue-300 text-sm">
                    <i class="fas fa-cog mr-1"></i>Advanced Options
                    <i id="advancedIcon" class="fas fa-chevron-down ml-1"></i>
                </button>
            </div>

            <div id="advancedOptions" class="hidden space-y-4 mb-6">
                <!-- Custom Code -->
                <div>
                    <label for="{{ form.custom_code.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Custom Short Code
                    </label>
                    {{ form.custom_code(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    <p class="text-xs text-gray-400 mt-1">Leave empty for auto-generated code</p>
                </div>

                <!-- Title -->
                <div>
                    <label for="{{ form.title.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Title
                    </label>
                    {{ form.title(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                </div>

                <!-- Description -->
                <div>
                    <label for="{{ form.description.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Description
                    </label>
                    {{ form.description(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                </div>

                <!-- Expiration -->
                <div>
                    <label for="{{ form.expires_at.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Expiration Date
                    </label>
                    {{ form.expires_at(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    <p class="text-xs text-gray-400 mt-1">Leave empty for no expiration</p>
                </div>

                <!-- Password -->
                <div>
                    <label for="{{ form.password.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Password Protection
                    </label>
                    {{ form.password(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    <p class="text-xs text-gray-400 mt-1">Optional password protection</p>
                </div>

                <!-- Visibility -->
                <div>
                    <label for="{{ form.is_public.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Visibility
                    </label>
                    {{ form.is_public(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-center">
                {{ form.submit(class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-8 py-3 rounded-lg transition-colors") }}
            </div>
        </form>
    </div>

    <!-- Recent URLs (for logged in users) -->
    {% if current_user.is_authenticated and recent_urls %}
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-history text-blue-400 mr-2"></i>
                Your Recent Links
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for url in recent_urls %}
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <a href="{{ url.get_short_url(request) }}" target="_blank" 
                                   class="text-blue-400 hover:text-blue-300 font-medium">
                                    {{ url.get_short_url(request) }}
                                </a>
                                <button onclick="copyToClipboard('{{ url.get_short_url(request) }}')" 
                                        class="text-gray-400 hover:text-white">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <p class="text-gray-300 text-sm truncate">{{ url.original_url }}</p>
                            {% if url.title %}
                                <p class="text-gray-400 text-xs mt-1">{{ url.title }}</p>
                            {% endif %}
                        </div>
                        <div class="text-right">
                            <div class="text-white font-medium">{{ url.click_count }}</div>
                            <div class="text-gray-400 text-xs">clicks</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Features Section -->
    <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
            <div class="w-12 h-12 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-chart-line text-blue-400 text-xl"></i>
            </div>
            <h3 class="text-white font-semibold mb-2">Analytics</h3>
            <p class="text-gray-400 text-sm">Track clicks, devices, and referrers</p>
        </div>
        
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
            <div class="w-12 h-12 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-lock text-green-400 text-xl"></i>
            </div>
            <h3 class="text-white font-semibold mb-2">Password Protection</h3>
            <p class="text-gray-400 text-sm">Secure your links with passwords</p>
        </div>
        
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
            <div class="w-12 h-12 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-clock text-purple-400 text-xl"></i>
            </div>
            <h3 class="text-white font-semibold mb-2">Expiration</h3>
            <p class="text-gray-400 text-sm">Set automatic link expiration</p>
        </div>
    </div>
</div>

<script>
function toggleAdvanced() {
    const options = document.getElementById('advancedOptions');
    const icon = document.getElementById('advancedIcon');
    
    if (options.classList.contains('hidden')) {
        options.classList.remove('hidden');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        options.classList.add('hidden');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50';
        toast.textContent = 'Copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}
</script>
{% endblock %}
