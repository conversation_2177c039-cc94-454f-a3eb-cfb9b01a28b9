{% extends "base.html" %}
{% block title %}{{ project.name }} - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Project Header -->
    <div class="bg-gradient-to-br from-cyan-600 via-blue-600 to-indigo-600 rounded-2xl p-8 shadow-2xl border border-cyan-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-folder-open text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            {{ project.name }}
                        </h1>
                        <p class="text-cyan-100 text-lg">{{ project.description or 'No description provided' }}</p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                    <div class="text-white text-sm opacity-75 uppercase tracking-wide mb-1">
                        Project ID
                    </div>
                    <div class="text-white text-xl font-mono font-bold">
                        {{ project.project_id }}
                    </div>
                    <div class="text-cyan-200 text-sm">
                        Created {{ project.created_at.strftime('%b %Y') }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Licenses -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-blue-500/20 border border-blue-400/30">
                        <i class="fas fa-key text-blue-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Total Licenses</p>
                        <p class="text-3xl font-bold text-white">{{ licenses|length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Licenses -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-green-500/20 hover:border-green-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-green-500/20 border border-green-400/30">
                        <i class="fas fa-check-circle text-green-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Active Licenses</p>
                        <p class="text-3xl font-bold text-white">
                            {% set active_count = licenses|selectattr('is_active')|list|length %}
                            {{ active_count }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Used Licenses -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-orange-500/20 hover:border-orange-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-orange-500/20 border border-orange-400/30">
                        <i class="fas fa-user-check text-orange-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Used Licenses</p>
                        <p class="text-3xl font-bold text-white">
                            {% set used_count = licenses|selectattr('used_by')|list|length %}
                            {{ used_count }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Files -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-purple-500/20 border border-purple-400/30">
                        <i class="fas fa-file text-purple-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Project Files</p>
                        <p class="text-3xl font-bold text-white">{{ project.files|length }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- License Management -->
        <div class="lg:col-span-2">
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl">
                <div class="px-8 py-6 border-b border-gray-700/50">
                    <div class="flex items-center justify-between">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-key text-blue-400 mr-3"></i>
                            License Keys
                        </h3>
                        <div class="flex space-x-3">
                            <button type="button" onclick="openLicenseModal()"
                                    class="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30">
                                <i class="fas fa-plus mr-2"></i>
                                Generate Key
                            </button>
                            <button type="button" onclick="openBulkModal()"
                                    class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-green-400/30">
                                <i class="fas fa-layer-group mr-2"></i>
                                Bulk Create
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-8">
                    {% if licenses %}
                        <!-- Export Options -->
                        <div class="mb-6 flex flex-wrap gap-3">
                            <a href="{{ url_for('download_licenses', project_id=project.project_id, format='txt') }}" 
                               class="bg-purple-600/80 hover:bg-purple-600 text-white px-4 py-2 rounded-xl transition-all duration-300 border border-purple-500/30">
                                <i class="fas fa-download mr-2"></i>Download TXT
                            </a>
                            <a href="{{ url_for('download_licenses', project_id=project.project_id, format='csv') }}" 
                               class="bg-indigo-600/80 hover:bg-indigo-600 text-white px-4 py-2 rounded-xl transition-all duration-300 border border-indigo-500/30">
                                <i class="fas fa-file-csv mr-2"></i>Download CSV
                            </a>
                        </div>

                        <!-- License List -->
                        <div class="space-y-4">
                            {% for license in licenses %}
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 hover:bg-white/10 transition-all duration-300 border border-gray-600/30 hover:border-blue-500/40">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                            <i class="fas fa-key text-white"></i>
                                        </div>
                                        <div>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-white font-mono text-lg font-bold">{{ license.license_key }}</span>
                                                <button onclick="copyToClipboard('{{ license.license_key }}')" 
                                                        class="text-gray-400 hover:text-white transition-colors">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                            <div class="flex items-center space-x-4 mt-1">
                                                {% if license.is_active %}
                                                    <span class="bg-green-500/20 text-green-300 px-2 py-1 rounded-lg text-sm">
                                                        <i class="fas fa-check-circle mr-1"></i>Active
                                                    </span>
                                                {% else %}
                                                    <span class="bg-red-500/20 text-red-300 px-2 py-1 rounded-lg text-sm">
                                                        <i class="fas fa-times-circle mr-1"></i>Expired
                                                    </span>
                                                {% endif %}
                                                
                                                {% if license.used_by %}
                                                    <span class="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-lg text-sm">
                                                        <i class="fas fa-user mr-1"></i>Used by {{ license.used_by }}
                                                    </span>
                                                {% else %}
                                                    <span class="bg-gray-500/20 text-gray-300 px-2 py-1 rounded-lg text-sm">
                                                        <i class="fas fa-clock mr-1"></i>Unused
                                                    </span>
                                                {% endif %}
                                            </div>
                                            <p class="text-gray-400 text-sm mt-1">
                                                Created: {{ license.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                {% if license.expires_at %}
                                                    | Expires: {{ license.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                                {% else %}
                                                    | Never expires
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="deleteLicense('{{ license.license_key }}')" 
                                                class="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/20 transition-all duration-300">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-16">
                            <div class="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-blue-400/30">
                                <i class="fas fa-key text-blue-300 text-3xl"></i>
                            </div>
                            <h4 class="text-2xl font-bold text-gray-300 mb-3">No License Keys</h4>
                            <p class="text-gray-400 mb-8 text-lg">Generate your first license key to get started</p>
                            <button type="button" onclick="openLicenseModal()"
                                    class="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30">
                                <i class="fas fa-plus mr-2"></i>
                                Generate First License
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <button type="button" onclick="openLicenseModal()"
                            class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-blue-500/40">
                        <i class="fas fa-plus text-blue-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Generate License</span>
                    </button>
                    <button type="button" onclick="openBulkModal()" 
                            class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-green-500/40">
                        <i class="fas fa-layer-group text-green-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Bulk Create</span>
                    </button>
                    <a href="{{ url_for('files_dashboard') }}" 
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-purple-500/40">
                        <i class="fas fa-upload text-purple-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Upload Files</span>
                    </a>
                    <a href="{{ url_for('dashboard') }}" 
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-gray-500/40">
                        <i class="fas fa-arrow-left text-gray-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Back to Dashboard</span>
                    </a>
                </div>
            </div>

            <!-- Project Info -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-info-circle text-cyan-400 mr-2"></i>
                        Project Info
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Project ID</span>
                        <span class="text-white font-mono text-sm">{{ project.project_id }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Created</span>
                        <span class="text-white">{{ project.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Total Licenses</span>
                        <span class="text-white font-bold">{{ licenses|length }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Project Files</span>
                        <span class="text-white font-bold">{{ project.files|length }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals and Scripts will be added in the next part -->
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        showNotification('License key copied to clipboard!', 'success');
    });
}

function showNotification(message, type) {
    // Simple notification system
    const notification = document.createElement('div');
    let bgColor = 'bg-red-600';
    if (type === 'success') bgColor = 'bg-green-600';
    else if (type === 'info') bgColor = 'bg-blue-600';

    notification.className = `fixed top-4 right-4 p-4 rounded-xl text-white z-50 ${bgColor}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, type === 'info' ? 2000 : 3000);
}

function openLicenseModal() {
    document.getElementById('licenseModal').classList.remove('hidden');
    document.getElementById('licenseModal').classList.add('flex');
}

function closeLicenseModal() {
    document.getElementById('licenseModal').classList.add('hidden');
    document.getElementById('licenseModal').classList.remove('flex');
    document.getElementById('licenseForm').reset();
}

function openBulkModal() {
    document.getElementById('bulkModal').classList.remove('hidden');
    document.getElementById('bulkModal').classList.add('flex');
}

function closeBulkModal() {
    document.getElementById('bulkModal').classList.add('hidden');
    document.getElementById('bulkModal').classList.remove('flex');
    document.getElementById('bulkForm').reset();
}

function toggleDurationInput() {
    const select = document.getElementById('durationType');
    const input = document.getElementById('durationInput');

    if (select.value === 'unlimited') {
        input.classList.add('hidden');
    } else {
        input.classList.remove('hidden');
    }
}

function toggleBulkDurationInput() {
    const select = document.getElementById('bulkDurationType');
    const input = document.getElementById('bulkDurationInput');

    if (select.value === 'unlimited') {
        input.classList.add('hidden');
    } else {
        input.classList.remove('hidden');
    }
}

function deleteLicense(licenseKey) {
    if (confirm('Are you sure you want to delete this license key?')) {
        // Delete functionality will be implemented
        alert('Delete license: ' + licenseKey);
    }
}

// Form handlers
document.getElementById('licenseForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // Calculate duration in days
    const durationType = formData.get('duration_type');
    const durationValue = parseInt(formData.get('duration_value')) || 0;
    let durationDays = null;

    if (durationType !== 'unlimited' && durationValue > 0) {
        if (durationType === 'days') {
            durationDays = durationValue;
        } else if (durationType === 'months') {
            durationDays = durationValue * 30;
        } else if (durationType === 'years') {
            durationDays = durationValue * 365;
        }
    }

    // Prepare final form data
    const finalFormData = new FormData();
    finalFormData.append('csrf_token', formData.get('csrf_token'));
    if (formData.get('custom_key')) {
        finalFormData.append('custom_key', formData.get('custom_key'));
    }
    if (durationDays) {
        finalFormData.append('duration_days', durationDays);
    }

    fetch('/license/create_license/{{ project.project_id }}', {
        method: 'POST',
        body: finalFormData
    })
    .then(response => {
        if (response.ok) {
            showNotification('License generated successfully!', 'success');
            closeLicenseModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Error generating license', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error generating license', 'error');
    });
});

document.getElementById('bulkForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const count = parseInt(formData.get('count'));
    const keyPrefix = formData.get('key_prefix');

    if (count > 50) {
        showNotification('Maximum 50 licenses per bulk creation', 'error');
        return;
    }

    // Calculate duration in days
    const durationType = formData.get('duration_type');
    const durationValue = parseInt(formData.get('duration_value')) || 0;
    let durationDays = null;

    if (durationType !== 'unlimited' && durationValue > 0) {
        if (durationType === 'days') {
            durationDays = durationValue;
        } else if (durationType === 'months') {
            durationDays = durationValue * 30;
        } else if (durationType === 'years') {
            durationDays = durationValue * 365;
        }
    }

    closeBulkModal();
    createBulkLicenses(count, keyPrefix, durationDays);
});

async function createBulkLicenses(count, keyPrefix, durationDays) {
    let created = 0;
    let errors = 0;

    showNotification(`Creating ${count} licenses...`, 'info');

    for (let i = 0; i < count; i++) {
        try {
            const formData = new FormData();
            formData.append('csrf_token', '{{ csrf_token() }}');

            // Generate custom key if prefix provided
            if (keyPrefix) {
                const keyNumber = String(i + 1).padStart(3, '0');
                formData.append('custom_key', `${keyPrefix}${keyNumber}`);
            }

            if (durationDays) {
                formData.append('duration_days', durationDays);
            }

            const response = await fetch('/license/create_license/{{ project.project_id }}', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                created++;
            } else {
                errors++;
            }
        } catch (error) {
            errors++;
        }

        // Small delay to prevent overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (created > 0) {
        showNotification(`Created ${created} licenses successfully!`, 'success');
        setTimeout(() => location.reload(), 1000);
    } else {
        showNotification('Failed to create licenses', 'error');
    }
}
</script>

<!-- License Generation Modal -->
<div id="licenseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Generate License Key</h3>
            <button onclick="closeLicenseModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="licenseForm" class="space-y-4">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Custom License Key (optional)</label>
                <input type="text" name="custom_key" id="customKey" placeholder="e.g. key123 (leave empty for auto-generated)"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-400 mt-1">If empty, a secure random key will be generated</p>
            </div>

            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration</label>
                <select name="duration_type" id="durationType" onchange="toggleDurationInput()"
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="unlimited">Unlimited</option>
                    <option value="days">Days</option>
                    <option value="months">Months</option>
                    <option value="years">Years</option>
                </select>
            </div>

            <div id="durationInput" class="hidden">
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration Value</label>
                <input type="number" name="duration_value" min="1" max="3650" value="30"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeLicenseModal()"
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Generate
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bulk License Creation Modal -->
<div id="bulkModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Bulk Create Licenses</h3>
            <button onclick="closeBulkModal()" class="text-gray-400 hover:text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="bulkForm" class="space-y-4">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Number of Licenses</label>
                <input type="number" name="count" min="1" max="50" value="5"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500">
                <p class="text-xs text-gray-400 mt-1">Maximum 50 licenses per bulk creation</p>
            </div>

            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Key Prefix (optional)</label>
                <input type="text" name="key_prefix" placeholder="e.g. BULK- (will add numbers: BULK-001, BULK-002...)"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500">
                <p class="text-xs text-gray-400 mt-1">If empty, random keys will be generated</p>
            </div>

            <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration</label>
                <select name="duration_type" id="bulkDurationType" onchange="toggleBulkDurationInput()"
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-500">
                    <option value="unlimited">Unlimited</option>
                    <option value="days">Days</option>
                    <option value="months">Months</option>
                    <option value="years">Years</option>
                </select>
            </div>

            <div id="bulkDurationInput" class="hidden">
                <label class="block text-gray-300 text-sm font-medium mb-2">Duration Value</label>
                <input type="number" name="duration_value" min="1" max="3650" value="30"
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500">
            </div>

            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeBulkModal()"
                        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300">
                    Create Bulk
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
