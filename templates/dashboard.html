{% extends "base.html" %}
{% block title %}Dashboard - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    Welcome back, {{ current_user.username }}!
                    {% if current_user.is_lux_active() %}
                        <i class="fas fa-crown text-yellow-400 ml-2" title="Lux Premium User"></i>
                    {% endif %}
                </h1>
                <p class="text-blue-100">Your command center for projects, files, and services</p>
            </div>
            <div class="text-right">
                <div class="text-white text-sm opacity-75">
                    Account Status
                </div>
                <div class="text-white text-lg font-semibold">
                    {% if current_user.is_admin %}
                        Administrator
                    {% elif current_user.is_lux_active() %}
                        Lux Premium
                    {% else %}
                        Standard User
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Websites Card -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-gray-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                        <i class="fas fa-globe text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Websites</p>
                        <p class="text-2xl font-bold text-white">
                            {{ stats.websites if stats else 0 }}
                        </p>
                        <p class="text-xs text-gray-500 mt-1">Active hosting</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects Card -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-purple-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-600 bg-opacity-20">
                        <i class="fas fa-folder text-purple-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Projects</p>
                        <p class="text-2xl font-bold text-white">
                            {{ current_user.projects|length }}
                            {% if current_user.get_max_projects_limit() > 0 %}
                                <span class="text-lg text-gray-500">/ {{ current_user.get_max_projects_limit() }}</span>
                            {% else %}
                                <span class="text-lg text-gray-500">/ ∞</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                {% if current_user.get_max_projects_limit() > 0 %}
                    {% set project_percent = (current_user.projects|length / current_user.get_max_projects_limit() * 100) %}
                    <div class="mt-4">
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="h-2 rounded-full {% if project_percent >= 90 %}bg-red-500{% elif project_percent >= 70 %}bg-yellow-500{% else %}bg-purple-500{% endif %}"
                                 style="width: {{ [project_percent, 100]|min }}%"></div>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">{{ "%.0f"|format(project_percent) }}% used</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Storage Card -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-blue-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-600 bg-opacity-20">
                        <i class="fas fa-hdd text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Storage</p>
                        <p class="text-2xl font-bold text-white">
                            {{ current_user.get_storage_used_formatted() }}
                            <span class="text-lg text-gray-500">
                                {% if current_user.get_storage_limit_mb_effective() > 0 %}
                                    / {{ (current_user.get_storage_limit_mb_effective() / 1024)|round(1) }} GB
                                {% else %}
                                    / ∞
                                {% endif %}
                            </span>
                        </p>
                    </div>
                </div>
                {% if current_user.get_used_storage_mb() > 0 %}
                    {% set storage_percent = current_user.get_storage_percent() %}
                    <div class="mt-4">
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="h-2 rounded-full {% if storage_percent >= 90 %}bg-red-500{% elif storage_percent >= 70 %}bg-yellow-500{% else %}bg-blue-500{% endif %}"
                                 style="width: {{ storage_percent }}%"></div>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">{{ storage_percent }}% used</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Short URLs Card -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-green-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-600 bg-opacity-20">
                        <i class="fas fa-link text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Short URLs</p>
                        <p class="text-2xl font-bold text-white">
                            {{ ShortenedUrl.query.filter_by(user_id=current_user.id).count() if ShortenedUrl else 0 }}
                            {% if current_user.get_shortener_limit() > 0 %}
                                <span class="text-lg text-gray-500">/ {{ current_user.get_shortener_limit() }}</span>
                            {% else %}
                                <span class="text-lg text-gray-500">/ ∞</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                {% if current_user.get_shortener_limit() > 0 %}
                    {% set url_count = ShortenedUrl.query.filter_by(user_id=current_user.id).count() if ShortenedUrl else 0 %}
                    {% set url_percent = (url_count / current_user.get_shortener_limit() * 100) %}
                    <div class="mt-4">
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="h-2 rounded-full {% if url_percent >= 90 %}bg-red-500{% elif url_percent >= 70 %}bg-yellow-500{% else %}bg-green-500{% endif %}"
                                 style="width: {{ [url_percent, 100]|min }}%"></div>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">{{ "%.0f"|format(url_percent) }}% used</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Email Accounts Card -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 hover:border-orange-500 transition-colors">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-600 bg-opacity-20">
                        <i class="fas fa-envelope text-orange-400 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-400">Email Accounts</p>
                        <p class="text-2xl font-bold text-white">
                            {{ current_user.email_accounts|length if current_user.email_accounts else 0 }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Recent Projects -->
        <div class="lg:col-span-2">
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <i class="fas fa-folder text-purple-400 mr-2"></i>
                            Recent Projects
                        </h3>
                        <a href="{{ url_for('create_project') }}" 
                           class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                            <i class="fas fa-plus mr-1"></i>
                            New Project
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    {% if current_user.projects %}
                        <div class="space-y-4">
                            {% for project in current_user.projects[:5] %}
                            <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg hover:bg-gray-650 transition-colors">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-600 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-folder text-purple-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-white font-medium">{{ project.name }}</h4>
                                        <p class="text-gray-400 text-sm">{{ project.licenses|length }} licenses</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-400">{{ project.created_at.strftime('%m/%d') }}</span>
                                    <a href="{{ url_for('project_detail', project_id=project.project_id) }}" 
                                       class="text-purple-400 hover:text-purple-300">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if current_user.projects|length > 5 %}
                        <div class="mt-4 text-center">
                            <a href="{{ url_for('dashboard') }}" class="text-purple-400 hover:text-purple-300 text-sm">
                                View all {{ current_user.projects|length }} projects
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-8">
                            <i class="fas fa-folder-open text-gray-600 text-4xl mb-4"></i>
                            <h4 class="text-lg font-medium text-gray-400 mb-2">No projects yet</h4>
                            <p class="text-gray-500 mb-4">Create your first project to get started</p>
                            <a href="{{ url_for('create_project') }}" 
                               class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
                                Create Project
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{{ url_for('create_project') }}" 
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-plus text-purple-400 mr-3"></i>
                        <span class="text-white">New Project</span>
                    </a>
                    <a href="{{ url_for('files_dashboard') }}" 
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-upload text-blue-400 mr-3"></i>
                        <span class="text-white">Upload Files</span>
                    </a>
                    <a href="{{ url_for('shorten_url_page') }}" 
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-link text-green-400 mr-3"></i>
                        <span class="text-white">Shorten URL</span>
                    </a>
                    <a href="{{ url_for('user_email_dashboard') }}"
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-envelope text-orange-400 mr-3"></i>
                        <span class="text-white">Email Dashboard</span>
                    </a>
                    <a href="{{ url_for('websites') }}"
                       class="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-globe text-cyan-400 mr-3"></i>
                        <span class="text-white">My Websites</span>
                    </a>
                    {% if not current_user.is_lux_active() %}
                    <a href="{{ url_for('lux_dashboard') }}" 
                       class="w-full flex items-center p-3 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 rounded-lg transition-all">
                        <i class="fas fa-crown text-white mr-3"></i>
                        <span class="text-white font-medium">Upgrade to Lux</span>
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Account Status -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-user text-blue-400 mr-2"></i>
                        Account Status
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Account Type</span>
                        <span class="text-white font-medium">
                            {% if current_user.is_admin %}
                                <i class="fas fa-shield-alt text-red-400 mr-1"></i>
                                Administrator
                            {% elif current_user.is_lux_active() %}
                                <i class="fas fa-crown text-yellow-400 mr-1"></i>
                                Lux Premium
                            {% else %}
                                Standard
                            {% endif %}
                        </span>
                    </div>
                    
                    {% if current_user.is_lux_active() and current_user.lux_expires_at %}
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Lux Expires</span>
                        <span class="text-white font-medium">
                            {{ current_user.get_lux_days_remaining() }} days
                        </span>
                    </div>
                    {% endif %}
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Member Since</span>
                        <span class="text-white font-medium">
                            {{ current_user.created_at.strftime('%b %Y') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
