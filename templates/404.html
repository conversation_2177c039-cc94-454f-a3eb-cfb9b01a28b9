{% extends "base.html" %}
{% block title %}Page Not Found - LXND{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- 404 Icon -->
        <div class="w-32 h-32 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-8 shadow-2xl">
            <i class="fas fa-exclamation-triangle text-white text-4xl"></i>
        </div>

        <!-- Error Message -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8">
            <h1 class="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-pink-600 mb-4">
                404
            </h1>
            <h2 class="text-2xl font-bold text-white mb-4">
                Page Not Found
            </h2>
            <p class="text-gray-400 text-lg mb-8">
                The page you're looking for doesn't exist or has been moved.
            </p>

            <!-- Action Buttons -->
            <div class="space-y-4">
                <a href="{{ url_for('dashboard') }}"
                   class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-400/30 inline-block">
                    <i class="fas fa-home mr-2"></i>
                    Go to Dashboard
                </a>
                <button onclick="history.back()"
                        class="w-full bg-gray-600/80 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 border border-gray-500/30">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Go Back
                </button>
            </div>
        </div>

        <!-- Help Links -->
        <div class="grid grid-cols-2 gap-4 mt-8">
            <a href="{{ url_for('dashboard') }}"
               class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center hover:bg-white/10 transition-all duration-300">
                <i class="fas fa-home text-blue-400 text-xl mb-2"></i>
                <p class="text-white font-medium text-sm">Dashboard</p>
            </a>
            <a href="{{ url_for('user_email_dashboard') }}"
               class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center hover:bg-white/10 transition-all duration-300">
                <i class="fas fa-envelope text-purple-400 text-xl mb-2"></i>
                <p class="text-white font-medium text-sm">Email</p>
            </a>
        </div>
    </div>
</div>
{% endblock %}