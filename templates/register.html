{% extends "base.html" %}
{% block title %}Create Account - LXND{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <i class="fas fa-user-plus text-white text-2xl"></i>
            </div>
            <h2 class="text-4xl font-bold text-white mb-2">
                Join LXND
            </h2>
            <p class="text-gray-400 text-lg">Create your account and get started</p>
        </div>

        <!-- Registration Form -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8">
            <form method="POST" action="{{ url_for('register') }}" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Username Field -->
                <div>
                    <label for="{{ form.username.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-user mr-2 text-blue-400"></i>
                        Username
                    </label>
                    {{ form.username(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300", placeholder="Choose a username") }}
                    {% if form.username.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.username.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">3-20 characters, letters and numbers only</p>
                </div>

                <!-- Email Field -->
                <div>
                    <label for="{{ form.email.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-envelope mr-2 text-green-400"></i>
                        Email Address
                    </label>
                    {{ form.email(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300", placeholder="Enter your email") }}
                    {% if form.email.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.email.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    <label for="{{ form.password.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-lock mr-2 text-purple-400"></i>
                        Password
                    </label>
                    <div class="relative">
                        {{ form.password(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300", placeholder="Create a password", id="password-input") }}
                        <button type="button" onclick="togglePassword('password-input', 'password-toggle-icon')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.password.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="mt-2 text-xs text-gray-500">
                        <div class="grid grid-cols-2 gap-2">
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-400 mr-1" id="length-check" style="display: none;"></i>
                                <i class="fas fa-times text-red-400 mr-1" id="length-cross"></i>
                                <span>8+ characters</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-400 mr-1" id="special-check" style="display: none;"></i>
                                <i class="fas fa-times text-red-400 mr-1" id="special-cross"></i>
                                <span>Special character</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div>
                    <label for="{{ form.password2.id }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-lock mr-2 text-orange-400"></i>
                        Confirm Password
                    </label>
                    <div class="relative">
                        {{ form.password2(class="w-full px-4 py-3 bg-white/5 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300", placeholder="Confirm your password", id="confirm-password-input") }}
                        <button type="button" onclick="togglePassword('confirm-password-input', 'confirm-password-toggle-icon')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password2.errors %}
                        <div class="mt-2 text-sm text-red-400">
                            {% for error in form.password2.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Terms and Conditions -->
                <div class="flex items-start">
                    <input type="checkbox" id="terms" name="terms" required class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 mt-1">
                    <label for="terms" class="ml-2 text-sm text-gray-300">
                        I agree to the 
                        <a href="{{ url_for('terms_of_service') }}" class="text-blue-400 hover:text-blue-300 transition-colors" target="_blank">Terms of Service</a>
                        and 
                        <a href="{{ url_for('privacy_policy') }}" class="text-blue-400 hover:text-blue-300 transition-colors" target="_blank">Privacy Policy</a>
                    </label>
                </div>

                <!-- Submit Button -->
                <div>
                    {{ form.submit(class="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl border border-green-400/30") }}
                </div>
            </form>

            <!-- Divider -->
            <div class="mt-8 pt-6 border-t border-gray-700/50">
                <div class="text-center">
                    <p class="text-gray-400 text-sm">
                        Already have an account?
                        <a href="{{ url_for('login') }}" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                            Sign in here
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Benefits Preview -->
        <div class="grid grid-cols-3 gap-4 mt-8">
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-cloud text-blue-400 text-xl mb-2"></i>
                <p class="text-white font-medium text-xs">Cloud Storage</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-envelope text-green-400 text-xl mb-2"></i>
                <p class="text-white font-medium text-xs">Email Service</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 text-center">
                <i class="fas fa-link text-purple-400 text-xl mb-2"></i>
                <p class="text-white font-medium text-xs">URL Shortener</p>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password strength validation
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password-input');
    const lengthCheck = document.getElementById('length-check');
    const lengthCross = document.getElementById('length-cross');
    const specialCheck = document.getElementById('special-check');
    const specialCross = document.getElementById('special-cross');
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            
            // Check length
            if (password.length >= 8) {
                lengthCheck.style.display = 'inline';
                lengthCross.style.display = 'none';
            } else {
                lengthCheck.style.display = 'none';
                lengthCross.style.display = 'inline';
            }
            
            // Check special character
            const specialRegex = /[!@#$%^&*(),.?":{}|<>]/;
            if (specialRegex.test(password)) {
                specialCheck.style.display = 'inline';
                specialCross.style.display = 'none';
            } else {
                specialCheck.style.display = 'none';
                specialCross.style.display = 'inline';
            }
        });
    }
    
    // Auto-focus on username field
    const usernameField = document.getElementById('{{ form.username.id }}');
    if (usernameField) {
        usernameField.focus();
    }
});
</script>
{% endblock %}
