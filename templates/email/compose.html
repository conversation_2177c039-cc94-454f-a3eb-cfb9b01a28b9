{% extends "base.html" %}
{% block title %}Compose Email - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Compose Email Header -->
    <div class="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-8 shadow-2xl border border-emerald-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-edit text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            Compose Email
                        </h1>
                        <p class="text-emerald-100 text-lg">Create and send a new email message</p>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('user_email_dashboard') }}" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 border border-white/30">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Email
                </a>
            </div>
        </div>
    </div>

    {% if not email_accounts %}
        <!-- No Email Account -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-8 text-center">
            <i class="fas fa-exclamation-triangle text-yellow-400 text-4xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-400 mb-2">No Email Account</h3>
            <p class="text-gray-500 mb-6">You need an email account to compose messages.</p>
            <a href="{{ url_for('user_email_dashboard') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>Create Email Account
            </a>
        </div>
    {% else %}
        <!-- Compose Form -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <!-- From Account Selection -->
                <div class="mb-4">
                    <label for="{{ form.from_account.id }}" class="block text-sm font-medium text-gray-300 mb-2">From</label>
                    {{ form.from_account(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    {% if form.from_account.errors %}
                        <div class="mt-1 text-sm text-red-400">
                            {% for error in form.from_account.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- To -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">To</label>
                    {{ form.to(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    <p class="text-xs text-gray-400 mt-1">Separate multiple recipients with commas</p>
                    {% if form.to.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.to.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- CC -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">CC (Optional)</label>
                    {{ form.cc(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    {% if form.cc.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.cc.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- BCC -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">BCC (Optional)</label>
                    {{ form.bcc(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    {% if form.bcc.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.bcc.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Subject -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                    {{ form.subject(class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                    {% if form.subject.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.subject.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Message Body -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                    <textarea name="body" rows="12" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Type your message here..." required>{{ form.body.data or '' }}</textarea>
                    {% if form.body.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.body.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Attachments -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Attachments (Optional)</label>
                    <input type="file" name="attachments" multiple
                           class="w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                           onchange="showSelectedFiles(this)">
                    <p class="text-xs text-gray-400 mt-1">Maximum 25MB per attachment. You can select multiple files.</p>

                    <!-- Selected Files Display -->
                    <div id="selected-files" class="mt-2 space-y-1"></div>

                    {% if form.attachments.errors %}
                        <div class="text-red-400 text-sm mt-1">
                            {% for error in form.attachments.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>Send Email
                    </button>
                    <div class="flex space-x-3">
                        <button type="button" onclick="saveDraft()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>Save Draft
                        </button>
                        <a href="{{ url_for('user_email_dashboard') }}" class="text-gray-400 hover:text-gray-300 px-4 py-2">
                            Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Email Tips -->
        <div class="mt-8 bg-blue-900 bg-opacity-50 border border-blue-700 rounded-lg p-4">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-blue-400 mr-2 mt-0.5"></i>
                <div class="text-blue-200 text-sm">
                    <p><strong>Email Tips:</strong></p>
                    <ul class="mt-2 space-y-1">
                        <li>• Use clear, descriptive subject lines</li>
                        <li>• Separate multiple recipients with commas</li>
                        <li>• Keep attachments under 25MB total</li>
                        <li>• Use BCC for privacy when sending to multiple recipients</li>
                    </ul>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
function saveDraft() {
    alert('Draft save functionality would be implemented here');
}

function showSelectedFiles(input) {
    const container = document.getElementById('selected-files');
    container.innerHTML = '';

    if (input.files.length === 0) {
        return;
    }

    for (let i = 0; i < input.files.length; i++) {
        const file = input.files[i];
        const fileSize = (file.size / (1024 * 1024)).toFixed(2);

        const fileDiv = document.createElement('div');
        fileDiv.className = 'flex items-center justify-between bg-gray-700 rounded p-2 text-sm';

        // Check file size
        const sizeClass = file.size > 25 * 1024 * 1024 ? 'text-red-400' : 'text-gray-300';
        const sizeWarning = file.size > 25 * 1024 * 1024 ? ' (Too large!)' : '';

        fileDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file text-blue-400 mr-2"></i>
                <span class="text-white">${file.name}</span>
            </div>
            <span class="${sizeClass}">${fileSize} MB${sizeWarning}</span>
        `;

        container.appendChild(fileDiv);
    }
}
</script>
{% endblock %}
