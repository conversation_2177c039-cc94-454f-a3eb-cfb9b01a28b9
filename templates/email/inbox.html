{% extends "base.html" %}

{% block title %}{{ account.email_address }} - Inbox{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Email Inbox Header -->
    <div class="bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl p-8 shadow-2xl border border-blue-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-inbox text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            {{ account.email_address }}
                        </h1>
                        <p class="text-blue-100 text-lg">{{ folder_title or 'All Mail' }}</p>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <button onclick="checkEmail()" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 border border-white/30">
                    <i class="fas fa-sync mr-2"></i>Check Mail
                </button>
                <a href="{{ url_for('user_compose_email') }}" class="bg-emerald-600/80 hover:bg-emerald-600 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 border border-emerald-500/30">
                    <i class="fas fa-edit mr-2"></i>Compose
                </a>
                <button onclick="toggleEmailSetup()" class="bg-orange-600/80 hover:bg-orange-600 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 border border-orange-500/30">
                    <i class="fas fa-cogs mr-2"></i>Setup
                </button>
            </div>
        </div>
    </div>

    <!-- Account Info Bar -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div class="flex items-center">
                    <i class="fas fa-envelope text-blue-400 mr-2"></i>
                    <span class="text-white font-medium">{{ account.email_address }}</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-hdd text-green-400 mr-2"></i>
                    <span class="text-gray-300">{{ account.get_storage_used_formatted() }} / {{ account.get_storage_limit_formatted() }}</span>
                </div>
                <div class="flex items-center space-x-2">
                    {% if account.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>Active
                        </span>
                    {% endif %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-paper-plane mr-1"></i>Full Email Server
                    </span>
                </div>
            </div>
            <div class="w-48 bg-gray-700 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ account.get_storage_percent() }}%"></div>
            </div>
        </div>
    </div>

    <!-- Email Client Setup (Hidden by default) -->
    <div id="emailSetupPanel" class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6 hidden">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-cogs text-orange-400 mr-2"></i>
            Email Client Setup: Configure your email client with these settings
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Incoming Mail (IMAP) -->
            <div class="bg-gray-700 rounded-lg p-4">
                <h4 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-download text-blue-400 mr-2"></i>
                    Incoming Mail (IMAP)
                </h4>

                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white font-mono">**************</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white font-mono">993</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-green-400">SSL/TLS</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Username:</span>
                        <span class="text-white font-mono">{{ account.email_address }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Password:</span>
                        <span class="text-yellow-400">Your email password</span>
                    </div>
                </div>
            </div>

            <!-- Outgoing Mail (SMTP) -->
            <div class="bg-gray-700 rounded-lg p-4">
                <h4 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-upload text-red-400 mr-2"></i>
                    Outgoing Mail (SMTP)
                </h4>

                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white font-mono">**************</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white font-mono">587</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-green-400">STARTTLS</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Username:</span>
                        <span class="text-white font-mono">{{ account.email_address }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Password:</span>
                        <span class="text-yellow-400">Your email password</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Copy Buttons -->
        <div class="mt-4 flex flex-wrap gap-2">
            <button onclick="copyToClipboard('**************')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                <i class="fas fa-copy mr-1"></i>Copy Server IP
            </button>
            <button onclick="copyToClipboard('{{ account.email_address }}')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                <i class="fas fa-copy mr-1"></i>Copy Email Address
            </button>
            <a href="{{ url_for('user_smtp_setup') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                <i class="fas fa-external-link-alt mr-1"></i>Full Setup Guide
            </a>
        </div>

        <!-- Important Notes -->
        <div class="mt-4 bg-yellow-900 bg-opacity-30 border border-yellow-700 rounded-lg p-3">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-400 mr-2 mt-0.5"></i>
                <div class="text-sm text-yellow-200">
                    <strong>Important:</strong> Use your email account password (not your panel login password) for authentication in your email client.
                </div>
            </div>
        </div>
    </div>

    <!-- Email List -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <div class="px-6 py-4 border-b border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white">{{ folder_title or 'All Mail' }}</h3>
                <span class="text-gray-400 text-sm">{{ email_messages.total }} messages</span>
            </div>

            <!-- Filter Buttons -->
            <div class="flex space-x-2 mt-3">
                <a href="{{ url_for('user_email_inbox', account_id=account.id, folder='all') }}"
                   class="px-3 py-1 rounded text-sm {% if current_folder == 'all' or not current_folder %}bg-blue-600 text-white{% else %}bg-gray-700 text-gray-300 hover:bg-gray-600{% endif %}">
                    <i class="fas fa-inbox mr-1"></i>All Mail
                </a>
                <a href="{{ url_for('user_email_inbox', account_id=account.id, folder='inbox') }}"
                   class="px-3 py-1 rounded text-sm {% if current_folder == 'inbox' %}bg-blue-600 text-white{% else %}bg-gray-700 text-gray-300 hover:bg-gray-600{% endif %}">
                    <i class="fas fa-download mr-1"></i>Received
                </a>
                <a href="{{ url_for('user_email_inbox', account_id=account.id, folder='sent') }}"
                   class="px-3 py-1 rounded text-sm {% if current_folder == 'sent' %}bg-blue-600 text-white{% else %}bg-gray-700 text-gray-300 hover:bg-gray-600{% endif %}">
                    <i class="fas fa-paper-plane mr-1"></i>Sent
                </a>
            </div>
        </div>
        
        {% if email_messages.items %}
            <div class="divide-y divide-gray-700">
                {% for message in email_messages.items %}
                <div class="p-4 hover:bg-gray-750 cursor-pointer" onclick="viewMessage({{ message.id }})">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3 flex-1">
                            <div class="flex items-center space-x-2">
                                {% if not message.is_read %}
                                    <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                {% else %}
                                    <span class="w-2 h-2"></span>
                                {% endif %}
                                {% if message.is_starred %}
                                    <i class="fas fa-star text-yellow-400"></i>
                                {% else %}
                                    <i class="far fa-star text-gray-500"></i>
                                {% endif %}
                            </div>
                            
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    {% if message.folder == 'SENT' %}
                                        <div class="flex items-center">
                                            <i class="fas fa-paper-plane text-blue-400 mr-2 text-xs"></i>
                                            <p class="text-white font-medium truncate">To: {{ message.recipients }}</p>
                                        </div>
                                    {% else %}
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-green-400 mr-2 text-xs"></i>
                                            <p class="text-white font-medium truncate">From: {{ message.sender }}</p>
                                        </div>
                                    {% endif %}
                                    <span class="text-gray-400 text-sm">
                                        {% if message.folder == 'SENT' and message.sent_at %}
                                            {{ message.sent_at.strftime('%m/%d %H:%M') }}
                                        {% elif message.received_at %}
                                            {{ message.received_at.strftime('%m/%d %H:%M') }}
                                        {% endif %}
                                    </span>
                                </div>
                                <p class="text-gray-300 font-medium truncate">{{ message.subject or '(No Subject)' }}</p>
                                <p class="text-gray-400 text-sm truncate">
                                    {{ (message.body_text or message.body_html or '')[:100] }}...
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2 ml-4">
                            {% if message.folder == 'INBOX' %}
                                <a href="{{ url_for('user_reply_email', message_id=message.id) }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                                   onclick="event.stopPropagation();">
                                    <i class="fas fa-reply mr-1"></i>Reply
                                </a>
                            {% endif %}
                            {% if message.get_attachments_list() %}
                                <i class="fas fa-paperclip text-gray-400"></i>
                            {% endif %}
                            <span class="text-xs text-gray-500">{{ message.get_size_formatted() }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if email_messages.pages > 1 %}
            <div class="px-6 py-4 border-t border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        Showing {{ ((email_messages.page - 1) * email_messages.per_page) + 1 }} to
                        {{ email_messages.page * email_messages.per_page if email_messages.page * email_messages.per_page < email_messages.total else email_messages.total }}
                        of {{ email_messages.total }} messages
                    </div>
                    <div class="flex space-x-2">
                        {% if email_messages.has_prev %}
                            <a href="{{ url_for('user_email_inbox', account_id=account.id, page=email_messages.prev_num, folder=current_folder) }}"
                               class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                Previous
                            </a>
                        {% endif %}
                        {% if email_messages.has_next %}
                            <a href="{{ url_for('user_email_inbox', account_id=account.id, page=email_messages.next_num, folder=current_folder) }}"
                               class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                Next
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <!-- Empty Inbox -->
            <div class="p-12 text-center">
                {% if current_folder == 'sent' %}
                    <i class="fas fa-paper-plane text-gray-600 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No sent emails</h3>
                    <p class="text-gray-500 mb-6">You haven't sent any emails yet.</p>
                {% elif current_folder == 'inbox' %}
                    <i class="fas fa-download text-gray-600 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No received emails</h3>
                    <p class="text-gray-500 mb-6">No emails have been received yet.</p>
                {% else %}
                    <i class="fas fa-inbox text-gray-600 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No emails</h3>
                    <p class="text-gray-500 mb-6">No emails found.</p>
                {% endif %}
                <div class="flex justify-center space-x-4">
                    <button onclick="checkEmail()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-sync mr-2"></i>Check for New Mail
                    </button>
                    <a href="{{ url_for('user_compose_email') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-edit mr-2"></i>Send Your First Email
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Email Client Setup Info -->
    <div class="mt-8 bg-blue-900 bg-opacity-50 border border-blue-700 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
            <div class="text-blue-200 text-sm">
                <p><strong>Email Client Setup:</strong> Configure your email client with these settings:</p>
                <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <strong>IMAP:</strong> mail.lxnd.cloud:993 (SSL)
                    </div>
                    <div>
                        <strong>SMTP:</strong> mail.lxnd.cloud:587 (STARTTLS)
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewMessage(messageId) {
    // Navigate to the message view page
    window.location.href = `/email/{{ account.id }}/message/${messageId}`;
}

function checkEmail() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Checking...';
    button.disabled = true;
    
    // Make API call to check for new emails
    fetch(`/api/email/check/{{ account.id }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            // Refresh the page to show new messages
            window.location.reload();
        } else if (data.error) {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error checking email');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function toggleEmailSetup() {
    const panel = document.getElementById('emailSetupPanel');
    if (panel.classList.contains('hidden')) {
        panel.classList.remove('hidden');
        panel.scrollIntoView({ behavior: 'smooth' });
    } else {
        panel.classList.add('hidden');
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700', 'bg-green-600', 'hover:bg-green-700');
        button.classList.add('bg-green-500');

        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-500');
            if (originalText.includes('Server IP')) {
                button.classList.add('bg-blue-600', 'hover:bg-blue-700');
            } else {
                button.classList.add('bg-green-600', 'hover:bg-green-700');
            }
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy text: ', err);
    });
}
</script>
{% endblock %}
