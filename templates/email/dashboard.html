{% extends "base.html" %}
{% block title %}Email Hub - LXND{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-8">
    <!-- Email Header -->
    <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl p-8 shadow-2xl border border-purple-500/20">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-at text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-1">
                            Email Hub
                        </h1>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                    <div class="text-white text-sm opacity-75 uppercase tracking-wide mb-1">
                        Active Accounts
                    </div>
                    <div class="text-white text-3xl font-bold">
                        {{ email_accounts|length if email_accounts else 0 }}
                    </div>
                    <div class="text-purple-200 text-sm">
                        @lxnd.cloud
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Accounts -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-indigo-500/20 hover:border-indigo-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-indigo-500/20 border border-indigo-400/30">
                        <i class="fas fa-user-circle text-indigo-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Email Accounts</p>
                        <p class="text-3xl font-bold text-white">{{ email_accounts|length if email_accounts else 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inbox Messages -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-blue-500/20 border border-blue-400/30">
                        <i class="fas fa-inbox text-blue-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Inbox Messages</p>
                        <p class="text-3xl font-bold text-white">
                            {% set total_received = 0 %}
                            {% for account in email_accounts %}
                                {% set total_received = total_received + (account.received_emails|length if account.received_emails else 0) %}
                            {% endfor %}
                            {{ total_received }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sent Messages -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-emerald-500/20 hover:border-emerald-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-emerald-500/20 border border-emerald-400/30">
                        <i class="fas fa-paper-plane text-emerald-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Sent Messages</p>
                        <p class="text-3xl font-bold text-white">
                            {% set total_sent = 0 %}
                            {% for account in email_accounts %}
                                {% set total_sent = total_sent + (account.sent_emails|length if account.sent_emails else 0) %}
                            {% endfor %}
                            {{ total_sent }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Used -->
        <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-4 rounded-xl bg-purple-500/20 border border-purple-400/30">
                        <i class="fas fa-database text-purple-300 text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300 uppercase tracking-wide">Email Storage</p>
                        <p class="text-3xl font-bold text-white">
                            {% set email_storage = 0 %}
                            {% for account in email_accounts %}
                                {% set email_storage = email_storage + (account.storage_used_mb if account.storage_used_mb else 0) %}
                            {% endfor %}
                            {{ "%.1f"|format(email_storage) }} MB
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Email Accounts Section -->
        <div class="lg:col-span-2">
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl">
                <div class="px-8 py-6 border-b border-gray-700/50">
                    <div class="flex items-center justify-between">
                        <h3 class="text-2xl font-bold text-white flex items-center">
                            <i class="fas fa-envelope-open text-indigo-400 mr-3"></i>
                            Email Accounts
                        </h3>
                        <a href="{{ url_for('user_create_email_account') }}"
                           class="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-indigo-400/30">
                            <i class="fas fa-plus mr-2"></i>
                            New Account
                        </a>
                    </div>
                </div>
                <div class="p-8">
                    {% if email_accounts %}
                        <div class="space-y-6">
                            {% for account in email_accounts %}
                            <div class="bg-white/5 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 border border-gray-600/30 hover:border-indigo-500/40">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                            <i class="fas fa-at text-white text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-xl font-bold text-white">{{ account.email_address }}</h4>
                                            <p class="text-gray-300">
                                                {{ account.received_emails|length if account.received_emails else 0 }} received • 
                                                {{ account.sent_emails|length if account.sent_emails else 0 }} sent
                                            </p>
                                            <p class="text-sm text-gray-400">
                                                Created {{ account.created_at.strftime('%B %d, %Y') }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <a href="{{ url_for('user_email_inbox', account_id=account.id) }}" 
                                           class="bg-blue-600/80 hover:bg-blue-600 text-white px-4 py-2 rounded-xl transition-all duration-300 border border-blue-500/30">
                                            <i class="fas fa-inbox mr-1"></i>
                                            Inbox
                                        </a>
                                        <a href="{{ url_for('user_compose_email') }}" 
                                           class="bg-emerald-600/80 hover:bg-emerald-600 text-white px-4 py-2 rounded-xl transition-all duration-300 border border-emerald-500/30">
                                            <i class="fas fa-edit mr-1"></i>
                                            Compose
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-16">
                            <div class="w-24 h-24 bg-indigo-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-indigo-400/30">
                                <i class="fas fa-envelope text-indigo-300 text-3xl"></i>
                            </div>
                            <h4 class="text-2xl font-bold text-gray-300 mb-3">No Email Accounts</h4>
                            <p class="text-gray-400 mb-8 text-lg">Create your first professional email account to get started</p>
                            <a href="{{ url_for('user_create_email_account') }}"
                               class="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl border border-indigo-400/30">
                                <i class="fas fa-plus mr-2"></i>
                                Create Email Account
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Account Limits -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl mb-6">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-chart-bar text-blue-400 mr-2"></i>
                        Account Usage
                    </h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-gray-300">Email Accounts</span>
                        <span class="text-white font-medium">
                            {{ current_count }}/{{ max_accounts_display }}
                        </span>
                    </div>
                    {% if not is_unlimited %}
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ (current_count / max_accounts * 100)|round(1) }}%"></div>
                        </div>
                        {% if not can_create_more %}
                            <p class="text-yellow-400 text-sm mt-2 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Upgrade to Lux for more accounts!
                            </p>
                        {% endif %}
                    {% else %}
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                        <p class="text-green-400 text-sm mt-2 flex items-center">
                            <i class="fas fa-crown mr-1"></i>
                            Unlimited accounts (Admin)
                        </p>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    {% if can_create_more %}
                        <a href="{{ url_for('user_create_email_account') }}"
                           class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-indigo-500/40">
                            <i class="fas fa-plus text-indigo-400 mr-3 group-hover:scale-110 transition-transform"></i>
                            <span class="text-white font-medium">New Email Account</span>
                        </a>
                    {% else %}
                        <div class="w-full flex items-center p-4 bg-gray-700/50 rounded-xl border border-gray-600/30 opacity-60">
                            <i class="fas fa-lock text-gray-400 mr-3"></i>
                            <span class="text-gray-400 font-medium">Account Limit Reached</span>
                        </div>
                    {% endif %}
                    {% if email_accounts %}
                    <a href="{{ url_for('user_compose_email') }}" 
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-emerald-500/40">
                        <i class="fas fa-edit text-emerald-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Compose Email</span>
                    </a>
                    {% endif %}
                    <a href="{{ url_for('dashboard') }}" 
                       class="w-full flex items-center p-4 bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 group border border-gray-600/30 hover:border-purple-500/40">
                        <i class="fas fa-home text-purple-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-white font-medium">Back to Dashboard</span>
                    </a>
                </div>
            </div>

            <!-- Email Setup Guide -->
            <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-xl">
                <div class="px-6 py-4 border-b border-gray-700/50">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                        Setup Guide
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="text-gray-300">
                        <h4 class="font-semibold text-white mb-2 flex items-center">
                            <i class="fas fa-server text-indigo-400 mr-2"></i>
                            SMTP Settings
                        </h4>
                        <div class="text-sm space-y-1 bg-white/5 p-3 rounded-lg border border-gray-600/30">
                            <p><strong>Server:</strong> smtp.lxnd.cloud</p>
                            <p><strong>Port:</strong> 587 (STARTTLS)</p>
                            <p><strong>Security:</strong> TLS/STARTTLS</p>
                        </div>
                    </div>
                    <div class="text-gray-300">
                        <h4 class="font-semibold text-white mb-2 flex items-center">
                            <i class="fas fa-download text-purple-400 mr-2"></i>
                            IMAP Settings
                        </h4>
                        <div class="text-sm space-y-1 bg-white/5 p-3 rounded-lg border border-gray-600/30">
                            <p><strong>Server:</strong> imap.lxnd.cloud</p>
                            <p><strong>Port:</strong> 993 (SSL)</p>
                            <p><strong>Security:</strong> SSL/TLS</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
