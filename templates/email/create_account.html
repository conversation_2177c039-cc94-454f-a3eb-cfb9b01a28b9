{% extends "base.html" %}

{% block title %}Create Email Account - LXND{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-8 text-center">
        <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-envelope text-white text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">Create Your Email Account</h1>
        <p class="text-gray-400">Get your free @lxnd.cloud email address</p>
    </div>

    <!-- Create Account Form -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-8">
        <form method="POST">
            {{ form.hidden_tag() }}

            <!-- Email Address -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Choose Your Email Address</label>
                <div class="flex">
                    <input type="text" id="username" placeholder="yourname" 
                           class="flex-1 px-4 py-3 bg-gray-700 border border-gray-600 rounded-l text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                           onchange="updateEmailAddress()" required>
                    <span class="px-4 py-3 bg-gray-600 border border-gray-600 border-l-0 rounded-r text-gray-300 text-lg">@lxnd.cloud</span>
                </div>
                {{ form.email_address(style="display: none;") }}
                <p class="text-xs text-gray-400 mt-2">Choose a unique username for your email address</p>
                {% if form.email_address.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.email_address.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Display Name -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Display Name (Optional)</label>
                {{ form.display_name(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Your Full Name") }}
                <p class="text-xs text-gray-400 mt-1">This name will appear when you send emails</p>
                {% if form.display_name.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.display_name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Password -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                {{ form.password(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Enter a secure password") }}
                <p class="text-xs text-gray-400 mt-1">Minimum 8 characters required</p>
                {% if form.password.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Confirm Password -->
            <div class="mb-8">
                <label class="block text-sm font-medium text-gray-300 mb-2">Confirm Password</label>
                {{ form.confirm_password(class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Confirm your password") }}
                {% if form.confirm_password.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.confirm_password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Submit Button -->
            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium text-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>Create My Email Account
            </button>
        </form>
    </div>

    <!-- Account Benefits -->
    <div class="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-star text-yellow-400 mr-2"></i>
            What You Get
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="flex items-start">
                <i class="fas fa-check text-green-400 mr-2 mt-0.5"></i>
                <div>
                    <p class="text-white font-medium">10GB Storage</p>
                    <p class="text-gray-400">Plenty of space for your emails and attachments</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-shield-alt text-blue-400 mr-2 mt-0.5"></i>
                <div>
                    <p class="text-white font-medium">Secure & Private</p>
                    <p class="text-gray-400">SSL/TLS encryption for all connections</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-mobile-alt text-purple-400 mr-2 mt-0.5"></i>
                <div>
                    <p class="text-white font-medium">Email Client Support</p>
                    <p class="text-gray-400">Use with any IMAP/SMTP email client</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-globe text-green-400 mr-2 mt-0.5"></i>
                <div>
                    <p class="text-white font-medium">Professional Domain</p>
                    <p class="text-gray-400">Your own @lxnd.cloud email address</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Client Configuration -->
    <div class="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-cog text-blue-400 mr-2"></i>
            Email Client Setup
        </h3>
        
        <p class="text-gray-400 mb-4">Once your account is created, you can configure any email client with these settings:</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-300 mb-3">Incoming Mail (IMAP)</h4>
                <div class="bg-gray-700 rounded p-3 text-sm space-y-1">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white">mail.lxnd.cloud</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white">993</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-white">SSL/TLS</span>
                    </div>
                </div>
            </div>
            
            <div>
                <h4 class="text-sm font-medium text-gray-300 mb-3">Outgoing Mail (SMTP)</h4>
                <div class="bg-gray-700 rounded p-3 text-sm space-y-1">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white">mail.lxnd.cloud</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white">587</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-white">STARTTLS</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms Notice -->
    <div class="mt-8 bg-blue-900 bg-opacity-50 border border-blue-700 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
            <div class="text-blue-200 text-sm">
                {% if current_user.is_admin %}
                    <p><strong>Admin Account:</strong> You have unlimited email accounts with 1TB storage each.</p>
                {% elif current_user.is_lux_active() %}
                    <p><strong>Lux Account:</strong> You can create up to 3 email accounts with 1TB storage each.</p>
                {% else %}
                    <p><strong>Free Account:</strong> You get 1 free email account with 10GB storage.</p>
                    <p class="mt-1"><strong>Upgrade to Lux</strong> for 2 additional accounts and 1TB storage!</p>
                {% endif %}
                <p class="mt-1">By creating an account, you agree to use the email service responsibly and in accordance with our terms of service.</p>
            </div>
        </div>
    </div>
</div>

<script>
function updateEmailAddress() {
    const username = document.getElementById('username').value;
    const emailField = document.getElementById('email_address');
    emailField.value = username + '@lxnd.cloud';
}

// Initialize email address on page load
document.addEventListener('DOMContentLoaded', function() {
    const emailField = document.getElementById('email_address');
    if (emailField.value) {
        const username = emailField.value.split('@')[0];
        document.getElementById('username').value = username;
    }
});
</script>
{% endblock %}
