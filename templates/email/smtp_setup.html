{% extends "base.html" %}

{% block title %}SMTP Setup Guide{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-white mb-2">📧 SMTP Setup Guide</h1>
            <p class="text-gray-400">Configure your email client to send and receive emails</p>
        </div>
        <a href="{{ url_for('user_email_dashboard') }}" class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-arrow-left mr-2"></i>Back to Email
        </a>
    </div>

    <!-- Your Email Accounts -->
    {% if accounts %}
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                <i class="fas fa-user text-blue-400 mr-2"></i>
                Your Email Accounts
            </h3>
            
            <div class="space-y-3">
                {% for account in accounts %}
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">{{ account.email_address }}</h4>
                                <p class="text-sm text-gray-400">Use this email address in your email client</p>
                            </div>
                            <button onclick="copyToClipboard('{{ account.email_address }}')" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% else %}
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-yellow-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-semibold text-white mb-2">No Email Accounts</h3>
                <p class="text-gray-400 mb-4">You need to create an email account first</p>
                <a href="{{ url_for('user_create_email_account') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>Create Email Account
                </a>
            </div>
        </div>
    {% endif %}

    <!-- Server Configuration -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-server text-green-400 mr-2"></i>
            Server Configuration
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Incoming Mail (IMAP) -->
            <div class="bg-gray-700 rounded-lg p-4">
                <h4 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-download text-blue-400 mr-2"></i>
                    Incoming Mail (IMAP)
                </h4>
                
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white font-mono">{{ server_config.smtp_server }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white font-mono">993</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-green-400">SSL/TLS</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Authentication:</span>
                        <span class="text-white">Normal password</span>
                    </div>
                </div>
            </div>

            <!-- Outgoing Mail (SMTP) -->
            <div class="bg-gray-700 rounded-lg p-4">
                <h4 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-upload text-red-400 mr-2"></i>
                    Outgoing Mail (SMTP)
                </h4>
                
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Server:</span>
                        <span class="text-white font-mono">{{ server_config.smtp_server }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Port:</span>
                        <span class="text-white font-mono">{{ server_config.smtp_port_submission }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Security:</span>
                        <span class="text-green-400">STARTTLS</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Authentication:</span>
                        <span class="text-white">Normal password</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Client Setup Instructions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Gmail/Google Mail -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                <i class="fab fa-google text-red-400 mr-2"></i>
                Gmail / Google Mail
            </h4>
            
            <ol class="space-y-2 text-sm text-gray-300">
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                    <span>Open Gmail → Settings → See all settings</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                    <span>Go to "Accounts and Import" tab</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                    <span>Click "Add a mail account" in "Check mail from other accounts"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">4</span>
                    <span>Enter your @{{ server_config.domain }} email address</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">5</span>
                    <span>Use the server settings shown above</span>
                </li>
            </ol>
        </div>

        <!-- Outlook -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                <i class="fab fa-microsoft text-blue-400 mr-2"></i>
                Microsoft Outlook
            </h4>
            
            <ol class="space-y-2 text-sm text-gray-300">
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                    <span>Open Outlook → File → Add Account</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                    <span>Choose "Manual setup or additional server types"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                    <span>Select "POP or IMAP"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">4</span>
                    <span>Enter your email and password</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">5</span>
                    <span>Use the server settings shown above</span>
                </li>
            </ol>
        </div>

        <!-- Thunderbird -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                <i class="fas fa-envelope text-orange-400 mr-2"></i>
                Mozilla Thunderbird
            </h4>
            
            <ol class="space-y-2 text-sm text-gray-300">
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                    <span>Open Thunderbird → Account Settings</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                    <span>Click "Account Actions" → "Add Mail Account"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                    <span>Enter your name, email, and password</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">4</span>
                    <span>Click "Manual config" if auto-detection fails</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">5</span>
                    <span>Use the server settings shown above</span>
                </li>
            </ol>
        </div>

        <!-- Mobile (iOS/Android) -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                <i class="fas fa-mobile-alt text-green-400 mr-2"></i>
                Mobile (iOS/Android)
            </h4>
            
            <ol class="space-y-2 text-sm text-gray-300">
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                    <span>Open Settings → Mail → Accounts</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                    <span>Tap "Add Account" → "Other"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                    <span>Choose "Add Mail Account"</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">4</span>
                    <span>Enter your email details</span>
                </li>
                <li class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">5</span>
                    <span>Use the server settings shown above</span>
                </li>
            </ol>
        </div>
    </div>

    <!-- Important Notes -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-exclamation-triangle text-yellow-400 mr-2"></i>
            Important Notes
        </h3>
        
        <div class="space-y-3 text-gray-300">
            <div class="flex items-start">
                <i class="fas fa-key text-blue-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="font-medium text-white">Password</h4>
                    <p class="text-sm">Use your email account password, not your panel login password.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-shield-alt text-green-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="font-medium text-white">Security</h4>
                    <p class="text-sm">Always use SSL/TLS or STARTTLS for secure email transmission.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-clock text-purple-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="font-medium text-white">Sync Time</h4>
                    <p class="text-sm">It may take a few minutes for your email client to sync with the server.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-question-circle text-orange-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="font-medium text-white">Need Help?</h4>
                    <p class="text-sm">If you have issues, contact support or check your email client's documentation.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        button.classList.add('bg-green-600');
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy text: ', err);
    });
}
</script>
{% endblock %}
