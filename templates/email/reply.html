{% extends "base.html" %}

{% block title %}{{ messages.reply_email }} - LXND{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ messages.reply_email }}</h1>
            <a href="{{ url_for('user_email_inbox', account_id=original_message.account_id) }}" 
               class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ messages.back_to_inbox }}
            </a>
        </div>

        <!-- Original Message Preview -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ messages.replying_to }}</h3>
            <div class="space-y-2 text-sm">
                <div><span class="font-medium">{{ messages.from }}:</span> {{ original_message.sender }}</div>
                <div><span class="font-medium">{{ messages.subject }}:</span> {{ original_message.subject or messages.no_subject }}</div>
                <div><span class="font-medium">{{ messages.date }}:</span> 
                    {% if original_message.received_at %}
                        {{ original_message.received_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    {% else %}
                        {{ messages.unknown }}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Reply Form -->
        <div class="bg-white dark:bg-gray-900 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <form method="POST" class="space-y-6">
                    {{ form.hidden_tag() }}
                    
                    <!-- From Account -->
                    <div>
                        <label for="{{ form.from_account.id }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ messages.from_account }}
                        </label>
                        {{ form.from_account(class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500") }}
                        {% if form.from_account.errors %}
                            <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                                {% for error in form.from_account.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- To -->
                    <div>
                        <label for="{{ form.to.id }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ messages.to }}
                        </label>
                        {{ form.to(class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500", readonly=true) }}
                        {% if form.to.errors %}
                            <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                                {% for error in form.to.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="{{ form.subject.id }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ messages.subject }}
                        </label>
                        {{ form.subject(class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500") }}
                        {% if form.subject.errors %}
                            <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                                {% for error in form.subject.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Body -->
                    <div>
                        <label for="{{ form.body.id }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ messages.message }}
                        </label>
                        {{ form.body(class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500", rows="15") }}
                        {% if form.body.errors %}
                            <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                                {% for error in form.body.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-between">
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            {{ messages.send_reply }}
                        </button>
                        
                        <a href="{{ url_for('user_email_inbox', account_id=original_message.account_id) }}" 
                           class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                            {{ messages.cancel }}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
