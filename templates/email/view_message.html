{% extends "base.html" %}

{% block title %}{{ message.subject }} - {{ account.email_address }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-white mb-2">{{ message.subject or "(No Subject)" }}</h1>
                <p class="text-gray-400">{{ account.email_address }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('user_email_inbox', account_id=account.id) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Inbox
                </a>
            </div>
        </div>
    </div>

    <!-- Email Content -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
        <!-- <PERSON><PERSON>er -->
        <div class="bg-gray-700 px-6 py-4 border-b border-gray-600">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">
                                {{ message.sender.split('@')[0][0].upper() if message.sender else 'U' }}
                            </span>
                        </div>
                        <div>
                            <p class="text-white font-medium">{{ message.sender or "Unknown Sender" }}</p>
                            <p class="text-gray-400 text-sm">
                                {{ message.received_at.strftime('%B %d, %Y at %I:%M %p') if message.received_at else 'Unknown Date' }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-300">
                        {% if message.folder == 'SENT' %}
                            <!-- Gesendete Email: Zeige Empfänger und eigene Adresse als Absender -->
                            {% if message.recipients %}
                                {% set recipients_list = message.recipients | from_json %}
                                {% if recipients_list and recipients_list | length > 0 %}
                                    <p><strong>To:</strong> {{ recipients_list | join(', ') }}</p>
                                {% else %}
                                    <!-- Fallback: Zeige rohe Recipients-Daten für Debugging -->
                                    <p><strong>To:</strong> {{ message.recipients or 'No recipients data' }}</p>
                                {% endif %}
                            {% else %}
                                <p><strong>To:</strong> No recipients stored</p>
                            {% endif %}
                            <p><strong>From:</strong> {{ account.email_address }}</p>
                        {% else %}
                            <!-- Empfangene Email: Zeige Absender und eigene Adresse als Empfänger -->
                            <p><strong>From:</strong> {{ message.sender or "Unknown Sender" }}</p>
                            <p><strong>To:</strong> {{ account.email_address }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Message Actions -->
                <div class="flex space-x-2">
                    {% if message.is_read %}
                        <button onclick="markUnread({{ message.id }})" class="text-gray-400 hover:text-white" title="Mark as unread">
                            <i class="fas fa-envelope-open"></i>
                        </button>
                    {% else %}
                        <button onclick="markRead({{ message.id }})" class="text-gray-400 hover:text-white" title="Mark as read">
                            <i class="fas fa-envelope"></i>
                        </button>
                    {% endif %}
                    
                    <button onclick="toggleStar({{ message.id }})" class="text-gray-400 hover:text-yellow-400" title="Star message">
                        <i class="fas fa-star{% if not message.is_starred %}-o{% endif %}"></i>
                    </button>
                    
                    <button onclick="deleteMessage({{ message.id }})" class="text-gray-400 hover:text-red-400" title="Delete message">
                        <i class="fas fa-trash"></i>
                    </button>
                    
                    <a href="{{ url_for('user_compose_email') }}?reply_to={{ message.id }}" class="text-gray-400 hover:text-blue-400" title="Reply">
                        <i class="fas fa-reply"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Email Body -->
        <div class="px-6 py-6">
            {% if message.body_html %}
                <!-- HTML Content -->
                <div class="prose prose-invert max-w-none">
                    {{ message.body_html | safe }}
                </div>
            {% elif message.body_text %}
                <!-- Plain Text Content -->
                <div class="whitespace-pre-wrap text-gray-200 font-mono text-sm">
                    {{ message.body_text }}
                </div>
            {% else %}
                <!-- No Content -->
                <div class="text-center text-gray-400 py-8">
                    <i class="fas fa-envelope-open text-4xl mb-4"></i>
                    <p>This message has no content.</p>
                </div>
            {% endif %}
        </div>

        <!-- Attachments (if any) -->
        {% set attachments_list = message.get_attachments_list() %}
        {% if attachments_list %}
            <div class="px-6 py-4 bg-gray-700 border-t border-gray-600">
                <h3 class="text-white font-medium mb-3">
                    <i class="fas fa-paperclip mr-2"></i>Attachments ({{ attachments_list | length }})
                </h3>
                <div class="space-y-2">
                    {% for attachment in attachments_list %}
                        <div class="flex items-center justify-between bg-gray-600 rounded-lg p-3">
                            <div class="flex items-center">
                                <i class="fas fa-file text-blue-400 mr-3"></i>
                                <div>
                                    <p class="text-white font-medium">{{ attachment.filename or 'Unknown file' }}</p>
                                    <p class="text-gray-400 text-sm">
                                        {% if attachment.size %}
                                            {{ "%.1f"|format(attachment.size / 1024) }} KB
                                        {% else %}
                                            Unknown size
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <a href="{{ url_for('download_attachment', account_id=account.id, message_id=message.id, attachment_id=loop.index0) }}"
                               class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm inline-flex items-center">
                                <i class="fas fa-download mr-1"></i>Download
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="mt-6 flex space-x-3">
        <a href="{{ url_for('user_compose_email') }}?reply_to={{ message.id }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-reply mr-2"></i>Reply
        </a>
        <a href="{{ url_for('user_compose_email') }}?forward={{ message.id }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-share mr-2"></i>Forward
        </a>
        <button onclick="deleteMessage({{ message.id }})" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-trash mr-2"></i>Delete
        </button>
    </div>
</div>

<script>
function markRead(messageId) {
    fetch(`/email/{{ account.id }}/message/${messageId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}

function markUnread(messageId) {
    fetch(`/email/{{ account.id }}/message/${messageId}/mark-unread`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}

function toggleStar(messageId) {
    // TODO: Implement star functionality
    console.log('Toggle star for message:', messageId);
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message?')) {
        // TODO: Implement delete functionality
        console.log('Delete message:', messageId);
    }
}
</script>
{% endblock %}
