{% extends "base.html" %}

{% block title %}API Documentation - Lxnd.cloud{% endblock %}

{% block head %}
<style>
    /* Smooth scrolling for the entire page */
    html {
        scroll-behavior: smooth;
    }

    /* Enhanced navigation styling */
    .nav-link {
        position: relative;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        margin: 0.125rem 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(0);
    }

    .nav-link:hover {
        background-color: rgba(59, 130, 246, 0.15);
        transform: translateX(4px);
        box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.1);
    }

    /* Active state styling with enhanced background and transition */
    .nav-link.active {
        background: linear-gradient(135deg, rgb(37, 99, 235) 0%, rgb(59, 130, 246) 100%) !important;
        color: white !important;
        font-weight: 600;
        transform: translateX(6px);
        box-shadow: 0 4px 12px 0 rgba(37, 99, 235, 0.3), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        border-left: 3px solid rgb(96, 165, 250);
    }

    /* Enhanced border indicator for sub-items when active */
    .nav-link.active.ml-4::before {
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 20px;
        background: linear-gradient(180deg, rgb(96, 165, 250) 0%, rgb(59, 130, 246) 100%);
        border-radius: 2px;
        box-shadow: 0 0 8px rgba(96, 165, 250, 0.4);
    }

    /* Add a subtle glow effect for active items */
    .nav-link.active::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.375rem;
        opacity: 0;
        animation: activeGlow 2s ease-in-out infinite alternate;
    }

    @keyframes activeGlow {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }

    /* Pulse animation for newly activated items */
    @keyframes activePulse {
        0% {
            transform: translateX(6px) scale(1);
            box-shadow: 0 4px 12px 0 rgba(37, 99, 235, 0.3), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        }
        50% {
            transform: translateX(6px) scale(1.02);
            box-shadow: 0 6px 16px 0 rgba(37, 99, 235, 0.4), 0 4px 8px 0 rgba(0, 0, 0, 0.15);
        }
        100% {
            transform: translateX(6px) scale(1);
            box-shadow: 0 4px 12px 0 rgba(37, 99, 235, 0.3), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        }
    }

    /* Apply pulse animation to newly active items */
    .nav-link.active.pulse {
        animation: activePulse 0.6s ease-in-out;
    }

    /* Sticky sidebar */
    .sticky {
        position: sticky;
        top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
    <div class="lg:col-span-1">
        <div class="bg-gray-800 border border-gray-700 shadow-lg rounded-lg p-6 sticky top-8">
            <h5 class="text-lg font-semibold text-white mb-4">Documentation</h5>
            <nav class="space-y-2" id="sidebar-nav">
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 transition-colors duration-200" href="#overview" data-target="overview">Overview</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 transition-colors duration-200" href="#authentication" data-target="authentication">Authentication</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 transition-colors duration-200" href="#license-api" data-target="license-api">License API</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#license-check" data-target="license-check">Check License</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#license-add" data-target="license-add">Add License</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 transition-colors duration-200" href="#file-api" data-target="file-api">File Upload API</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#files-list" data-target="files-list">List Files</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#files-upload" data-target="files-upload">Upload File</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#files-get" data-target="files-get">Get File Info</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#files-delete" data-target="files-delete">Delete File</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 ml-4 transition-colors duration-200" href="#files-toggle" data-target="files-toggle">Toggle Visibility</a>
                <a class="nav-link block text-blue-400 hover:text-blue-300 py-1 transition-colors duration-200" href="#errors" data-target="errors">Error Handling</a>
            </nav>
        </div>
    </div>
    
    <div class="lg:col-span-3">
        <div class="bg-gray-800 border border-gray-700 shadow-lg rounded-lg p-8">
            <h1 class="text-3xl font-bold text-white mb-8">API Documentation</h1>
                
                <section id="overview" class="mb-12">
                    <h2 class="text-2xl font-bold text-white mb-4">Overview</h2>
                    <p class="text-gray-400 mb-6">The lxnd.cloud API provides endpoints for managing software licenses and file uploads. All API endpoints return JSON responses.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-750 p-4 rounded-lg border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-2">License API</h4>
                            <p class="text-gray-400 text-sm mb-2">Validate and manage software licenses</p>
                            <div class="license-key bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded border border-gray-600">{{ request.host_url }}api/license/</div>
                        </div>
                        <div class="bg-gray-750 p-4 rounded-lg border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-2">File Upload API</h4>
                            <p class="text-gray-400 text-sm mb-2">Upload, manage, and download files</p>
                            <div class="license-key bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded border border-gray-600">{{ request.host_url }}api/files/</div>
                        </div>
                    </div>

                    <h4 class="text-lg font-semibold text-white mb-2">Content Types</h4>
                    <ul class="text-gray-400 list-disc list-inside">
                        <li>License API: <code class="bg-gray-700 text-gray-200 px-2 py-1 rounded">application/json</code></li>
                        <li>File Upload API: <code class="bg-gray-700 text-gray-200 px-2 py-1 rounded">multipart/form-data</code> for uploads, <code class="bg-gray-700 text-gray-200 px-2 py-1 rounded">application/json</code> for responses</li>
                    </ul>
                </section>
                
                <section id="authentication" class="mb-12">
                    <h2 class="text-2xl font-bold text-white mb-4">Authentication</h2>
                    <p class="text-gray-400 mb-6">API endpoints use different authentication methods depending on the API. You can get your API token from your <a href="{{ url_for('profile') }}" class="text-blue-400 hover:text-blue-300 underline">profile page</a> after creating an account.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-750 p-4 rounded-lg border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-3">License API</h4>
                            <p class="text-gray-400 text-sm mb-3">Uses URL path authentication for simplicity</p>
                            <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm">
                                <code>/api/license/add/{project_id}/{api_token}/{license_key}</code>
                            </div>
                        </div>
                        <div class="bg-gray-750 p-4 rounded-lg border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-3">File Upload API</h4>
                            <p class="text-gray-400 text-sm mb-3">Uses Bearer token in Authorization header</p>
                            <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm">
                                <code>Authorization: Bearer YOUR_API_TOKEN</code>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- License API Section -->
                <section id="license-api" class="mb-12">
                    <h2 class="text-2xl font-bold text-white mb-6">License API</h2>
                    <p class="text-gray-400 mb-6">Endpoints for validating and managing software licenses.</p>

                    <!-- Check License -->
                    <div id="license-check" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Check License</h3>
                        <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                            <span class="bg-green-900 text-green-300 px-2 py-1 rounded font-medium">GET</span> 
                            <code class="ml-2 text-gray-200">/api/license/check/{project_id}/{license_key}</code>
                        </div>
                        
                        <h5 class="text-lg font-medium text-white mb-2">Description</h5>
                        <p class="text-gray-400 mb-4">Validates a license key for a specific project and returns its status.</p>
                        
                        <h5 class="text-lg font-medium text-white mb-3">Parameters</h5>
                        <div class="overflow-x-auto mb-4">
                            <table class="w-full text-sm text-left text-gray-400">
                                <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3">Parameter</th>
                                        <th scope="col" class="px-6 py-3">Type</th>
                                        <th scope="col" class="px-6 py-3">Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="bg-gray-800 border-b border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-200">project_id</td>
                                        <td class="px-6 py-4">string</td>
                                        <td class="px-6 py-4">The unique project identifier</td>
                                    </tr>
                                    <tr class="bg-gray-800 border-b border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-200">license_key</td>
                                        <td class="px-6 py-4">string</td>
                                        <td class="px-6 py-4">The license key to validate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <h5 class="text-lg font-medium text-white mb-3">Response</h5>
                        <pre class="bg-gray-950 text-gray-300 p-4 rounded-lg overflow-x-auto border border-gray-800"><code>{
  "valid": true,
  "project_id": "project_id",
  "license_key": "license_key",
  "created_at": "2024-01-01T00:00:00",
  "last_checked": "2024-01-01T00:00:00",
  "is_active": true,
  "is_expired": false,
  "expires_at": "2024-12-31T23:59:59",
  "days_until_expiry": 45
}</code></pre>

                        <h5 class="text-lg font-medium text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X GET "{{ request.host_url }}api/license/check/your_project_id/your_license_key"</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

response = requests.get("{{ request.host_url }}api/license/check/PROJECT_ID/LICENSE_KEY")
license_data = response.json()

if license_data["valid"]:
    print("License is valid!")
    print(f"Expires in {license_data['days_until_expiry']} days")
else:
    print("License is invalid or expired")</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>fetch("{{ request.host_url }}api/license/check/PROJECT_ID/LICENSE_KEY")
  .then(response => response.json())
  .then(data => {
    if (data.valid) {
      console.log("License is valid!");
      console.log(`Expires in ${data.days_until_expiry} days`);
    } else {
      console.log("License is invalid or expired");
    }
  });</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Add License -->
                    <div id="license-add" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Add License</h3>
                        <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                            <span class="bg-blue-900 text-blue-300 px-2 py-1 rounded font-medium">POST</span> 
                            <code class="ml-2 text-gray-200">/api/license/add/{project_id}/{api_token}/{license_key}</code>
                        </div>
                        
                        <h5 class="text-lg font-medium text-white mb-2">Description</h5>
                        <p class="text-gray-400 mb-4">Adds a new license key to a project. Requires authentication.</p>
                        
                        <h5 class="text-lg font-medium text-white mb-3">Parameters</h5>
                        <div class="overflow-x-auto mb-4">
                            <table class="w-full text-sm text-left text-gray-400">
                                <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3">Parameter</th>
                                        <th scope="col" class="px-6 py-3">Type</th>
                                        <th scope="col" class="px-6 py-3">Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="bg-gray-800 border-b border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-200">project_id</td>
                                        <td class="px-6 py-4">string</td>
                                        <td class="px-6 py-4">The unique project identifier</td>
                                    </tr>
                                    <tr class="bg-gray-800 border-b border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-200">api_token</td>
                                        <td class="px-6 py-4">string</td>
                                        <td class="px-6 py-4">Your API authentication token</td>
                                    </tr>
                                    <tr class="bg-gray-800 border-b border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-200">license_key</td>
                                        <td class="px-6 py-4">string</td>
                                        <td class="px-6 py-4">The license key to add</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <h5 class="text-lg font-medium text-white mb-3">Request Body (Optional)</h5>
                        <pre class="bg-gray-950 text-gray-300 p-4 rounded-lg overflow-x-auto border border-gray-800 mb-4"><code>{
  "duration_days": 30
}</code></pre>
                        
                        <h5 class="text-lg font-medium text-white mb-3">Response</h5>
                        <pre class="bg-gray-950 text-gray-300 p-4 rounded-lg overflow-x-auto border border-gray-800"><code>{
  "success": true,
  "project_id": "project_id",
  "license_key": "license_key",
  "created_at": "2024-01-01T00:00:00",
  "expires_at": "2024-02-01T00:00:00",
  "duration_days": 30
}</code></pre>

                        <h5 class="text-lg font-medium text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X POST "{{ request.host_url }}api/license/add/your_project_id/your_api_token/new_license_key" \
     -H "Content-Type: application/json" \
     -d '{"duration_days": 30}'</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

data = {"duration_days": 30}
response = requests.post(
    "{{ request.host_url }}api/license/add/PROJECT_ID/API_TOKEN/NEW_LICENSE_KEY",
    json=data
)
result = response.json()
print(f"License added: {result['success']}")</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>fetch("{{ request.host_url }}api/license/add/PROJECT_ID/API_TOKEN/NEW_LICENSE_KEY", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    duration_days: 30
  })
})
  .then(response => response.json())
  .then(data => {
    console.log("License added:", data.success);
  });</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- File Upload API Section -->
                <section id="file-api" class="mb-12">
                    <h2 class="text-2xl font-bold text-white mb-6">File Upload API</h2>
                    <p class="text-gray-400 mb-6">Endpoints for uploading, managing, and downloading files.</p>

                    <!-- List Files -->
                    <div id="files-list" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">List Files</h3>
                            <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                                <span class="bg-green-900 text-green-300 px-2 py-1 rounded font-medium">GET</span>
                                <code class="ml-2 text-gray-200">/api/files</code>
                            </div>
                            <p class="text-gray-400 mb-3">Retrieves all files for the authenticated user.</p>
                            <h5 class="text-md font-semibold text-white mb-2">Headers</h5>
                            <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 mb-3">
                                <code>Authorization: Bearer YOUR_API_TOKEN</code>
                            </div>
                            <h5 class="text-md font-semibold text-white mb-2">Response</h5>
                            <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>{
  "files": [
    {
      "id": 1,
      "file_id": "abc123def456",
      "filename": "unique_filename.jpg",
      "original_filename": "my_image.jpg",
      "file_size": 1024000,
      "file_type": "jpg",
      "upload_date": "2024-01-15T10:30:00",
      "download_count": 5,
      "public": false,
      "download_url": "https://example.com/files/download/abc123def456"
    }
  ],
  "total_files": 1,
  "total_size_bytes": 1024000
}</code></pre>

                        <h5 class="text-md font-semibold text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X GET "{{ request.host_url }}api/files" \
     -H "Authorization: Bearer YOUR_API_TOKEN"</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

headers = {'Authorization': 'Bearer YOUR_API_TOKEN'}
response = requests.get("{{ request.host_url }}api/files", headers=headers)
files_data = response.json()
print(f"Total files: {files_data['total_files']}")</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>fetch("{{ request.host_url }}api/files", {
  headers: {
    "Authorization": "Bearer YOUR_API_TOKEN"
  }
})
  .then(response => response.json())
  .then(data => {
    console.log(`Total files: ${data.total_files}`);
    data.files.forEach(file => {
      console.log(`- ${file.original_filename} (${file.file_size} bytes)`);
    });
  });</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Upload File -->
                    <div id="files-upload" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Upload File</h3>
                            <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                                <span class="bg-blue-900 text-blue-300 px-2 py-1 rounded font-medium">POST</span>
                                <code class="ml-2 text-gray-200">/api/files/upload</code>
                            </div>
                            <p class="text-gray-400 mb-3">Uploads a file to the user's account.</p>
                            <h5 class="text-md font-semibold text-white mb-2">Headers</h5>
                            <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 mb-3">
                                <code>Authorization: Bearer YOUR_API_TOKEN<br>Content-Type: multipart/form-data</code>
                            </div>
                            <h5 class="text-md font-semibold text-white mb-2">Form Data</h5>
                            <ul class="text-gray-400 mb-3 list-disc list-inside">
                                <li><code>file</code> (required): The file to upload</li>
                                <li><code>public</code> (optional): "true" for public, "false" for private (default: false)</li>
                            </ul>
                            <h5 class="text-md font-semibold text-white mb-2">Response</h5>
                            <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>{
  "message": "File uploaded successfully",
  "file": {
    "id": 1,
    "file_id": "abc123def456",
    "filename": "unique_filename.jpg",
    "original_filename": "my_image.jpg",
    "file_size": 1024000,
    "file_type": "jpg",
    "upload_date": "2024-01-15T10:30:00",
    "public": false,
    "download_url": "https://example.com/files/download/abc123def456"
  }
}</code></pre>

                        <h5 class="text-md font-semibold text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X POST "{{ request.host_url }}api/files/upload" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -F "file=@/path/to/your/file.jpg" \
     -F "public=false"</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

files = {'file': open('/path/to/your/file.jpg', 'rb')}
data = {'public': 'false'}
headers = {'Authorization': 'Bearer YOUR_API_TOKEN'}

response = requests.post(
    "{{ request.host_url }}api/files/upload",
    files=files,
    data=data,
    headers=headers
)
result = response.json()
print(f"File uploaded: {result['file']['original_filename']}")</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>const fileInput = document.getElementById('fileInput');
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('public', 'false');

fetch("{{ request.host_url }}api/files/upload", {
  method: "POST",
  headers: {
    "Authorization": "Bearer YOUR_API_TOKEN"
  },
  body: formData
})
  .then(response => response.json())
  .then(data => {
    console.log("File uploaded:", data.file.original_filename);
  });</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Get File Info -->
                    <div id="files-get" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Get File Info</h3>
                            <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                                <span class="bg-green-900 text-green-300 px-2 py-1 rounded font-medium">GET</span>
                                <code class="ml-2 text-gray-200">/api/files/{file_id}</code>
                            </div>
                            <p class="text-gray-400 mb-3">Retrieves information about a specific file.</p>
                            <h5 class="text-md font-semibold text-white mb-2">Headers</h5>
                            <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 mb-3">
                                <code>Authorization: Bearer YOUR_API_TOKEN</code>
                            </div>
                        </div>
                    </div>

                    <!-- Delete File -->
                    <div id="files-delete" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Delete File</h3>
                        <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                            <span class="bg-red-900 text-red-300 px-2 py-1 rounded font-medium">DELETE</span>
                            <code class="ml-2 text-gray-200">/api/files/{file_id}</code>
                        </div>
                        <p class="text-gray-400 mb-4">Deletes a file from the user's account.</p>

                        <h5 class="text-md font-semibold text-white mb-2">Headers</h5>
                        <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 mb-4">
                            <code>Authorization: Bearer YOUR_API_TOKEN</code>
                        </div>

                        <h5 class="text-md font-semibold text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X DELETE "{{ request.host_url }}api/files/abc123def456" \
     -H "Authorization: Bearer YOUR_API_TOKEN"</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

headers = {'Authorization': 'Bearer YOUR_API_TOKEN'}
response = requests.delete("{{ request.host_url }}api/files/FILE_ID", headers=headers)
result = response.json()
print(result['message'])</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>fetch("{{ request.host_url }}api/files/FILE_ID", {
  method: "DELETE",
  headers: {
    "Authorization": "Bearer YOUR_API_TOKEN"
  }
})
  .then(response => response.json())
  .then(data => {
    console.log(data.message);
  });</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Toggle File Visibility -->
                    <div id="files-toggle" class="mb-8">
                        <h3 class="text-xl font-semibold text-white mb-4">Toggle File Visibility</h3>
                        <div class="bg-gray-700 border border-gray-600 p-4 rounded-lg mb-4">
                            <span class="bg-blue-900 text-blue-300 px-2 py-1 rounded font-medium">POST</span>
                            <code class="ml-2 text-gray-200">/api/files/{file_id}/toggle-visibility</code>
                        </div>
                        <p class="text-gray-400 mb-4">Toggles a file between public and private visibility.</p>

                        <h5 class="text-md font-semibold text-white mb-2">Headers</h5>
                        <div class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 mb-4">
                            <code>Authorization: Bearer YOUR_API_TOKEN</code>
                        </div>

                        <h5 class="text-md font-semibold text-white mb-3">Examples</h5>
                        <div class="space-y-4">
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">cURL</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>curl -X POST "{{ request.host_url }}api/files/abc123def456/toggle-visibility" \
     -H "Authorization: Bearer YOUR_API_TOKEN"</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">Python</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>import requests

headers = {'Authorization': 'Bearer YOUR_API_TOKEN'}
response = requests.post("{{ request.host_url }}api/files/FILE_ID/toggle-visibility", headers=headers)
result = response.json()
print(f"File is now {result['file']['public'] and 'public' or 'private'}")</code></pre>
                            </div>
                            <div>
                                <h6 class="text-md font-semibold text-white mb-2">JavaScript</h6>
                                <pre class="bg-gray-950 text-gray-300 p-3 rounded border border-gray-800 text-sm"><code>fetch("{{ request.host_url }}api/files/FILE_ID/toggle-visibility", {
  method: "POST",
  headers: {
    "Authorization": "Bearer YOUR_API_TOKEN"
  }
})
  .then(response => response.json())
  .then(data => {
    console.log(`File is now ${data.file.public ? 'public' : 'private'}`);
  });</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Examples are now integrated into each endpoint above -->
                
                <section id="errors" class="mb-12">
                    <h2 class="text-2xl font-bold text-white mb-6">Error Handling</h2>
                    
                    <h4 class="text-lg font-semibold text-white mb-3">HTTP Status Codes</h4>
                    <div class="overflow-x-auto mb-6">
                        <table class="w-full text-sm text-left text-gray-400">
                            <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Status Code</th>
                                    <th scope="col" class="px-6 py-3">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-gray-800 border-b border-gray-700">
                                    <td class="px-6 py-4 font-medium text-gray-200">200</td>
                                    <td class="px-6 py-4">Success</td>
                                </tr>
                                <tr class="bg-gray-800 border-b border-gray-700">
                                    <td class="px-6 py-4 font-medium text-gray-200">400</td>
                                    <td class="px-6 py-4">Bad Request - Invalid parameters</td>
                                </tr>
                                <tr class="bg-gray-800 border-b border-gray-700">
                                    <td class="px-6 py-4 font-medium text-gray-200">401</td>
                                    <td class="px-6 py-4">Unauthorized - Invalid API token</td>
                                </tr>
                                <tr class="bg-gray-800 border-b border-gray-700">
                                    <td class="px-6 py-4 font-medium text-gray-200">404</td>
                                    <td class="px-6 py-4">Not Found - Project or license not found</td>
                                </tr>
                                <tr class="bg-gray-800 border-b border-gray-700">
                                    <td class="px-6 py-4 font-medium text-gray-200">500</td>
                                    <td class="px-6 py-4">Internal Server Error</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <h4 class="text-lg font-semibold text-white mb-3">Error Response Format</h4>
                    <pre class="bg-gray-950 text-gray-300 p-4 rounded-lg overflow-x-auto border border-gray-800 mb-6"><code>{
  "success": false,
  "error": "Error message description",
  "valid": false
}</code></pre>
                    
                    <h4 class="text-lg font-semibold text-white mb-3">Common Errors</h4>
                    <ul class="text-gray-400 space-y-2">
                        <li><strong class="text-gray-300">Project not found</strong>: The specified project ID doesn't exist</li>
                        <li><strong class="text-gray-300">License key not found</strong>: The license key doesn't exist for the project</li>
                        <li><strong class="text-gray-300">Invalid API token</strong>: The API token is invalid or expired</li>
                        <li><strong class="text-gray-300">License key already exists</strong>: Attempting to add a duplicate license key</li>
                    </ul>
                </section>
                
                <div class="bg-blue-900/20 border border-blue-800 rounded-lg p-6">
                    <h5 class="text-lg font-semibold text-blue-300 mb-3">Need Help?</h5>
                    <p class="text-blue-300">If you need assistance with the API, please check our examples or create an account to test the endpoints in your <a href="{{ url_for('profile') }}" class="text-blue-400 hover:text-blue-300 underline">profile page</a>.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation links and sections
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section[id], div[id]');

    // Function to remove active class from all nav links with smooth transition
    function removeActiveClasses() {
        navLinks.forEach(link => {
            if (link.classList.contains('active')) {
                // Add a brief fade-out effect before removing active class
                link.style.transition = 'all 0.2s ease-out';
                link.classList.remove('active');
            }
        });
    }

    // Function to add active class to current nav link with smooth transition and pulse effect
    function addActiveClass(targetId) {
        const activeLink = document.querySelector(`[data-target="${targetId}"]`);
        if (activeLink && !activeLink.classList.contains('active')) {
            // Add a brief delay to ensure smooth transition
            setTimeout(() => {
                activeLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                activeLink.classList.add('active');

                // Add pulse effect for newly activated item
                activeLink.classList.add('pulse');

                // Remove pulse class after animation completes
                setTimeout(() => {
                    activeLink.classList.remove('pulse');
                }, 600);
            }, 50);
        }
    }

    // Function to get the current section based on scroll position
    function getCurrentSection() {
        const scrollPosition = window.scrollY + 100; // Offset for better UX

        for (let i = sections.length - 1; i >= 0; i--) {
            const section = sections[i];
            const sectionTop = section.offsetTop;

            if (scrollPosition >= sectionTop) {
                return section.id;
            }
        }

        return sections[0]?.id || 'overview';
    }

    // Function to update active navigation
    function updateActiveNav() {
        const currentSection = getCurrentSection();
        removeActiveClasses();
        addActiveClass(currentSection);
    }

    // Throttle function to limit scroll event frequency
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    // Add scroll event listener with throttling
    window.addEventListener('scroll', throttle(updateActiveNav, 100));

    // Set initial active state
    updateActiveNav();

    // Add smooth scrolling to navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for sticky header
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });

                // Update URL hash
                history.pushState(null, null, `#${targetId}`);

                // Update active state immediately
                setTimeout(() => {
                    removeActiveClasses();
                    addActiveClass(targetId);
                }, 100);
            }
        });
    });

    // Handle direct hash navigation (e.g., page refresh with hash)
    if (window.location.hash) {
        const hashId = window.location.hash.substring(1);
        const targetElement = document.getElementById(hashId);
        if (targetElement) {
            setTimeout(() => {
                const offsetTop = targetElement.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                removeActiveClasses();
                addActiveClass(hashId);
            }, 100);
        }
    }
});
</script>

{% endblock %}