from datetime import datetime
import secrets
import string

from database import db

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Relationships
    licenses = db.relationship('License', backref='project', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Project {self.name}>'

class License(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(64), unique=True, nullable=False)
    project_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON><PERSON>('project.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    used_at = db.Column(db.DateTime)
    used_by_ip = db.Column(db.String(45))  # IPv6 support
    used_by_hwid = db.Column(db.String(255))
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<License {self.license_key}>'

class LuxLicense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(64), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    used_at = db.Column(db.DateTime)
    used_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    used_by_ip = db.Column(db.String(45))
    duration_days = db.Column(db.Integer, default=30)  # Default 30 days
    notes = db.Column(db.Text)
    
    # Relationship
    used_by_user = db.relationship('User', backref='used_lux_licenses', foreign_keys=[used_by_user_id])
    
    def __init__(self, **kwargs):
        super(LuxLicense, self).__init__(**kwargs)
        if not self.license_key:
            self.license_key = self.generate_license_key()
    
    def generate_license_key(self):
        """Generate a unique license key"""
        while True:
            # Generate a random key with format: XXXX-XXXX-XXXX-XXXX
            key_parts = []
            for _ in range(4):
                part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
                key_parts.append(part)
            
            license_key = '-'.join(key_parts)
            
            # Check if key already exists
            if not LuxLicense.query.filter_by(license_key=license_key).first():
                return license_key
    
    def is_used(self):
        """Check if license has been used"""
        return self.used_at is not None
    
    def use_license(self, user_id, ip_address):
        """Mark license as used"""
        if self.is_used():
            return False, "License already used"
        
        if not self.is_active:
            return False, "License is not active"
        
        self.used_at = datetime.utcnow()
        self.used_by_user_id = user_id
        self.used_by_ip = ip_address
        
        return True, "License activated successfully"
    
    def __repr__(self):
        return f'<LuxLicense {self.license_key}>'
