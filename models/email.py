from datetime import datetime
import json
import uuid
from werkzeug.security import generate_password_hash, check_password_hash
import base64

from database import db

class EmailAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>ey('user.id'), nullable=False)

    # Email Configuration
    email_address = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    smtp_password = db.Column(db.Text, nullable=True)  # Base64 encoded password for SMTP/IMAP
    display_name = db.Column(db.String(100))

    # Storage and Limits
    storage_used_mb = db.Column(db.Integer, default=0)
    storage_limit_mb = db.Column(db.Integer, default=10240)  # 10GB default

    # Status
    is_active = db.Column(db.<PERSON>, default=True)
    is_verified = db.Column(db.Boolean, default=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Relationships
    messages = db.relationship('EmailMessage', backref='account', lazy=True, cascade='all, delete-orphan')

    @property
    def received_emails(self):
        """Get received emails for this account"""
        return EmailMessage.query.filter_by(
            account_id=self.id,
            folder='INBOX'
        ).all()

    @property
    def sent_emails(self):
        """Get sent emails for this account"""
        return EmailMessage.query.filter_by(
            account_id=self.id,
            folder='SENT'
        ).all()

    def set_password(self, password):
        """Set password hash and SMTP password"""
        self.password_hash = generate_password_hash(password)
        # Store base64 encoded password for SMTP/IMAP authentication
        self.smtp_password = base64.b64encode(password.encode()).decode()

    def check_password(self, password):
        """Check password"""
        return check_password_hash(self.password_hash, password)

    def get_smtp_password(self):
        """Get decoded SMTP password"""
        if self.smtp_password:
            return base64.b64decode(self.smtp_password.encode()).decode()
        return None

    def can_receive_email(self, size_mb):
        """Check if account can receive email of given size"""
        return (self.storage_used_mb + size_mb) <= self.storage_limit_mb

    def recalculate_storage(self):
        """Recalculate storage usage"""
        total_bytes = sum(msg.size_bytes or 0 for msg in self.messages)
        self.storage_used_mb = int(total_bytes / (1024 * 1024))

    def __repr__(self):
        return f'<EmailAccount {self.email_address}>'

class EmailMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    account_id = db.Column(db.Integer, db.ForeignKey('email_account.id'), nullable=False)

    # Message Headers
    message_id = db.Column(db.String(255), unique=True, nullable=False)
    subject = db.Column(db.String(500))
    sender = db.Column(db.String(255), nullable=False)
    recipients = db.Column(db.Text)  # JSON array of recipients
    cc = db.Column(db.Text)  # JSON array of CC recipients
    bcc = db.Column(db.Text)  # JSON array of BCC recipients

    # Message Content
    body_text = db.Column(db.Text)
    body_html = db.Column(db.Text)
    attachments = db.Column(db.Text)  # JSON array of attachment info

    # Message Metadata
    folder = db.Column(db.String(50), default='INBOX')  # INBOX, SENT, DRAFT, TRASH
    is_read = db.Column(db.Boolean, default=False)
    is_starred = db.Column(db.Boolean, default=False)
    size_bytes = db.Column(db.Integer, default=0)

    # Timestamps
    sent_at = db.Column(db.DateTime)
    received_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, **kwargs):
        super(EmailMessage, self).__init__(**kwargs)
        if not self.message_id:
            self.message_id = self.generate_message_id()

    def generate_message_id(self):
        """Generate unique message ID"""
        return f"<{uuid.uuid4()}@lxnd.cloud>"

    def get_size_mb(self):
        """Get message size in MB"""
        return round(self.size_bytes / (1024 * 1024), 2)

    def get_size_formatted(self):
        """Get message size formatted as KB or MB"""
        if not self.size_bytes:
            return "0 B"

        if self.size_bytes < 1024:
            return f"{self.size_bytes} B"
        elif self.size_bytes < 1024 * 1024:
            return f"{round(self.size_bytes / 1024, 1)} KB"
        else:
            return f"{round(self.size_bytes / (1024 * 1024), 2)} MB"

    def get_recipients_list(self):
        """Get recipients as list"""
        try:
            return json.loads(self.recipients) if self.recipients else []
        except:
            return []

    def set_recipients_list(self, recipients):
        """Set recipients from list"""
        self.recipients = json.dumps(recipients)

    def get_attachments_list(self):
        """Get attachments as list"""
        try:
            return json.loads(self.attachments) if self.attachments else []
        except:
            return []

    def set_attachments_list(self, attachments):
        """Set attachments from list"""
        self.attachments = json.dumps(attachments)

    def __repr__(self):
        return f'<EmailMessage {self.subject}>'

class EmailTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    template_type = db.Column(db.String(50), nullable=False)  # welcome, notification, custom
    subject = db.Column(db.String(500))
    body_text = db.Column(db.Text)
    body_html = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def render_template(self, variables):
        """Render template with variables"""
        rendered_subject = self.subject or ''
        rendered_body_text = self.body_text or ''
        rendered_body_html = self.body_html or ''

        # Replace variables in template
        for key, value in variables.items():
            placeholder = '{' + key + '}'
            rendered_subject = rendered_subject.replace(placeholder, str(value))
            rendered_body_text = rendered_body_text.replace(placeholder, str(value))
            rendered_body_html = rendered_body_html.replace(placeholder, str(value))

        return {
            'subject': rendered_subject,
            'body_text': rendered_body_text,
            'body_html': rendered_body_html
        }

    def __repr__(self):
        return f'<EmailTemplate {self.name}>'
