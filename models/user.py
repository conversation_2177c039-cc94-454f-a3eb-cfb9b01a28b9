from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

from database import db

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    api_token = db.Column(db.String(64), unique=True, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_admin = db.Column(db.<PERSON>, default=False)
    is_moderator = db.Column(db.Bo<PERSON>, default=False)
    is_banned = db.Column(db.<PERSON>, default=False)
    is_lux = db.Column(db.<PERSON>, default=False)
    lux_expires_at = db.Column(db.DateTime, nullable=True)  # None = permanent
    upload_limit_mb = db.Column(db.Integer, default=15360)  # 15GB in MB
    max_projects = db.Column(db.Integer, default=3)
    max_keys_per_project = db.Column(db.Integer, default=100)
    
    # Relationships
    projects = db.relationship('Project', backref='owner', lazy=True)
    files = db.relationship('FileUpload', backref='owner', lazy=True)
    email_accounts = db.relationship('EmailAccount', backref='owner', lazy=True)

    # Discord Integration
    discord_id = db.Column(db.String(20), unique=True, nullable=True)
    discord_username = db.Column(db.String(100), nullable=True)
    discord_avatar = db.Column(db.String(255), nullable=True)
    discord_linked_at = db.Column(db.DateTime, nullable=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def generate_api_token(self):
        self.api_token = secrets.token_urlsafe(32)
        return self.api_token
    
    def get_used_storage_mb(self):
        total_bytes = sum(file.file_size for file in self.files)
        return total_bytes / (1024 * 1024)  # Convert bytes to MB

    def get_storage_used_formatted(self):
        """Get storage used formatted as KB/MB/GB based on actual bytes"""
        total_bytes = sum(file.file_size for file in self.files)

        if total_bytes < 1024:
            return f"{total_bytes} B"
        elif total_bytes < 1024 * 1024:
            return f"{round(total_bytes / 1024, 1)} KB"
        elif total_bytes < 1024 * 1024 * 1024:
            return f"{round(total_bytes / (1024 * 1024), 1)} MB"
        else:
            return f"{round(total_bytes / (1024 * 1024 * 1024), 2)} GB"
    
    def get_storage_percent(self):
        if self.upload_limit_mb == 0:  # Unlimited
            return 0
        used = self.get_used_storage_mb()
        return min(100, int((used / self.upload_limit_mb) * 100))
    
    def can_upload(self, file_size_bytes):
        if self.upload_limit_mb == 0:  # Unlimited
            return True
        current_usage_mb = self.get_used_storage_mb()
        file_size_mb = file_size_bytes / (1024 * 1024)
        return (current_usage_mb + file_size_mb) <= self.upload_limit_mb
    
    def can_create_project(self):
        if self.max_projects == 0:  # Unlimited
            return True
        return len([p for p in self.projects]) < self.max_projects
    
    def can_create_license(self, project):
        if self.max_keys_per_project == 0:  # Unlimited
            return True
        return len([l for l in project.licenses]) < self.max_keys_per_project
    
    def is_lux_active(self):
        """Check if user has active Lux subscription"""
        if not self.is_lux:
            return False
        if self.lux_expires_at is None:  # Permanent
            return True
        return self.lux_expires_at > datetime.utcnow()
    
    def get_lux_status(self):
        """Get Lux subscription status"""
        if not self.is_lux:
            return "Not subscribed"
        if self.lux_expires_at is None:
            return "Permanent"
        if self.lux_expires_at > datetime.utcnow():
            return f"Expires {self.lux_expires_at.strftime('%Y-%m-%d')}"
        return "Expired"

    def get_max_projects_limit(self):
        """Get maximum projects limit for user"""
        return self.max_projects

    def get_max_keys_per_project_limit(self):
        """Get maximum keys per project limit for user"""
        return self.max_keys_per_project

    def get_upload_limit_formatted(self):
        """Get upload limit formatted"""
        if self.upload_limit_mb == 0:
            return "Unlimited"
        if self.upload_limit_mb >= 1024:
            return f"{self.upload_limit_mb / 1024:.1f} GB"
        return f"{self.upload_limit_mb} MB"

    def __repr__(self):
        return f'<User {self.username}>'
