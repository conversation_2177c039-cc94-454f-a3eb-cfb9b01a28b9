from datetime import datetime
import secrets
import string

from database import db

class FileUpload(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('user.id'), nullable=False)
    file_id = db.Column(db.String(32), unique=True, nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    mime_type = db.Column(db.String(100))
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    download_count = db.Column(db.Integer, default=0)
    public = db.Column(db.<PERSON>, default=False)
    
    def __init__(self, **kwargs):
        super(FileUpload, self).__init__(**kwargs)
        if not self.file_id:
            self.file_id = self.generate_file_id()
    
    def generate_file_id(self):
        """Generate a unique file ID"""
        while True:
            # Generate a random ID with letters and numbers
            file_id = ''.join(secrets.choice(string.ascii_letters + string.digits + '-_') for _ in range(22))
            
            # Check if ID already exists
            if not FileUpload.query.filter_by(file_id=file_id).first():
                return file_id
    
    def get_size_formatted(self):
        """Get file size formatted as KB/MB/GB"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{round(self.file_size / 1024, 1)} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{round(self.file_size / (1024 * 1024), 1)} MB"
        else:
            return f"{round(self.file_size / (1024 * 1024 * 1024), 2)} GB"
    
    def __repr__(self):
        return f'<FileUpload {self.original_filename}>'
