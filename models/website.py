from datetime import datetime

from database import db

class Website(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    
    # Website Configuration
    subdomain = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # SSL Configuration
    ssl_enabled = db.Column(db.<PERSON>, default=True)
    ssl_cert_path = db.Column(db.String(255))
    ssl_key_path = db.Column(db.String(255))
    
    # SFTP Configuration
    sftp_enabled = db.Column(db.Boolean, default=True)
    sftp_username = db.Column(db.String(50))
    sftp_password = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('websites', lazy=True))
    files = db.relationship('WebsiteFile', backref='website', lazy=True, cascade='all, delete-orphan')
    
    def get_domain(self):
        """Get full domain name"""
        return f"{self.subdomain}.lxnd.cloud"
    
    def get_file_count(self):
        """Get number of files"""
        return len(self.files)
    
    def get_total_size(self):
        """Get total size of all files in bytes"""
        return sum(file.file_size for file in self.files)
    
    def get_total_size_formatted(self):
        """Get total size formatted"""
        total_bytes = self.get_total_size()
        
        if total_bytes < 1024:
            return f"{total_bytes} B"
        elif total_bytes < 1024 * 1024:
            return f"{round(total_bytes / 1024, 1)} KB"
        elif total_bytes < 1024 * 1024 * 1024:
            return f"{round(total_bytes / (1024 * 1024), 1)} MB"
        else:
            return f"{round(total_bytes / (1024 * 1024 * 1024), 2)} GB"
    
    def __repr__(self):
        return f'<Website {self.subdomain}.lxnd.cloud>'

class WebsiteFile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    website_id = db.Column(db.Integer, db.ForeignKey('website.id'), nullable=False)
    
    # File Information
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)  # Full path on disk
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    mime_type = db.Column(db.String(100))
    is_index = db.Column(db.Boolean, default=False)  # Mark as index.html
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_size_formatted(self):
        """Get file size formatted"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{round(self.file_size / 1024, 1)} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{round(self.file_size / (1024 * 1024), 1)} MB"
        else:
            return f"{round(self.file_size / (1024 * 1024 * 1024), 2)} GB"
    
    def __repr__(self):
        return f'<WebsiteFile {self.filename}>'
