from datetime import datetime

from database import db

class DiscordSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    
    # Bot Configuration
    bot_token = db.Column(db.String(255))
    bot_prefix = db.Column(db.String(10), default='!')
    bot_status = db.Column(db.String(100), default='LXND License Management')
    bot_activity_type = db.Column(db.String(20), default='watching')  # playing, streaming, listening, watching
    
    # Server Configuration
    guild_id = db.Column(db.String(20))
    
    # Channel Configuration
    log_channel_id = db.Column(db.String(20))
    welcome_channel_id = db.Column(db.String(20))
    verification_channel_id = db.Column(db.String(20))
    
    # Role Configuration
    verified_role_id = db.Column(db.String(20))
    lux_role_id = db.Column(db.String(20))
    admin_role_id = db.Column(db.String(20))
    
    # Ticket System
    ticket_category_id = db.Column(db.String(20))
    ticket_log_channel_id = db.Column(db.String(20))
    
    # Features
    verification_enabled = db.Column(db.Boolean, default=True)
    ticket_system_enabled = db.Column(db.Boolean, default=True)
    auto_role_enabled = db.Column(db.Boolean, default=True)
    
    # Messages
    welcome_message = db.Column(db.Text, default='Welcome to the server!')
    verification_message = db.Column(db.Text, default='Please verify your account to access the server.')
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @classmethod
    def get_settings(cls):
        """Get current Discord settings (singleton pattern)"""
        settings = cls.query.first()
        if not settings:
            settings = cls()
            db.session.add(settings)
            db.session.commit()
        return settings
    
    def __repr__(self):
        return f'<DiscordSettings {self.id}>'
