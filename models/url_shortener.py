from datetime import datetime
import secrets
import string

from database import db

class ShortenedURL(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>ey('user.id'), nullable=True)  # Can be anonymous
    
    # URL Information
    short_code = db.Column(db.String(10), unique=True, nullable=False)
    original_url = db.Column(db.Text, nullable=False)
    title = db.Column(db.String(200))
    description = db.Column(db.Text)
    
    # Security and Access
    password_hash = db.Column(db.String(255))  # Optional password protection
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    expires_at = db.Column(db.DateTime)  # Optional expiration
    
    # Analytics
    click_count = db.Column(db.Integer, default=0)
    last_clicked = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='shortened_urls')
    
    def __init__(self, **kwargs):
        super(ShortenedURL, self).__init__(**kwargs)
        if not self.short_code:
            self.short_code = self.generate_short_code()
    
    def generate_short_code(self):
        """Generate a unique short code"""
        while True:
            # Generate a random code with letters and numbers
            code = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(6))
            
            # Check if code already exists
            if not ShortenedURL.query.filter_by(short_code=code).first():
                return code
    
    def is_expired(self):
        """Check if URL has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_password_protected(self):
        """Check if URL is password protected"""
        return self.password_hash is not None
    
    def get_short_url(self):
        """Get the full short URL"""
        return f"https://lxnd.cloud/s/{self.short_code}"
    
    def increment_clicks(self):
        """Increment click count and update last clicked"""
        self.click_count += 1
        self.last_clicked = datetime.utcnow()
    
    def __repr__(self):
        return f'<ShortenedURL {self.short_code}>'
