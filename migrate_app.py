#!/usr/bin/env python3
"""
Migration script to transition from monolithic app.py to modular structure
"""

import os
import shutil
import sys
from datetime import datetime

def backup_original():
    """Create backup of original app.py"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"app_backup_{timestamp}.py"
    
    if os.path.exists('app.py'):
        shutil.copy2('app.py', backup_name)
        print(f"✅ Created backup: {backup_name}")
        return backup_name
    else:
        print("❌ Original app.py not found")
        return None

def create_directory_structure():
    """Create the new directory structure"""
    directories = [
        'models',
        'routes', 
        'utils',
        'languages',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def test_new_structure():
    """Test if the new modular structure works"""
    print("\n🧪 Testing new application structure...")
    
    try:
        # Try to import the new app
        from app_new import create_app
        
        # Create test app
        app = create_app('testing')
        
        with app.app_context():
            # Test database creation
            from database import db
            db.create_all()
            
            print("✅ Database initialization successful")
            
            # Test model imports
            from models.user import User
            from models.project import Project
            from models.email import EmailAccount
            
            print("✅ Model imports successful")
            
            # Test route imports
            from routes.main import main_bp
            from routes.auth import auth_bp
            
            print("✅ Route imports successful")
            
        print("✅ New structure test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def switch_to_new_app():
    """Switch from old app.py to new modular structure"""
    
    print("\n🔄 Switching to new application structure...")
    
    # Rename old app.py
    if os.path.exists('app.py'):
        os.rename('app.py', 'app_old.py')
        print("✅ Renamed app.py to app_old.py")
    
    # Rename new app to main
    if os.path.exists('app_new.py'):
        os.rename('app_new.py', 'app.py')
        print("✅ Activated new modular app.py")
    
    print("✅ Migration completed successfully!")

def rollback_migration():
    """Rollback to original app.py if something goes wrong"""
    
    print("\n🔄 Rolling back migration...")
    
    # Restore original app.py
    if os.path.exists('app_old.py'):
        if os.path.exists('app.py'):
            os.rename('app.py', 'app_new.py')
        os.rename('app_old.py', 'app.py')
        print("✅ Restored original app.py")
    
    print("✅ Rollback completed")

def show_migration_summary():
    """Show summary of the migration"""
    
    print("\n" + "="*60)
    print("📋 MIGRATION SUMMARY")
    print("="*60)
    
    print("\n📁 New Structure:")
    print("  ├── app.py                 # Main application entry point")
    print("  ├── config.py              # Configuration management")
    print("  ├── database.py            # Database initialization")
    print("  ├── models/                # Database models")
    print("  │   ├── user.py")
    print("  │   ├── project.py")
    print("  │   ├── email.py")
    print("  │   └── ...")
    print("  ├── routes/                # Route blueprints")
    print("  │   ├── main.py")
    print("  │   ├── auth.py")
    print("  │   └── ...")
    print("  └── utils/                 # Utility modules")
    print("      ├── forms.py")
    print("      ├── decorators.py")
    print("      └── ...")
    
    print("\n✨ Benefits:")
    print("  • Better code organization")
    print("  • Easier maintenance")
    print("  • Improved scalability")
    print("  • Cleaner imports")
    print("  • Modular development")
    
    print("\n🔧 Next Steps:")
    print("  1. Test the application thoroughly")
    print("  2. Move remaining routes to blueprints")
    print("  3. Add more utility functions")
    print("  4. Implement proper logging")
    print("  5. Add comprehensive tests")
    
    print("\n📝 Files to Review:")
    print("  • app_old.py (original backup)")
    print("  • Any custom modifications")
    print("  • Environment-specific configs")

def main():
    """Main migration function"""
    
    print("🚀 LXND Application Migration Tool")
    print("="*50)
    
    # Check if migration is needed
    if not os.path.exists('app.py'):
        print("❌ No app.py found. Nothing to migrate.")
        return
    
    if os.path.exists('models') and os.path.exists('routes'):
        print("✅ Modular structure already exists.")
        
        choice = input("Do you want to test the new structure? (y/n): ").lower()
        if choice == 'y':
            if test_new_structure():
                print("✅ New structure is working correctly!")
            else:
                print("❌ New structure has issues. Please check the errors above.")
        return
    
    print("\n📋 Migration Plan:")
    print("  1. Backup original app.py")
    print("  2. Create new directory structure")
    print("  3. Test new modular structure")
    print("  4. Switch to new structure")
    
    choice = input("\nProceed with migration? (y/n): ").lower()
    if choice != 'y':
        print("❌ Migration cancelled.")
        return
    
    try:
        # Step 1: Backup
        backup_file = backup_original()
        if not backup_file:
            return
        
        # Step 2: Create structure
        create_directory_structure()
        
        # Step 3: Test new structure
        if not test_new_structure():
            print("❌ Migration failed during testing.")
            choice = input("Do you want to continue anyway? (y/n): ").lower()
            if choice != 'y':
                return
        
        # Step 4: Switch to new structure
        switch_to_new_app()
        
        # Show summary
        show_migration_summary()
        
        print(f"\n✅ Migration completed successfully!")
        print(f"📁 Original app.py backed up as: {backup_file}")
        print("🚀 You can now start the application with: python app.py")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print("🔄 Attempting rollback...")
        rollback_migration()
        sys.exit(1)

if __name__ == '__main__':
    main()
