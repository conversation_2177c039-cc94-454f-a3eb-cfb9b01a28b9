const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

class Database {
    constructor() {
        this.pool = null;
        this.config = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'lxnd_app',
            password: process.env.DB_PASSWORD || 'LxndApp2024!',
            database: process.env.DB_NAME || 'lxnd_main',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            acquireTimeout: 60000,
            timeout: 60000,
            reconnect: true,
            charset: 'utf8mb4'
        };
    }

    async connect() {
        try {
            this.pool = mysql.createPool(this.config);
            
            // Test the connection
            const connection = await this.pool.getConnection();
            await connection.ping();
            connection.release();
            
            logger.info('Successfully connected to MySQL database');
            return true;
        } catch (error) {
            logger.error('Failed to connect to MySQL database:', error);
            return false;
        }
    }

    async query(sql, params = []) {
        try {
            const [rows] = await this.pool.execute(sql, params);
            return rows;
        } catch (error) {
            logger.error('Database query error:', error);
            throw error;
        }
    }

    async getUserByDiscordId(discordId) {
        const sql = 'SELECT * FROM user WHERE discord_id = ?';
        const rows = await this.query(sql, [discordId]);
        return rows[0] || null;
    }

    async updateUserDiscordInfo(userId, discordId, discordUsername, discordAvatar) {
        const sql = `
            UPDATE user 
            SET discord_id = ?, discord_username = ?, discord_avatar = ?, discord_linked_at = NOW() 
            WHERE id = ?
        `;
        return await this.query(sql, [discordId, discordUsername, discordAvatar, userId]);
    }

    async createDiscordRoleUpdate(discordId, roleType, action) {
        const sql = `
            INSERT INTO discord_role_updates (discord_id, role_type, action, timestamp, processed) 
            VALUES (?, ?, ?, NOW(), FALSE)
        `;
        return await this.query(sql, [discordId, roleType, action]);
    }

    async getPendingRoleUpdates() {
        const sql = 'SELECT * FROM discord_role_updates WHERE processed = FALSE ORDER BY timestamp ASC';
        return await this.query(sql);
    }

    async markRoleUpdateProcessed(updateId) {
        const sql = 'UPDATE discord_role_updates SET processed = TRUE, processed_at = NOW() WHERE id = ?';
        return await this.query(sql, [updateId]);
    }

    async markRoleUpdateError(updateId, errorMessage) {
        const sql = 'UPDATE discord_role_updates SET processed = TRUE, processed_at = NOW(), error_message = ? WHERE id = ?';
        return await this.query(sql, [errorMessage, updateId]);
    }

    async logCommand(discordUserId, discordUsername, commandName, options, guildId, channelId, success, errorMessage, executionTime) {
        const sql = `
            INSERT INTO discord_command_logs
            (discord_user_id, discord_username, command_name, command_options, guild_id, channel_id, success, error_message, execution_time_ms)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        return await this.query(sql, [
            discordUserId,
            discordUsername,
            commandName,
            options ? JSON.stringify(options) : null,
            guildId,
            channelId,
            success,
            errorMessage,
            executionTime
        ]);
    }

    async createVerificationSession(sessionToken, discordUserId, discordUsername, state, expiresAt) {
        const sql = `
            INSERT INTO discord_verification_sessions
            (session_token, discord_user_id, discord_username, state, expires_at)
            VALUES (?, ?, ?, ?, ?)
        `;
        return await this.query(sql, [sessionToken, discordUserId, discordUsername, state, expiresAt]);
    }

    async getVerificationSession(sessionToken) {
        const sql = 'SELECT * FROM discord_verification_sessions WHERE session_token = ? AND expires_at > NOW() AND completed = FALSE';
        const rows = await this.query(sql, [sessionToken]);
        return rows[0] || null;
    }

    async completeVerificationSession(sessionToken, linkedUserId) {
        const sql = `
            UPDATE discord_verification_sessions
            SET completed = TRUE, completed_at = NOW(), linked_user_id = ?
            WHERE session_token = ?
        `;
        return await this.query(sql, [linkedUserId, sessionToken]);
    }

    async getBotSettings() {
        const sql = 'SELECT * FROM bot_settings WHERE id = 1';
        const rows = await this.query(sql);
        return rows[0] || null;
    }

    async updateServerStats(guildId, totalMembers, verifiedMembers, luxMembers, adminMembers, onlineMembers) {
        const sql = `
            INSERT INTO discord_server_stats
            (guild_id, total_members, verified_members, lux_members, admin_members, online_members)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        return await this.query(sql, [guildId, totalMembers, verifiedMembers, luxMembers, adminMembers, onlineMembers]);
    }

    async getVerifiedUsersCount() {
        const sql = 'SELECT COUNT(*) as count FROM user WHERE discord_id IS NOT NULL';
        const rows = await this.query(sql);
        return rows[0].count;
    }

    async getLuxUsersCount() {
        const sql = 'SELECT COUNT(*) as count FROM user WHERE discord_id IS NOT NULL AND is_lux = TRUE AND (lux_expires_at IS NULL OR lux_expires_at > NOW())';
        const rows = await this.query(sql);
        return rows[0].count;
    }

    async close() {
        if (this.pool) {
            await this.pool.end();
            logger.info('Database connection closed');
        }
    }
}

module.exports = new Database();
