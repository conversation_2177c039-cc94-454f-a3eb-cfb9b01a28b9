require('dotenv').config();
const database = require('./config/database');
const logger = require('./utils/logger');

async function runTests() {
    console.log('🧪 Running LXND Discord Bot Setup Tests...\n');
    
    let testsPassed = 0;
    let testsFailed = 0;
    
    // Test 1: Environment Variables
    console.log('📋 Test 1: Environment Variables');
    try {
        const requiredEnvVars = [
            'DISCORD_TOKEN',
            'DISCORD_CLIENT_ID',
            'DISCORD_CLIENT_SECRET',
            'GUILD_ID',
            'VERIFICATION_ROLE_ID',
            'DB_HOST',
            'DB_USER',
            'DB_PASSWORD',
            'DB_NAME'
        ];
        
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length === 0) {
            console.log('   ✅ All required environment variables are set');
            testsPassed++;
        } else {
            console.log(`   ❌ Missing environment variables: ${missingVars.join(', ')}`);
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Error checking environment variables: ${error.message}`);
        testsFailed++;
    }
    
    // Test 2: Database Connection
    console.log('\n📋 Test 2: Database Connection');
    try {
        const connected = await database.connect();
        if (connected) {
            console.log('   ✅ Database connection successful');
            testsPassed++;
        } else {
            console.log('   ❌ Database connection failed');
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Database connection error: ${error.message}`);
        testsFailed++;
    }
    
    // Test 3: Database Tables
    console.log('\n📋 Test 3: Database Tables');
    try {
        const tables = [
            'user',
            'discord_role_updates',
            'bot_settings',
            'discord_command_logs',
            'discord_verification_sessions',
            'discord_server_stats'
        ];
        
        let allTablesExist = true;
        for (const table of tables) {
            try {
                await database.query(`SELECT 1 FROM ${table} LIMIT 1`);
                console.log(`   ✅ Table '${table}' exists`);
            } catch (error) {
                console.log(`   ❌ Table '${table}' missing or inaccessible`);
                allTablesExist = false;
            }
        }
        
        if (allTablesExist) {
            testsPassed++;
        } else {
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Error checking database tables: ${error.message}`);
        testsFailed++;
    }
    
    // Test 4: Discord Columns in User Table
    console.log('\n📋 Test 4: Discord Columns in User Table');
    try {
        const discordColumns = [
            'discord_id',
            'discord_username',
            'discord_avatar',
            'discord_linked_at'
        ];
        
        let allColumnsExist = true;
        for (const column of discordColumns) {
            try {
                await database.query(`SELECT ${column} FROM user LIMIT 1`);
                console.log(`   ✅ Column 'user.${column}' exists`);
            } catch (error) {
                console.log(`   ❌ Column 'user.${column}' missing`);
                allColumnsExist = false;
            }
        }
        
        if (allColumnsExist) {
            testsPassed++;
        } else {
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Error checking user table columns: ${error.message}`);
        testsFailed++;
    }
    
    // Test 5: Bot Settings
    console.log('\n📋 Test 5: Bot Settings');
    try {
        const settings = await database.getBotSettings();
        if (settings) {
            console.log('   ✅ Bot settings found');
            console.log(`   📊 Embed Color: ${settings.embed_color}`);
            console.log(`   📊 Footer: ${settings.global_footer}`);
            console.log(`   📊 Status: ${settings.status}`);
            testsPassed++;
        } else {
            console.log('   ❌ Bot settings not found');
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Error checking bot settings: ${error.message}`);
        testsFailed++;
    }
    
    // Test 6: Database Operations
    console.log('\n📋 Test 6: Database Operations');
    try {
        // Test role update creation
        await database.createDiscordRoleUpdate('test_user_123', 'test_role', 'add');
        console.log('   ✅ Role update creation works');
        
        // Test pending role updates retrieval
        const pendingUpdates = await database.getPendingRoleUpdates();
        console.log(`   ✅ Pending role updates retrieval works (${pendingUpdates.length} pending)`);
        
        // Clean up test data
        await database.query('DELETE FROM discord_role_updates WHERE discord_id = ?', ['test_user_123']);
        console.log('   ✅ Test data cleanup successful');
        
        testsPassed++;
    } catch (error) {
        console.log(`   ❌ Error testing database operations: ${error.message}`);
        testsFailed++;
    }
    
    // Test 7: Discord Token Validation
    console.log('\n📋 Test 7: Discord Token Validation');
    try {
        const token = process.env.DISCORD_TOKEN;
        if (token && token.length > 50 && token.includes('.')) {
            console.log('   ✅ Discord token format appears valid');
            testsPassed++;
        } else {
            console.log('   ❌ Discord token format appears invalid');
            testsFailed++;
        }
    } catch (error) {
        console.log(`   ❌ Error validating Discord token: ${error.message}`);
        testsFailed++;
    }
    
    // Test 8: File Permissions
    console.log('\n📋 Test 8: File Permissions');
    try {
        const fs = require('fs');
        const path = require('path');
        
        // Check if logs directory is writable
        const logsDir = path.join(__dirname, 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
        
        // Test write permission
        const testFile = path.join(logsDir, 'test.txt');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        
        console.log('   ✅ File permissions are correct');
        testsPassed++;
    } catch (error) {
        console.log(`   ❌ File permission error: ${error.message}`);
        testsFailed++;
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Summary');
    console.log('='.repeat(50));
    console.log(`✅ Tests Passed: ${testsPassed}`);
    console.log(`❌ Tests Failed: ${testsFailed}`);
    console.log(`📈 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`);
    
    if (testsFailed === 0) {
        console.log('\n🎉 All tests passed! The bot is ready to run.');
        console.log('\n📋 Next steps:');
        console.log('   1. Run: npm run deploy');
        console.log('   2. Run: npm start');
    } else {
        console.log('\n⚠️  Some tests failed. Please fix the issues before running the bot.');
        process.exit(1);
    }
    
    // Close database connection
    await database.close();
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = { runTests };
