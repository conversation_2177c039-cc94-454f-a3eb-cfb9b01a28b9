# LXND Discord Bot - Deployment Guide

## Produktions-Deployment

### 1. Server-Vorbereitung

**System-Anforderungen:**
- Ubuntu 20.04+ oder CentOS 8+
- Node.js 18+
- MySQL 8.0+
- Mindestens 1GB RAM
- 10GB freier Speicherplatz

**Node.js Installation:**
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### 2. Bot-Setup

**Repository klonen:**
```bash
cd /opt
sudo git clone <repository-url> lxnd-discord-bot
cd lxnd-discord-bot/discord-bot
sudo chown -R $USER:$USER .
```

**Abhängigkeiten installieren:**
```bash
npm install --production
```

**Umgebungsvariablen konfigurieren:**
```bash
cp .env.example .env
nano .env
```

**Wichtige Produktions-Einstellungen:**
```env
# Discord Bot Configuration
DISCORD_TOKEN=your_production_bot_token
DISCORD_CLIENT_ID=your_client_id
DISCORD_CLIENT_SECRET=your_client_secret

# Server Configuration
GUILD_ID=1396070849664253952
VERIFICATION_ROLE_ID=1396072581802491934

# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=lxnd_app
DB_PASSWORD=your_secure_password
DB_NAME=lxnd_main

# Web Server Configuration
PORT=3001
OAUTH_REDIRECT_URI=https://lxnd.cloud/auth/discord/callback

# Logging
LOG_LEVEL=info
NODE_ENV=production
```

### 3. Datenbank-Setup

```bash
npm run setup
npm test
```

### 4. Commands deployen

```bash
npm run deploy
```

### 5. Systemd Service erstellen

**Service-Datei erstellen:**
```bash
sudo nano /etc/systemd/system/lxnd-discord-bot.service
```

**Service-Konfiguration:**
```ini
[Unit]
Description=LXND Discord Bot
After=network.target mysql.service

[Service]
Type=simple
User=lxnd
WorkingDirectory=/opt/lxnd-discord-bot/discord-bot
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=lxnd-discord-bot

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/lxnd-discord-bot/discord-bot/logs

[Install]
WantedBy=multi-user.target
```

**Service aktivieren:**
```bash
sudo systemctl daemon-reload
sudo systemctl enable lxnd-discord-bot
sudo systemctl start lxnd-discord-bot
```

**Status überprüfen:**
```bash
sudo systemctl status lxnd-discord-bot
sudo journalctl -u lxnd-discord-bot -f
```

### 6. Nginx Reverse Proxy (Optional)

**Nginx-Konfiguration:**
```nginx
server {
    listen 80;
    server_name bot.lxnd.cloud;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 7. SSL-Zertifikat (Let's Encrypt)

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d bot.lxnd.cloud
```

### 8. Monitoring und Logs

**Log-Rotation einrichten:**
```bash
sudo nano /etc/logrotate.d/lxnd-discord-bot
```

```
/opt/lxnd-discord-bot/discord-bot/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 lxnd lxnd
    postrotate
        systemctl reload lxnd-discord-bot
    endscript
}
```

**Monitoring-Script:**
```bash
#!/bin/bash
# /opt/lxnd-discord-bot/monitor.sh

BOT_STATUS=$(systemctl is-active lxnd-discord-bot)
if [ "$BOT_STATUS" != "active" ]; then
    echo "Bot is down, restarting..."
    systemctl restart lxnd-discord-bot
    # Optional: Send notification
fi
```

**Crontab für Monitoring:**
```bash
crontab -e
# Add: */5 * * * * /opt/lxnd-discord-bot/monitor.sh
```

### 9. Backup-Strategie

**Datenbank-Backup:**
```bash
#!/bin/bash
# /opt/lxnd-discord-bot/backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups/lxnd-discord-bot"
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u lxnd_app -p lxnd_main > $BACKUP_DIR/database_$DATE.sql

# Bot configuration backup
tar -czf $BACKUP_DIR/bot_config_$DATE.tar.gz /opt/lxnd-discord-bot/discord-bot/.env

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 10. Updates und Wartung

**Bot-Update:**
```bash
cd /opt/lxnd-discord-bot
sudo git pull
cd discord-bot
npm install --production
npm run deploy
sudo systemctl restart lxnd-discord-bot
```

**Gesundheitscheck:**
```bash
# Bot-Status
curl http://localhost:3001/health

# Logs überprüfen
sudo journalctl -u lxnd-discord-bot --since "1 hour ago"

# Systemressourcen
htop
df -h
```

## Troubleshooting

### Häufige Probleme

**Bot startet nicht:**
1. Überprüfe Logs: `sudo journalctl -u lxnd-discord-bot -f`
2. Teste Konfiguration: `npm test`
3. Überprüfe Datenbankverbindung
4. Validiere Discord-Token

**Commands funktionieren nicht:**
1. Commands neu deployen: `npm run deploy`
2. Bot-Berechtigungen in Discord überprüfen
3. Guild-ID und Role-IDs validieren

**Datenbankfehler:**
1. MySQL-Service überprüfen: `sudo systemctl status mysql`
2. Datenbankbenutzer und -berechtigungen überprüfen
3. Netzwerkverbindung testen

**Performance-Probleme:**
1. Systemressourcen überprüfen: `htop`, `free -h`
2. Log-Dateien rotieren
3. Alte Datenbank-Einträge bereinigen

### Support

Bei Problemen:
1. Logs sammeln und analysieren
2. Konfiguration überprüfen
3. Tests ausführen: `npm test`
4. LXND-Support kontaktieren
