const { Events, Embed<PERSON><PERSON>er, ActionRow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
    name: Events.GuildMemberAdd,
    async execute(member) {
        try {
            // Only process members from the configured guild
            if (member.guild.id !== process.env.GUILD_ID) {
                return;
            }

            logger.info(`New member joined: ${member.user.tag} (${member.user.id})`);

            // Create welcome embed
            const welcomeEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`🎉 Welcome to ${member.guild.name}!`)
                .setDescription(`Hello ${member.user}, welcome to our Discord server!`)
                .addFields(
                    {
                        name: '🔗 Get Verified',
                        value: 'Link your Discord account with your LXND account to access exclusive channels and features.',
                        inline: false
                    },
                    {
                        name: '📋 How to get started:',
                        value: '1. Use `/verify` command to start the verification process\n2. Log in with your LXND account\n3. Enjoy access to verified channels!',
                        inline: false
                    },
                    {
                        name: '❓ Need help?',
                        value: 'Feel free to ask questions in our support channels or contact a moderator.',
                        inline: false
                    }
                )
                .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
                .setTimestamp()
                .setFooter({ 
                    text: 'LXND License Management System',
                    iconURL: member.guild.iconURL({ dynamic: true })
                });

            // Create verification button
            const verifyButton = new ButtonBuilder()
                .setLabel('Start Verification')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🔗')
                .setURL(`https://lxnd.cloud/auth/discord?state=${member.user.id}`);

            const row = new ActionRowBuilder()
                .addComponents(verifyButton);

            // Send welcome message to the member
            try {
                await member.send({ 
                    embeds: [welcomeEmbed], 
                    components: [row] 
                });
                logger.info(`Welcome message sent to ${member.user.tag}`);
            } catch (error) {
                logger.warn(`Could not send welcome DM to ${member.user.tag}: ${error.message}`);
                
                // If DM fails, try to send in a welcome channel (if configured)
                const welcomeChannelId = process.env.WELCOME_CHANNEL_ID;
                if (welcomeChannelId) {
                    const welcomeChannel = member.guild.channels.cache.get(welcomeChannelId);
                    if (welcomeChannel) {
                        try {
                            await welcomeChannel.send({ 
                                content: `${member.user}`, 
                                embeds: [welcomeEmbed], 
                                components: [row] 
                            });
                            logger.info(`Welcome message sent to welcome channel for ${member.user.tag}`);
                        } catch (channelError) {
                            logger.error(`Could not send welcome message to channel: ${channelError.message}`);
                        }
                    }
                }
            }

        } catch (error) {
            logger.error('Error in guildMemberAdd event:', error);
        }
    },
};
