const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const logger = require('./logger');

class VerificationMessage {
    static createVerificationEmbed() {
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🔗 LXND Account Verification')
            .setDescription('Welcome to the LXND Discord Server! Please read our rules and verify your account to access all channels.')
            .addFields(
                {
                    name: '📋 Server Rules',
                    value: '• Be respectful to all members\n• No spam or excessive self-promotion\n• Keep discussions relevant to channels\n• No NSFW content outside designated channels\n• Follow Discord Terms of Service\n• Use appropriate language and behavior\n• No harassment or discrimination\n• Respect staff decisions',
                    inline: false
                },
                {
                    name: '🔗 Important Links',
                    value: '[📜 Terms of Service](https://lxnd.cloud/tos)\n[📋 Privacy Policy](https://lxnd.cloud/privacy)\n[⚖️ Community Guidelines](https://lxnd.cloud/guidelines)',
                    inline: false
                },
                {
                    name: '🎁 Verification Benefits',
                    value: '• Access to all server channels\n• Automatic role assignment based on LXND status\n• Priority support and assistance\n• Integration with LXND dashboard features\n• Exclusive member-only content',
                    inline: false
                },
                {
                    name: '🔒 Privacy & Security',
                    value: 'We only store your Discord ID, username, and avatar. Your LXND account data remains secure and is never shared with third parties.',
                    inline: false
                },
                {
                    name: '❓ Need Help?',
                    value: 'If you encounter any issues during verification, please contact our support team or ask in the support channels.',
                    inline: false
                }
            )
            .setThumbnail('https://lxnd.cloud/static/images/logo.png')
            .setFooter({ 
                text: 'LXND License Management System • Click the button below to verify',
                iconURL: 'https://lxnd.cloud/static/images/favicon.ico'
            })
            .setTimestamp();

        return embed;
    }

    static createVerificationButton() {
        const button = new ButtonBuilder()
            .setLabel('Verify Account')
            .setStyle(ButtonStyle.Link)
            .setEmoji('🔗')
            .setURL('https://lxnd.cloud/oauth/discord/auth');

        const row = new ActionRowBuilder()
            .addComponents(button);

        return row;
    }

    static async sendVerificationMessage(client) {
        try {
            const guild = client.guilds.cache.get(process.env.GUILD_ID);
            if (!guild) {
                logger.error('Guild not found for verification message');
                return false;
            }

            const channel = guild.channels.cache.get(process.env.VERIFICATION_CHANNEL_ID);
            if (!channel) {
                logger.error('Verification channel not found');
                return false;
            }

            // Delete existing messages in the channel (optional - keeps it clean)
            try {
                const messages = await channel.messages.fetch({ limit: 100 });
                if (messages.size > 0) {
                    await channel.bulkDelete(messages);
                    logger.info('Cleared existing messages from verification channel');
                }
            } catch (error) {
                logger.warn('Could not clear existing messages:', error.message);
            }

            const embed = this.createVerificationEmbed();
            const button = this.createVerificationButton();

            const message = await channel.send({
                embeds: [embed],
                components: [button]
            });

            logger.info(`Verification message sent to channel ${channel.name} (${channel.id})`);
            return message;

        } catch (error) {
            logger.error('Error sending verification message:', error);
            return false;
        }
    }

    static async updateVerificationMessage(client) {
        try {
            const guild = client.guilds.cache.get(process.env.GUILD_ID);
            if (!guild) return false;

            const channel = guild.channels.cache.get(process.env.VERIFICATION_CHANNEL_ID);
            if (!channel) return false;

            // Find the bot's message in the channel
            const messages = await channel.messages.fetch({ limit: 10 });
            const botMessage = messages.find(msg => 
                msg.author.id === client.user.id && 
                msg.embeds.length > 0 && 
                msg.embeds[0].title?.includes('LXND Account Verification')
            );

            if (botMessage) {
                const embed = this.createVerificationEmbed();
                const button = this.createVerificationButton();

                await botMessage.edit({
                    embeds: [embed],
                    components: [button]
                });

                logger.info('Verification message updated');
                return true;
            } else {
                // If no existing message found, send a new one
                return await this.sendVerificationMessage(client);
            }

        } catch (error) {
            logger.error('Error updating verification message:', error);
            return false;
        }
    }
}

module.exports = VerificationMessage;
