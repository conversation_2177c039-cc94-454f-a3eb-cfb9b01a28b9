const database = require('../config/database');
const logger = require('./logger');

class CommandLogger {
    static async logCommand(interaction, success = true, errorMessage = null, executionTime = null) {
        try {
            const startTime = interaction.createdTimestamp;
            const endTime = Date.now();
            const actualExecutionTime = executionTime || (endTime - startTime);

            // Get command options
            const options = {};
            if (interaction.options) {
                // Get all options from the interaction
                interaction.options.data.forEach(option => {
                    if (option.type === 1) { // SUB_COMMAND
                        options.subcommand = option.name;
                        if (option.options) {
                            option.options.forEach(subOption => {
                                options[subOption.name] = subOption.value;
                            });
                        }
                    } else {
                        options[option.name] = option.value;
                    }
                });
            }

            await database.logCommand(
                interaction.user.id,
                `${interaction.user.username}#${interaction.user.discriminator}`,
                interaction.commandName,
                options,
                interaction.guildId,
                interaction.channelId,
                success,
                errorMessage,
                actualExecutionTime
            );

            logger.info(`Command logged: ${interaction.commandName} by ${interaction.user.tag} (${success ? 'SUCCESS' : 'FAILED'})`);

        } catch (error) {
            logger.error('Error logging command:', error);
        }
    }

    static async getCommandStats(days = 7) {
        try {
            const sql = `
                SELECT 
                    command_name,
                    COUNT(*) as total_uses,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_uses,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_uses,
                    AVG(execution_time_ms) as avg_execution_time,
                    COUNT(DISTINCT discord_user_id) as unique_users
                FROM discord_command_logs 
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY command_name
                ORDER BY total_uses DESC
            `;
            
            return await database.query(sql, [days]);
        } catch (error) {
            logger.error('Error getting command stats:', error);
            return [];
        }
    }

    static async getUserCommandHistory(discordUserId, limit = 10) {
        try {
            const sql = `
                SELECT 
                    command_name,
                    command_options,
                    success,
                    error_message,
                    execution_time_ms,
                    timestamp
                FROM discord_command_logs 
                WHERE discord_user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            `;
            
            return await database.query(sql, [discordUserId, limit]);
        } catch (error) {
            logger.error('Error getting user command history:', error);
            return [];
        }
    }

    static async getFailedCommands(hours = 24) {
        try {
            const sql = `
                SELECT 
                    discord_user_id,
                    discord_username,
                    command_name,
                    command_options,
                    error_message,
                    timestamp
                FROM discord_command_logs 
                WHERE success = 0 
                AND timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                ORDER BY timestamp DESC
            `;
            
            return await database.query(sql, [hours]);
        } catch (error) {
            logger.error('Error getting failed commands:', error);
            return [];
        }
    }

    static async getMostActiveUsers(days = 7, limit = 10) {
        try {
            const sql = `
                SELECT 
                    discord_user_id,
                    discord_username,
                    COUNT(*) as command_count,
                    COUNT(DISTINCT command_name) as unique_commands,
                    MAX(timestamp) as last_command
                FROM discord_command_logs 
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY discord_user_id, discord_username
                ORDER BY command_count DESC
                LIMIT ?
            `;
            
            return await database.query(sql, [days, limit]);
        } catch (error) {
            logger.error('Error getting most active users:', error);
            return [];
        }
    }

    static async cleanOldLogs(days = 30) {
        try {
            const sql = 'DELETE FROM discord_command_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL ? DAY)';
            const result = await database.query(sql, [days]);
            
            logger.info(`Cleaned ${result.affectedRows} old command logs (older than ${days} days)`);
            return result.affectedRows;
        } catch (error) {
            logger.error('Error cleaning old logs:', error);
            return 0;
        }
    }
}

module.exports = CommandLogger;
