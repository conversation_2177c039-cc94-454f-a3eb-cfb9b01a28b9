const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON><PERSON>B<PERSON>er, ButtonStyle, ChannelType, PermissionFlagsBits } = require('discord.js');
const logger = require('./logger');

class TicketSystem {
    static createTicketEmbed() {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🎫 LXND Support Ticket System')
            .setDescription('Need help? Create a support ticket and our team will assist you!')
            .addFields(
                {
                    name: '📋 What can we help with?',
                    value: '• Account issues and verification\n• License problems and activation\n• Technical support and bugs\n• Billing and payment questions\n• General inquiries and feedback',
                    inline: false
                },
                {
                    name: '⏱️ Response Time',
                    value: 'We typically respond within 24 hours during business days.',
                    inline: true
                },
                {
                    name: '🔒 Privacy',
                    value: 'Your ticket is private and only visible to you and our support team.',
                    inline: true
                },
                {
                    name: '📝 Before Creating a Ticket',
                    value: '• Check our FAQ and documentation\n• Make sure you\'ve tried basic troubleshooting\n• Have your account information ready',
                    inline: false
                }
            )
            .setThumbnail('https://lxnd.cloud/static/images/logo.png')
            .setFooter({ 
                text: 'lxnd.cloud • Click the button below to create a ticket',
                iconURL: 'https://lxnd.cloud/static/images/logo.png'
            })
            .setTimestamp();

        return embed;
    }

    static createTicketButtons() {
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_ticket')
                    .setLabel('Create Ticket')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎫'),
                new ButtonBuilder()
                    .setCustomId('ticket_faq')
                    .setLabel('FAQ')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
            );

        return row;
    }

    static async sendTicketMessage(client) {
        try {
            const guild = client.guilds.cache.get(process.env.GUILD_ID);
            if (!guild) {
                logger.error('Guild not found for ticket message');
                return false;
            }

            const channel = guild.channels.cache.get(process.env.TICKET_CHANNEL_ID);
            if (!channel) {
                logger.error('Ticket channel not found');
                return false;
            }

            // Clear existing messages in the channel
            try {
                const messages = await channel.messages.fetch({ limit: 100 });
                if (messages.size > 0) {
                    await channel.bulkDelete(messages);
                    logger.info('Cleared existing messages from ticket channel');
                }
            } catch (error) {
                logger.warn('Could not clear existing messages:', error.message);
            }

            const embed = this.createTicketEmbed();
            const buttons = this.createTicketButtons();

            const message = await channel.send({
                embeds: [embed],
                components: [buttons]
            });

            logger.info(`Ticket message sent to channel ${channel.name} (${channel.id})`);
            return message;

        } catch (error) {
            logger.error('Error sending ticket message:', error);
            return false;
        }
    }

    static async createTicket(interaction) {
        try {
            const guild = interaction.guild;
            const user = interaction.user;
            
            // Check if user already has an open ticket
            const existingTicket = guild.channels.cache.find(channel => 
                channel.name.includes(`ticket-${user.username.toLowerCase()}`) && 
                channel.parentId === process.env.TICKET_CATEGORY_ID
            );

            if (existingTicket) {
                const embed = new EmbedBuilder()
                    .setColor('#ffaa00')
                    .setTitle('⚠️ Ticket Already Exists')
                    .setDescription(`You already have an open ticket: ${existingTicket}`)
                    .setTimestamp()
                    .setFooter({ text: 'lxnd.cloud' });

                return await interaction.reply({ embeds: [embed], ephemeral: true });
            }

            await interaction.deferReply({ ephemeral: true });

            // Create ticket channel
            const ticketChannel = await guild.channels.create({
                name: `ticket-${user.username.toLowerCase()}-${Date.now().toString().slice(-4)}`,
                type: ChannelType.GuildText,
                parent: process.env.TICKET_CATEGORY_ID,
                permissionOverwrites: [
                    {
                        id: guild.roles.everyone.id,
                        deny: [PermissionFlagsBits.ViewChannel],
                    },
                    {
                        id: user.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles
                        ],
                    },
                    // Add permissions for specific support role
                    {
                        id: process.env.SUPPORT_ROLE_ID,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.ManageMessages
                        ],
                    },
                    // Add permissions for support roles if they exist
                    ...(guild.roles.cache.find(role => role.name.toLowerCase().includes('support')) ? [{
                        id: guild.roles.cache.find(role => role.name.toLowerCase().includes('support')).id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.ManageMessages
                        ],
                    }] : []),
                    // Add permissions for admin roles
                    ...(guild.roles.cache.find(role => role.name.toLowerCase().includes('admin')) ? [{
                        id: guild.roles.cache.find(role => role.name.toLowerCase().includes('admin')).id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.ManageMessages,
                            PermissionFlagsBits.ManageChannels
                        ],
                    }] : [])
                ],
            });

            // Send welcome message in ticket
            const welcomeEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🎫 Support Ticket Created')
                .setDescription(`Hello ${user}! Thank you for creating a support ticket.`)
                .addFields(
                    {
                        name: '📝 Please describe your issue',
                        value: 'Provide as much detail as possible about your problem or question. Include:\n• What you were trying to do\n• What happened instead\n• Any error messages\n• Screenshots if relevant',
                        inline: false
                    },
                    {
                        name: '⏱️ Response Time',
                        value: 'Our support team will respond within 24 hours during business days.',
                        inline: true
                    },
                    {
                        name: '🔒 Privacy',
                        value: 'This ticket is private and only visible to you and our support team.',
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            const closeButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('close_ticket')
                        .setLabel('Close Ticket')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🗑️')
                );

            await ticketChannel.send({
                content: `${user} Welcome to your support ticket! <@&${process.env.SUPPORT_ROLE_ID}> will assist you.`,
                embeds: [welcomeEmbed],
                components: [closeButton]
            });

            // Confirm ticket creation
            const confirmEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Ticket Created Successfully')
                .setDescription(`Your support ticket has been created: ${ticketChannel}`)
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            await interaction.editReply({ embeds: [confirmEmbed] });

            logger.info(`Ticket created for user ${user.tag}: ${ticketChannel.name}`);

        } catch (error) {
            logger.error('Error creating ticket:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while creating your ticket. Please try again later or contact an administrator.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }

    static async closeTicket(interaction) {
        try {
            const channel = interaction.channel;
            
            // Check if this is actually a ticket channel
            if (!channel.name.startsWith('ticket-') || channel.parentId !== process.env.TICKET_CATEGORY_ID) {
                const embed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Error')
                    .setDescription('This command can only be used in ticket channels.')
                    .setTimestamp()
                    .setFooter({ text: 'lxnd.cloud' });

                return await interaction.reply({ embeds: [embed], ephemeral: true });
            }

            const embed = new EmbedBuilder()
                .setColor('#ffaa00')
                .setTitle('🗑️ Close Ticket')
                .setDescription('Are you sure you want to close this ticket? This action cannot be undone.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            const confirmButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_close_ticket')
                        .setLabel('Yes, Close Ticket')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('✅'),
                    new ButtonBuilder()
                        .setCustomId('cancel_close_ticket')
                        .setLabel('Cancel')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('❌')
                );

            await interaction.reply({
                embeds: [embed],
                components: [confirmButtons],
                ephemeral: true
            });

        } catch (error) {
            logger.error('Error in close ticket:', error);
        }
    }

    static async confirmCloseTicket(interaction) {
        try {
            const channel = interaction.channel;
            
            await interaction.deferReply();

            // Send closing message
            const closingEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('🗑️ Ticket Closing')
                .setDescription('This ticket will be deleted in 10 seconds...')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            await interaction.editReply({ embeds: [closingEmbed], components: [] });

            logger.info(`Ticket ${channel.name} is being closed by ${interaction.user.tag}`);

            // Delete channel after 10 seconds
            setTimeout(async () => {
                try {
                    await channel.delete();
                    logger.info(`Ticket ${channel.name} has been deleted`);
                } catch (error) {
                    logger.error('Error deleting ticket channel:', error);
                }
            }, 10000);

        } catch (error) {
            logger.error('Error confirming close ticket:', error);
        }
    }

    static async cancelCloseTicket(interaction) {
        try {
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Cancelled')
                .setDescription('Ticket closure has been cancelled.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            await interaction.update({ embeds: [embed], components: [] });

        } catch (error) {
            logger.error('Error cancelling close ticket:', error);
        }
    }

    static async showFAQ(interaction) {
        try {
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('❓ Frequently Asked Questions')
                .setDescription('Here are some common questions and answers:')
                .addFields(
                    {
                        name: '🔗 How do I verify my Discord account?',
                        value: 'Click the "Verify Account" button in the verification channel and follow the instructions to link your LXND account.',
                        inline: false
                    },
                    {
                        name: '🔑 I forgot my LXND account password',
                        value: 'Visit [lxnd.cloud](https://lxnd.cloud) and use the "Forgot Password" option on the login page.',
                        inline: false
                    },
                    {
                        name: '💳 How do I purchase a license?',
                        value: 'Open a Ticket and our support team will assist you on buying a license.',
                        inline: false
                    },
                    {
                        name: '🔧 My license isn\'t working',
                        value: 'Make sure you\'ve activated it correctly and check that it hasn\'t expired. If issues persist, create a support ticket.',
                        inline: false
                    },
                    {
                        name: '📞 How can I contact support?',
                        value: 'Create a support ticket using the button above, or visit our website for additional contact options.',
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud • Still need help? Create a ticket!' });

            await interaction.reply({ embeds: [embed], ephemeral: true });

        } catch (error) {
            logger.error('Error showing FAQ:', error);
        }
    }
}

module.exports = TicketSystem;
