const database = require('../config/database');
const logger = require('./logger');

class LuxMonitor {
    static async checkExpiredLuxUsers() {
        try {
            logger.info('Checking for expired Lux users...');
            
            // Get all users with Discord linked and expired Lux status
            const expiredUsers = await database.query(`
                SELECT discord_id, username, lux_expires_at 
                FROM user 
                WHERE discord_id IS NOT NULL 
                AND is_lux = TRUE 
                AND lux_expires_at IS NOT NULL 
                AND lux_expires_at <= NOW()
            `);

            if (expiredUsers.length === 0) {
                logger.info('No expired Lux users found');
                return 0;
            }

            logger.info(`Found ${expiredUsers.length} expired Lux users`);

            let processedCount = 0;
            for (const user of expiredUsers) {
                try {
                    // Remove Lux role
                    await database.createDiscordRoleUpdate(user.discord_id, 'lux', 'remove');
                    
                    logger.info(`Queued Lux role removal for user ${user.username} (Discord: ${user.discord_id})`);
                    processedCount++;
                    
                } catch (error) {
                    logger.error(`Error processing expired Lux user ${user.username}:`, error);
                }
            }

            logger.info(`Processed ${processedCount} expired Lux users`);
            return processedCount;

        } catch (error) {
            logger.error('Error checking expired Lux users:', error);
            return 0;
        }
    }

    static async checkNewLuxUsers() {
        try {
            logger.info('Checking for new Lux users...');
            
            // Get all users with Discord linked and active Lux status but no recent Lux role update
            const newLuxUsers = await database.query(`
                SELECT u.discord_id, u.username, u.lux_expires_at 
                FROM user u
                WHERE u.discord_id IS NOT NULL 
                AND u.is_lux = TRUE 
                AND (u.lux_expires_at IS NULL OR u.lux_expires_at > NOW())
                AND NOT EXISTS (
                    SELECT 1 FROM discord_role_updates dru 
                    WHERE dru.discord_id = u.discord_id 
                    AND dru.role_type = 'lux' 
                    AND dru.action = 'add' 
                    AND dru.timestamp > DATE_SUB(NOW(), INTERVAL 1 DAY)
                )
            `);

            if (newLuxUsers.length === 0) {
                logger.info('No new Lux users found');
                return 0;
            }

            logger.info(`Found ${newLuxUsers.length} users who should have Lux role`);

            let processedCount = 0;
            for (const user of newLuxUsers) {
                try {
                    // Add Lux role
                    await database.createDiscordRoleUpdate(user.discord_id, 'lux', 'add');
                    
                    logger.info(`Queued Lux role addition for user ${user.username} (Discord: ${user.discord_id})`);
                    processedCount++;
                    
                } catch (error) {
                    logger.error(`Error processing new Lux user ${user.username}:`, error);
                }
            }

            logger.info(`Processed ${processedCount} new Lux users`);
            return processedCount;

        } catch (error) {
            logger.error('Error checking new Lux users:', error);
            return 0;
        }
    }

    static async syncAllLuxRoles() {
        try {
            logger.info('Starting full Lux role synchronization...');
            
            const expiredCount = await this.checkExpiredLuxUsers();
            const newCount = await this.checkNewLuxUsers();
            
            logger.info(`Lux role sync completed: ${expiredCount} expired, ${newCount} new`);
            
            return {
                expired: expiredCount,
                new: newCount,
                total: expiredCount + newCount
            };

        } catch (error) {
            logger.error('Error in full Lux role sync:', error);
            return { expired: 0, new: 0, total: 0 };
        }
    }

    static async getUserLuxStatus(discordId) {
        try {
            const user = await database.getUserByDiscordId(discordId);
            if (!user) return null;

            const isLuxActive = user.is_lux && (!user.lux_expires_at || new Date(user.lux_expires_at) > new Date());
            
            return {
                username: user.username,
                isLux: user.is_lux,
                isLuxActive: isLuxActive,
                luxExpiresAt: user.lux_expires_at,
                daysRemaining: user.lux_expires_at ? 
                    Math.max(0, Math.ceil((new Date(user.lux_expires_at) - new Date()) / (1000 * 60 * 60 * 24))) : 
                    null
            };

        } catch (error) {
            logger.error('Error getting user Lux status:', error);
            return null;
        }
    }

    static startMonitoring() {
        // Check every hour for expired Lux users
        setInterval(async () => {
            await this.checkExpiredLuxUsers();
        }, 60 * 60 * 1000); // 1 hour

        // Full sync every 6 hours
        setInterval(async () => {
            await this.syncAllLuxRoles();
        }, 6 * 60 * 60 * 1000); // 6 hours

        logger.info('Lux monitoring started - checking every hour, full sync every 6 hours');
    }
}

module.exports = LuxMonitor;
