# Discord OAuth2 Integration über LXND Dashboard

## Übersicht

Das Discord OAuth2-System wurde vollständig in das LXND Dashboard integriert und läuft über Port 443 (HTTPS). Der Discord Bot fungiert als Trigger für die Verifizierung, aber die eigentliche OAuth2-Abwicklung erfolgt über das Hauptdashboard.

## Funktionsweise

### 1. Verifizierung über Discord Bot

**Benutzer verwendet `/verify` Command:**
1. Bot zeigt Verifizierungs-Embed mit Button
2. Button führt zu: `https://lxnd.cloud/oauth/discord/auth?discord_user_id=USER_ID`
3. Dashboard speichert Discord User ID in Session
4. Benutzer wird zum Login weitergeleitet (falls nicht eingeloggt)

### 2. Dashboard OAuth2-Flow

**Nach dem Login:**
1. Dashboard startet Discord OAuth2-Flow
2. Benutzer autorisiert die Anwendung bei Discord
3. Dashboard erhält Discord-Benutzerdaten
4. Verknüpfung wird in der Datenbank gespeichert
5. Discord-Rollen werden automatisch zugewiesen

### 3. Rollenverwaltung

**Automatische Rollenzuweisung:**
- **Verification Role** (ID: 1396072581802491934): Alle verifizierten Benutzer
- **Lux Role**: Benutzer mit aktivem Lux-Status
- **Admin Role**: Administratoren (falls konfiguriert)

## API-Endpunkte

### Dashboard-Routen

```python
# OAuth2-Initiierung (von Discord Bot)
GET /oauth/discord/auth?discord_user_id=USER_ID

# Discord OAuth2-Callback
GET /auth/discord/callback

# Bot-Callback-Handler
GET /auth/discord/bot-callback

# API für direkte Verknüpfung
POST /api/discord/link
```

### Bot-API-Endpunkte

```javascript
# Gesundheitscheck
GET /health

# Bot-Statistiken
GET /stats
```

## Konfiguration

### Discord Bot (.env)

```env
# Discord Bot Configuration
DISCORD_TOKEN=MTM5NTc0NDk4MDY2ODI1MjIyMA.GtP41j.fn92FIjkkQGAKmIvldiVBOw84fuQAkYTAeOmcM
DISCORD_CLIENT_ID=1395744980668252220
DISCORD_CLIENT_SECRET=IddAilPc1qNeGpRaOyU85Wo2K9ObB6jo

# Server Configuration
GUILD_ID=1396070849664253952
VERIFICATION_ROLE_ID=1396072581802491934

# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=lxnd_app
DB_PASSWORD=LxndApp2024!
DB_NAME=lxnd_main

# Web Server Configuration (HTTPS)
PORT=443
OAUTH_REDIRECT_URI=https://lxnd.cloud/auth/discord/callback

# Logging
LOG_LEVEL=info
```

### Dashboard-Konfiguration (app.py)

```python
# Discord OAuth2 Configuration
app.config['DISCORD_CLIENT_ID'] = '1395744980668252220'
app.config['DISCORD_CLIENT_SECRET'] = 'IddAilPc1qNeGpRaOyU85Wo2K9ObB6jo'
app.config['DISCORD_REDIRECT_URI'] = 'https://lxnd.cloud/auth/discord/callback'
app.config['DISCORD_BOT_TOKEN'] = 'MTM5NTc0NDk4MDY2ODI1MjIyMA.GtP41j.fn92FIjkkQGAKmIvldiVBOw84fuQAkYTAeOmcM'
```

## Datenbankstruktur

### Erweiterte User-Tabelle

```sql
ALTER TABLE user ADD COLUMN discord_id VARCHAR(20) UNIQUE DEFAULT NULL;
ALTER TABLE user ADD COLUMN discord_username VARCHAR(100) DEFAULT NULL;
ALTER TABLE user ADD COLUMN discord_avatar VARCHAR(255) DEFAULT NULL;
ALTER TABLE user ADD COLUMN discord_linked_at TIMESTAMP NULL DEFAULT NULL;
```

### Discord Role Updates

```sql
CREATE TABLE discord_role_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    discord_id VARCHAR(20) NOT NULL,
    role_type VARCHAR(50) NOT NULL,
    action ENUM('add','remove') NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL DEFAULT NULL,
    error_message TEXT DEFAULT NULL
);
```

## Sicherheit

### HTTPS-Konfiguration

Der Bot läuft auf Port 443 mit SSL-Zertifikaten:
- Zertifikat: `../ssl/cert.pem`
- Privater Schlüssel: `../ssl/key.pem`

### Session-Management

- Discord User ID wird temporär in Flask-Session gespeichert
- Verifizierungs-Sessions haben 15-Minuten-Timeout
- State-Parameter für CSRF-Schutz

### Berechtigungen

- Bot benötigt `GUILD_MEMBERS` Intent für Rollenverwaltung
- Minimale Discord-Berechtigungen: `Manage Roles`
- MySQL-Benutzer mit eingeschränkten Rechten

## Monitoring

### Health Checks

```bash
# Bot-Status
curl -k https://localhost:443/health

# Dashboard-Integration
curl https://lxnd.cloud/admin/discord/status
```

### Logs

```bash
# Bot-Logs
tail -f discord-bot/logs/combined.log

# Dashboard-Logs
tail -f app.log
```

## Troubleshooting

### Häufige Probleme

**1. SSL-Zertifikat-Fehler:**
```bash
# Zertifikate überprüfen
openssl x509 -in ../ssl/cert.pem -text -noout
```

**2. Port 443 bereits belegt:**
```bash
# Port-Nutzung überprüfen
sudo netstat -tlnp | grep :443
```

**3. Discord-Rollen werden nicht zugewiesen:**
```sql
-- Pending Role Updates überprüfen
SELECT * FROM discord_role_updates WHERE processed = FALSE;
```

**4. OAuth2-Callback-Fehler:**
- Discord Application Redirect URI überprüfen
- Client ID und Secret validieren
- Session-Storage überprüfen

### Debug-Modus

```bash
# Bot im Debug-Modus starten
LOG_LEVEL=debug npm start

# Dashboard-Debug
export FLASK_DEBUG=1
python app.py
```

## Deployment-Checkliste

- [ ] SSL-Zertifikate installiert
- [ ] Port 443 verfügbar
- [ ] Discord Application konfiguriert
- [ ] Datenbank-Schema aktualisiert
- [ ] Umgebungsvariablen gesetzt
- [ ] Bot-Commands deployed
- [ ] Health Checks funktionieren
- [ ] Rollenverwaltung getestet
- [ ] OAuth2-Flow getestet

## Support

Bei Problemen:
1. Logs überprüfen (Bot + Dashboard)
2. Health Checks ausführen
3. Datenbank-Verbindung testen
4. Discord-Konfiguration validieren
5. SSL-Zertifikate überprüfen
