#!/bin/bash

# LXND Discord Bot Startup Script

echo "🤖 Starting LXND Discord Bot..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node --version)"
    exit 1
fi

# Change to bot directory
cd "$(dirname "$0")"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ .env file created. Please edit it with your configuration."
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies."
        exit 1
    fi
fi

# Setup database
echo "🗄️  Setting up database..."
npm run setup
if [ $? -ne 0 ]; then
    echo "❌ Database setup failed."
    exit 1
fi

# Deploy commands
echo "⚡ Deploying Discord commands..."
npm run deploy
if [ $? -ne 0 ]; then
    echo "❌ Command deployment failed."
    exit 1
fi

# Start the bot
echo "🚀 Starting Discord bot..."
npm start
