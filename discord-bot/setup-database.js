require('dotenv').config();
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');

async function setupDatabase() {
    let connection = null;
    
    try {
        console.log('🔗 Connecting to MySQL database...');
        
        // Create connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'lxnd_app',
            password: process.env.DB_PASSWORD || 'LxndApp2024!',
            database: process.env.DB_NAME || 'lxnd_main',
            multipleStatements: true
        });

        console.log('✅ Connected to MySQL database');

        // Read and execute schema
        const schemaPath = path.join(__dirname, 'database', 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');

        console.log('📋 Executing database schema...');

        // Split schema into individual statements and execute them one by one
        const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);

        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i].trim();
            if (statement.length > 0 && !statement.startsWith('--')) {
                try {
                    await connection.execute(statement);
                    console.log(`   ✅ Statement ${i + 1}/${statements.length} executed`);
                } catch (error) {
                    if (error.code === 'ER_DUP_KEYNAME' || error.code === 'ER_TABLE_EXISTS_ERROR') {
                        console.log(`   ⚠️  Statement ${i + 1}/${statements.length} skipped (already exists)`);
                    } else {
                        console.log(`   ❌ Statement ${i + 1}/${statements.length} failed: ${error.message}`);
                        // Continue with other statements
                    }
                }
            }
        }

        console.log('✅ Database schema executed successfully');

        // Verify tables exist
        console.log('🔍 Verifying tables...');
        
        const tables = [
            'discord_role_updates',
            'bot_settings',
            'discord_command_logs',
            'discord_verification_sessions',
            'discord_server_stats'
        ];

        for (const table of tables) {
            const [rows] = await connection.execute(
                'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
                [process.env.DB_NAME || 'lxnd_main', table]
            );
            
            if (rows[0].count > 0) {
                console.log(`   ✅ Table '${table}' exists`);
            } else {
                console.log(`   ❌ Table '${table}' missing`);
            }
        }

        // Verify user table has Discord columns
        console.log('🔍 Verifying user table Discord columns...');
        
        const discordColumns = [
            'discord_id',
            'discord_username', 
            'discord_avatar',
            'discord_linked_at'
        ];

        for (const column of discordColumns) {
            const [rows] = await connection.execute(
                'SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = ?',
                [process.env.DB_NAME || 'lxnd_main', 'user', column]
            );
            
            if (rows[0].count > 0) {
                console.log(`   ✅ Column 'user.${column}' exists`);
            } else {
                console.log(`   ❌ Column 'user.${column}' missing`);
            }
        }

        // Check bot settings
        console.log('🔍 Checking bot settings...');
        const [settingsRows] = await connection.execute('SELECT * FROM bot_settings WHERE id = 1');
        
        if (settingsRows.length > 0) {
            console.log('   ✅ Bot settings found');
            console.log(`   📊 Embed Color: ${settingsRows[0].embed_color}`);
            console.log(`   📊 Footer: ${settingsRows[0].global_footer}`);
            console.log(`   📊 Status: ${settingsRows[0].status}`);
            console.log(`   📊 Activity: ${settingsRows[0].activity_type} ${settingsRows[0].activity_text}`);
        } else {
            console.log('   ❌ Bot settings not found');
        }

        console.log('\n🎉 Database setup completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Install dependencies: npm install');
        console.log('   2. Deploy commands: npm run deploy');
        console.log('   3. Start the bot: npm start');

    } catch (error) {
        console.error('❌ Database setup failed:', error);
        logger.error('Database setup failed:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 Database connection closed');
        }
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    setupDatabase();
}

module.exports = { setupDatabase };
