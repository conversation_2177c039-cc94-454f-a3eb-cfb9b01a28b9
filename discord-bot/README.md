# LXND Discord Bot

Ein Discord.js <PERSON><PERSON> für das LXND License Management System mit MySQL-Integration und OAuth2-Verifizierung.

## Features

- 🔗 **Discord OAuth2 Verifizierung** - Verknüpfung von Discord-Accounts mit LXND-Accounts
- 🎭 **Automatische Rollenverwaltung** - Basierend auf LXND-Account-Status
- 💾 **MySQL-Integration** - Vollständige Datenbankintegration mit dem LXND-System
- 📊 **Status-Überwachung** - Benutzer können ihren Account-Status überprüfen
- 🎉 **Willkommensnachrichten** - Automatische Begrüßung neuer Mitglieder
- 📝 **Logging** - Umfassendes Logging-System mit Winston

## Installation

### Voraussetzungen

- Node.js 18 oder höher
- MySQL-Datenbank (bereits konfiguriert)
- Discord Bot Token und Application

### Automatische Installation (Empfohlen)

**Linux/macOS:**
```bash
cd discord-bot
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
cd discord-bot
start.bat
```

### Manuelle Installation

1. **Abhängigkeiten installieren:**
   ```bash
   cd discord-bot
   npm install
   ```

2. **Umgebungsvariablen konfigurieren:**
   ```bash
   cp .env.example .env
   # Bearbeite .env mit deinen Werten
   ```

3. **Datenbank einrichten:**
   ```bash
   npm run setup
   ```

4. **Tests ausführen:**
   ```bash
   npm test
   ```

5. **Slash Commands deployen:**
   ```bash
   npm run deploy
   ```

6. **Bot starten:**
   ```bash
   npm start
   ```

   Oder für Entwicklung:
   ```bash
   npm run dev
   ```

### Vollständiger Test und Setup

```bash
npm run test:full
```

Dieser Befehl führt automatisch Setup, Tests und Command-Deployment aus.

## Konfiguration

### Umgebungsvariablen (.env)

```env
# Discord Bot Configuration
DISCORD_TOKEN=your_bot_token
DISCORD_CLIENT_ID=your_client_id
DISCORD_CLIENT_SECRET=your_client_secret

# Server Configuration
GUILD_ID=1396070849664253952
VERIFICATION_ROLE_ID=1396072581802491934

# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=lxnd_app
DB_PASSWORD=LxndApp2024!
DB_NAME=lxnd_main

# Web Server Configuration
PORT=3001
OAUTH_REDIRECT_URI=https://lxnd.cloud/auth/discord/callback

# Logging
LOG_LEVEL=info
```

## Commands

- `/ping` - Überprüft die Bot-Latenz
- `/verify` - Startet den Verifizierungsprozess
- `/status` - Zeigt den aktuellen Account-Status an

## Architektur

```
discord-bot/
├── commands/           # Slash Commands
├── events/            # Discord Events
├── config/            # Konfigurationsdateien
├── utils/             # Hilfsfunktionen
├── logs/              # Log-Dateien
├── index.js           # Hauptdatei
└── deploy-commands.js # Command Deployment
```

## Integration mit LXND Dashboard

Der Bot ist vollständig in das LXND Dashboard integriert:

- **Benutzerverknüpfung:** Discord-Accounts werden automatisch mit LXND-Accounts verknüpft
- **Rollensynchronisation:** Rollen werden basierend auf LXND-Status automatisch zugewiesen
- **Datenbankintegration:** Alle Daten werden in der gemeinsamen MySQL-Datenbank gespeichert

## Entwicklung

### Neue Commands hinzufügen

1. Erstelle eine neue Datei in `commands/`
2. Implementiere die Command-Struktur:
   ```javascript
   const { SlashCommandBuilder } = require('discord.js');

   module.exports = {
       data: new SlashCommandBuilder()
           .setName('commandname')
           .setDescription('Command description'),
       async execute(interaction) {
           // Command logic
       },
   };
   ```
3. Deploye die Commands: `npm run deploy`

### Neue Events hinzufügen

1. Erstelle eine neue Datei in `events/`
2. Implementiere die Event-Struktur:
   ```javascript
   const { Events } = require('discord.js');

   module.exports = {
       name: Events.EventName,
       async execute(...args) {
           // Event logic
       },
   };
   ```

## Logging

Der Bot verwendet Winston für Logging:
- **Console:** Entwicklungsumgebung
- **Files:** `logs/combined.log` und `logs/error.log`
- **Levels:** error, warn, info, debug

## Sicherheit

- Alle sensiblen Daten werden über Umgebungsvariablen verwaltet
- Database-Queries verwenden Prepared Statements
- Express-Server ist mit Helmet und CORS abgesichert
- Graceful Shutdown für saubere Beendigung

## Support

Bei Problemen oder Fragen:
1. Überprüfe die Log-Dateien in `logs/`
2. Stelle sicher, dass alle Umgebungsvariablen korrekt gesetzt sind
3. Überprüfe die Datenbankverbindung
4. Kontaktiere das LXND-Team
