@echo off
title LXND Discord Bot Startup

echo 🤖 Starting LXND Discord Bot...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

REM Change to bot directory
cd /d "%~dp0"

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Copying from .env.example...
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo ✅ .env file created. Please edit it with your configuration.
    ) else (
        echo ❌ .env.example file not found. Please create .env file manually.
        pause
        exit /b 1
    )
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Setup database
echo 🗄️  Setting up database...
npm run setup
if %errorlevel% neq 0 (
    echo ❌ Database setup failed.
    pause
    exit /b 1
)

REM Deploy commands
echo ⚡ Deploying Discord commands...
npm run deploy
if %errorlevel% neq 0 (
    echo ❌ Command deployment failed.
    pause
    exit /b 1
)

REM Start the bot
echo 🚀 Starting Discord bot...
npm start

pause
