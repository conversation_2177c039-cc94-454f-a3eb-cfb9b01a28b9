const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const database = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// OAuth2 Configuration
const DISCORD_API_BASE = 'https://discord.com/api/v10';
const CLIENT_ID = process.env.DISCORD_CLIENT_ID;
const CLIENT_SECRET = process.env.DISCORD_CLIENT_SECRET;
const REDIRECT_URI = process.env.OAUTH_REDIRECT_URI;

/**
 * Initiate Discord OAuth2 flow
 * GET /oauth/discord/auth?user_id=123
 */
router.get('/discord/auth', async (req, res) => {
    try {
        const userId = req.query.user_id;
        const discordUserId = req.query.discord_user_id;
        
        if (!userId && !discordUserId) {
            return res.status(400).json({ 
                error: 'Missing user_id or discord_user_id parameter' 
            });
        }

        // Generate state token for security
        const state = crypto.randomBytes(32).toString('hex');
        const sessionToken = crypto.randomBytes(32).toString('hex');
        
        // Store verification session
        const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        
        if (discordUserId) {
            // Coming from Discord command
            await database.createVerificationSession(
                sessionToken, 
                discordUserId, 
                'Unknown', // Will be updated when OAuth completes
                state, 
                expiresAt
            );
        }

        // Build Discord OAuth URL
        const params = new URLSearchParams({
            client_id: CLIENT_ID,
            redirect_uri: REDIRECT_URI,
            response_type: 'code',
            scope: 'identify',
            state: state,
            prompt: 'consent'
        });

        const authUrl = `${DISCORD_API_BASE}/oauth2/authorize?${params.toString()}`;
        
        // Store session info in a way that can be retrieved
        // In production, you might want to use Redis or similar
        req.session = req.session || {};
        req.session[state] = {
            sessionToken,
            userId,
            discordUserId,
            expiresAt
        };

        logger.info(`OAuth2 flow initiated for user ${userId || discordUserId}, state: ${state}`);
        
        res.redirect(authUrl);

    } catch (error) {
        logger.error('Error initiating OAuth2 flow:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Handle Discord OAuth2 callback
 * GET /oauth/discord/callback?code=...&state=...
 */
router.get('/discord/callback', async (req, res) => {
    try {
        const { code, state } = req.query;

        if (!code || !state) {
            return res.status(400).send(`
                <html>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: #ff0000;">❌ Verification Failed</h1>
                        <p>Missing authorization code or state parameter.</p>
                        <p><a href="https://lxnd.cloud">Return to LXND</a></p>
                    </body>
                </html>
            `);
        }

        // Retrieve session info
        const sessionInfo = req.session && req.session[state];
        if (!sessionInfo) {
            return res.status(400).send(`
                <html>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: #ff0000;">❌ Verification Failed</h1>
                        <p>Invalid or expired verification session.</p>
                        <p><a href="https://lxnd.cloud">Return to LXND</a></p>
                    </body>
                </html>
            `);
        }

        // Exchange code for access token
        const tokenResponse = await axios.post(`${DISCORD_API_BASE}/oauth2/token`, 
            new URLSearchParams({
                client_id: CLIENT_ID,
                client_secret: CLIENT_SECRET,
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: REDIRECT_URI
            }),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        const { access_token } = tokenResponse.data;

        // Get Discord user info
        const userResponse = await axios.get(`${DISCORD_API_BASE}/users/@me`, {
            headers: {
                'Authorization': `Bearer ${access_token}`
            }
        });

        const discordUser = userResponse.data;
        
        logger.info(`OAuth2 callback received for Discord user: ${discordUser.username}#${discordUser.discriminator} (${discordUser.id})`);

        // Check if this Discord account is already linked
        const existingUser = await database.getUserByDiscordId(discordUser.id);
        
        if (existingUser) {
            return res.send(`
                <html>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: #ffaa00;">⚠️ Already Linked</h1>
                        <p>This Discord account is already linked to LXND account: <strong>${existingUser.username}</strong></p>
                        <p><a href="https://lxnd.cloud">Return to LXND</a></p>
                    </body>
                </html>
            `);
        }

        // If coming from Discord command, complete the verification
        if (sessionInfo.discordUserId) {
            await database.completeVerificationSession(sessionInfo.sessionToken, null);
            
            return res.send(`
                <html>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: #00ff00;">✅ Verification Started</h1>
                        <p>Please log in to your LXND account to complete the linking process.</p>
                        <p><a href="https://lxnd.cloud/login" style="background: #0066cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to LXND</a></p>
                    </body>
                </html>
            `);
        }

        // If coming from LXND dashboard, this would be handled by the main app
        // For now, redirect to LXND with success message
        res.send(`
            <html>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #00ff00;">✅ Discord Verified</h1>
                    <p>Your Discord account has been verified. Please complete the linking in your LXND dashboard.</p>
                    <p><a href="https://lxnd.cloud/profile" style="background: #0066cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Profile</a></p>
                </body>
            </html>
        `);

        // Clean up session
        if (req.session && req.session[state]) {
            delete req.session[state];
        }

    } catch (error) {
        logger.error('Error in OAuth2 callback:', error);
        
        res.status(500).send(`
            <html>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #ff0000;">❌ Verification Error</h1>
                    <p>An error occurred during verification. Please try again.</p>
                    <p><a href="https://lxnd.cloud">Return to LXND</a></p>
                </body>
            </html>
        `);
    }
});

/**
 * API endpoint to link Discord account to LXND user
 * POST /oauth/discord/link
 */
router.post('/discord/link', async (req, res) => {
    try {
        const { userId, discordId, discordUsername, discordAvatar } = req.body;

        if (!userId || !discordId) {
            return res.status(400).json({ 
                error: 'Missing required parameters' 
            });
        }

        // Check if Discord account is already linked
        const existingUser = await database.getUserByDiscordId(discordId);
        if (existingUser) {
            return res.status(409).json({ 
                error: 'Discord account already linked to another user' 
            });
        }

        // Update user with Discord info
        await database.updateUserDiscordInfo(userId, discordId, discordUsername, discordAvatar);

        // Create role update for verification role
        await database.createDiscordRoleUpdate(discordId, 'verification', 'add');

        logger.info(`Discord account linked: User ${userId} -> Discord ${discordId}`);

        res.json({ 
            success: true, 
            message: 'Discord account linked successfully' 
        });

    } catch (error) {
        logger.error('Error linking Discord account:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
