const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON>er, PermissionFlagsBits } = require('discord.js');
const TicketSystem = require('../utils/ticketSystem');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Admin: Manage the ticket system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Send the ticket message to the ticket channel')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('close')
                .setDescription('Force close a ticket channel')
                .addChannelOption(option =>
                    option
                        .setName('channel')
                        .setDescription('Ticket channel to close')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('Show ticket system statistics')
        ),

    async execute(interaction) {
        // Check if user has admin permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            return await interaction.reply({ embeds: [embed], ephemeral: true });
        }

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'setup':
                    await handleSetup(interaction);
                    break;
                case 'close':
                    await handleForceClose(interaction);
                    break;
                case 'stats':
                    await handleStats(interaction);
                    break;
                default:
                    await interaction.reply({ content: 'Unknown subcommand', ephemeral: true });
            }
        } catch (error) {
            logger.error('Error in ticket command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing the ticket command.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },
};

async function handleSetup(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const success = await TicketSystem.sendTicketMessage(interaction.client);

        const embed = new EmbedBuilder()
            .setColor(success ? '#00ff00' : '#ff0000')
            .setTitle(success ? '✅ Ticket System Setup' : '❌ Setup Failed')
            .setDescription(success ? 
                `Ticket message has been sent to <#${process.env.TICKET_CHANNEL_ID}>` :
                'Failed to send ticket message. Check logs for details.'
            )
            .addFields(
                { name: 'Ticket Channel', value: `<#${process.env.TICKET_CHANNEL_ID}>`, inline: true },
                { name: 'Ticket Category', value: `<#${process.env.TICKET_CATEGORY_ID}>`, inline: true }
            )
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.editReply({ embeds: [embed] });

        logger.info(`Ticket system setup ${success ? 'completed' : 'failed'} by ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Error in ticket setup:', error);
        throw error;
    }
}

async function handleForceClose(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const targetChannel = interaction.options.getChannel('channel') || interaction.channel;
        
        // Check if this is a ticket channel
        if (!targetChannel.name.startsWith('ticket-') || targetChannel.parentId !== process.env.TICKET_CATEGORY_ID) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Invalid Channel')
                .setDescription('The specified channel is not a ticket channel.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            return await interaction.editReply({ embeds: [embed] });
        }

        // Send closing message to the ticket channel
        const closingEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('🗑️ Ticket Force Closed')
            .setDescription(`This ticket has been force closed by ${interaction.user}.\nChannel will be deleted in 10 seconds...`)
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await targetChannel.send({ embeds: [closingEmbed] });

        // Confirm to admin
        const confirmEmbed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Ticket Force Closed')
            .setDescription(`Ticket ${targetChannel.name} has been force closed and will be deleted in 10 seconds.`)
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.editReply({ embeds: [confirmEmbed] });

        logger.info(`Ticket ${targetChannel.name} force closed by ${interaction.user.tag}`);

        // Delete channel after 10 seconds
        setTimeout(async () => {
            try {
                await targetChannel.delete();
                logger.info(`Force closed ticket ${targetChannel.name} has been deleted`);
            } catch (error) {
                logger.error('Error deleting force closed ticket:', error);
            }
        }, 10000);

    } catch (error) {
        logger.error('Error in force close ticket:', error);
        throw error;
    }
}

async function handleStats(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const guild = interaction.guild;
        const ticketCategory = guild.channels.cache.get(process.env.TICKET_CATEGORY_ID);
        
        if (!ticketCategory) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Category Not Found')
                .setDescription('Ticket category not found. Please check the configuration.')
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            return await interaction.editReply({ embeds: [embed] });
        }

        const ticketChannels = ticketCategory.children.cache.filter(channel => 
            channel.name.startsWith('ticket-')
        );

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📊 Ticket System Statistics')
            .setDescription('Current ticket system status and statistics')
            .addFields(
                { name: '🎫 Open Tickets', value: ticketChannels.size.toString(), inline: true },
                { name: '📁 Ticket Category', value: ticketCategory.name, inline: true },
                { name: '📢 Ticket Channel', value: `<#${process.env.TICKET_CHANNEL_ID}>`, inline: true },
                { 
                    name: '📋 Recent Tickets', 
                    value: ticketChannels.size > 0 ? 
                        ticketChannels.map(channel => `• ${channel.name}`).slice(0, 5).join('\n') + 
                        (ticketChannels.size > 5 ? `\n... and ${ticketChannels.size - 5} more` : '') :
                        'No open tickets',
                    inline: false 
                }
            )
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        logger.error('Error in ticket stats:', error);
        throw error;
    }
}
