const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const database = require('../config/database');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('status')
        .setDescription('Check your LXND account status and verification information'),
    
    async execute(interaction) {
        try {
            const user = await database.getUserByDiscordId(interaction.user.id);
            
            if (!user) {
                const embed = new EmbedBuilder()
                    .setColor('#ff6b6b')
                    .setTitle('❌ Not Verified')
                    .setDescription('Your Discord account is not linked to any LXND account.')
                    .addFields({
                        name: '🔗 Get Started',
                        value: 'Use `/verify` to link your Discord account with your LXND account.',
                        inline: false
                    })
                    .setTimestamp()
                    .setFooter({ text: 'LXND License Management System' });

                return await interaction.reply({ 
                    embeds: [embed], 
                    ephemeral: true 
                });
            }

            // Determine user status
            const isLux = user.is_lux && (!user.lux_expires_at || new Date(user.lux_expires_at) > new Date());
            const isAdmin = user.is_admin;
            const isModerator = user.is_moderator;
            const isBanned = user.is_banned;

            // Create status embed
            const embed = new EmbedBuilder()
                .setColor(isBanned ? '#ff0000' : isLux ? '#ffd700' : '#00ff00')
                .setTitle('📊 Your LXND Status')
                .setDescription(`Account information for **${user.username}**`)
                .addFields(
                    {
                        name: '👤 Account Details',
                        value: `**Username:** ${user.username}\n**Email:** ${user.email}\n**Member since:** <t:${Math.floor(new Date(user.created_at).getTime() / 1000)}:D>`,
                        inline: false
                    },
                    {
                        name: '🔗 Discord Link',
                        value: `**Linked since:** ${user.discord_linked_at ? `<t:${Math.floor(new Date(user.discord_linked_at).getTime() / 1000)}:F>` : 'Unknown'}`,
                        inline: false
                    },
                    {
                        name: '🎭 Roles & Permissions',
                        value: `${isAdmin ? '👑 **Administrator**\n' : ''}${isModerator ? '🛡️ **Moderator**\n' : ''}${isLux ? '⭐ **Lux Member**\n' : ''}${isBanned ? '🚫 **Banned**\n' : ''}${!isAdmin && !isModerator && !isLux && !isBanned ? '👤 **Regular User**' : ''}`,
                        inline: false
                    }
                );

            // Add Lux expiration info if applicable
            if (isLux && user.lux_expires_at) {
                embed.addFields({
                    name: '⭐ Lux Membership',
                    value: `**Expires:** <t:${Math.floor(new Date(user.lux_expires_at).getTime() / 1000)}:F>`,
                    inline: false
                });
            } else if (isLux && !user.lux_expires_at) {
                embed.addFields({
                    name: '⭐ Lux Membership',
                    value: '**Status:** Permanent',
                    inline: false
                });
            }

            // Add storage and limits info
            embed.addFields(
                {
                    name: '💾 Account Limits',
                    value: `**Upload Limit:** ${user.upload_limit_mb} MB\n**Max Projects:** ${user.max_projects}\n**Keys per Project:** ${user.max_keys_per_project}`,
                    inline: true
                }
            );

            embed.setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            await interaction.reply({ 
                embeds: [embed], 
                ephemeral: true 
            });

            logger.info(`Status checked for user ${interaction.user.tag} (LXND: ${user.username})`);

        } catch (error) {
            logger.error('Error in status command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while retrieving your status. Please try again later.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            await interaction.reply({ 
                embeds: [errorEmbed], 
                ephemeral: true 
            });
        }
    },
};
