const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Show help information and available commands'),
    
    async execute(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🤖 LXND Discord Bot Help')
            .setDescription('Welcome to the LXND License Management System Discord Bot!')
            .addFields(
                {
                    name: '🔗 Verification Commands',
                    value: '`/verify` - Link your Discord account with your LXND account\n`/status` - Check your current LXND account status',
                    inline: false
                },
                {
                    name: '🛠️ Utility Commands',
                    value: '`/ping` - Check bot latency and response time\n`/help` - Show this help message',
                    inline: false
                },
                {
                    name: '👑 Admin Commands',
                    value: '`/admin sync-roles` - Sync all Discord roles with LXND database\n`/admin stats` - Show bot and server statistics\n`/admin user-info` - Get LXND info for a Discord user\n`/admin force-verify` - Force verify a Discord user',
                    inline: false
                },
                {
                    name: '🎁 Benefits of Verification',
                    value: '• Access to verified user channels\n• Automatic role assignment based on LXND status\n• Integration with LXND dashboard features\n• Priority support',
                    inline: false
                },
                {
                    name: '🔒 Privacy & Security',
                    value: 'We only store your Discord ID, username, and avatar. Your LXND account data remains secure and is never shared.',
                    inline: false
                },
                {
                    name: '❓ Need Help?',
                    value: 'If you encounter any issues or have questions, please contact our support team or ask in the support channels.',
                    inline: false
                }
            )
            .setThumbnail(interaction.client.user.displayAvatarURL({ dynamic: true }))
            .setTimestamp()
            .setFooter({ 
                text: 'LXND License Management System',
                iconURL: interaction.guild?.iconURL({ dynamic: true })
            });

        // Create buttons for quick actions
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Start Verification')
                    .setStyle(ButtonStyle.Link)
                    .setEmoji('🔗')
                    .setURL(`https://lxnd.cloud/oauth/discord/auth?discord_user_id=${interaction.user.id}`),
                new ButtonBuilder()
                    .setLabel('LXND Dashboard')
                    .setStyle(ButtonStyle.Link)
                    .setEmoji('🌐')
                    .setURL('https://lxnd.cloud'),
                new ButtonBuilder()
                    .setLabel('Support')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
                    .setURL('https://lxnd.cloud/support')
            );

        await interaction.reply({ 
            embeds: [embed], 
            components: [row],
            ephemeral: true 
        });
    },
};
