const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Replies with Pong and bot latency!'),
    
    async execute(interaction) {
        const sent = await interaction.reply({ 
            content: 'Pinging...', 
            fetchReply: true 
        });
        
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🏓 Pong!')
            .addFields(
                { 
                    name: 'Roundtrip latency', 
                    value: `${sent.createdTimestamp - interaction.createdTimestamp}ms`, 
                    inline: true 
                },
                { 
                    name: 'Websocket heartbeat', 
                    value: `${interaction.client.ws.ping}ms`, 
                    inline: true 
                }
            )
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.editReply({ 
            content: '', 
            embeds: [embed] 
        });
    },
};
