const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uilder, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const database = require('../config/database');
const logger = require('../utils/logger');
const LuxMonitor = require('../utils/luxMonitor');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin')
        .setDescription('Admin commands for Discord bot management')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('sync-roles')
                .setDescription('Sync all Discord roles with LXND database')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('sync-lux')
                .setDescription('Sync Lux roles and check for expired users')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('Show bot and server statistics')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('user-info')
                .setDescription('Get LXND info for a Discord user')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('Discord user to lookup')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('force-verify')
                .setDescription('Force verify a Discord user with LXND account')
                .addUserOption(option =>
                    option
                        .setName('discord-user')
                        .setDescription('Discord user to verify')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option
                        .setName('lxnd-username')
                        .setDescription('LXND username to link to')
                        .setRequired(true)
                )
        ),
    
    async execute(interaction) {
        // Check if user has admin permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.reply({ embeds: [embed], ephemeral: true });
        }

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'sync-roles':
                    await handleSyncRoles(interaction);
                    break;
                case 'sync-lux':
                    await handleSyncLux(interaction);
                    break;
                case 'stats':
                    await handleStats(interaction);
                    break;
                case 'user-info':
                    await handleUserInfo(interaction);
                    break;
                case 'force-verify':
                    await handleForceVerify(interaction);
                    break;
                default:
                    await interaction.reply({ content: 'Unknown subcommand', ephemeral: true });
            }
        } catch (error) {
            logger.error(`Error in admin command (${subcommand}):`, error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while executing the admin command.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },
};

async function handleSyncRoles(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        // Get all verified users
        const verifiedUsers = await database.query(
            'SELECT discord_id, is_lux, lux_expires_at, is_admin FROM user WHERE discord_id IS NOT NULL'
        );

        let syncCount = 0;
        let errorCount = 0;

        for (const user of verifiedUsers) {
            try {
                // Check if user should have verification role
                await database.createDiscordRoleUpdate(user.discord_id, 'verification', 'add');
                
                // Check if user should have lux role
                const isLuxActive = user.is_lux && (!user.lux_expires_at || new Date(user.lux_expires_at) > new Date());
                if (isLuxActive) {
                    await database.createDiscordRoleUpdate(user.discord_id, 'lux', 'add');
                } else {
                    await database.createDiscordRoleUpdate(user.discord_id, 'lux', 'remove');
                }

                syncCount++;
            } catch (error) {
                logger.error(`Error syncing roles for user ${user.discord_id}:`, error);
                errorCount++;
            }
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🔄 Role Sync Complete')
            .setDescription(`Role synchronization has been queued for processing.`)
            .addFields(
                { name: 'Users Processed', value: syncCount.toString(), inline: true },
                { name: 'Errors', value: errorCount.toString(), inline: true },
                { name: 'Status', value: 'Role updates will be processed within 30 seconds', inline: false }
            )
            .setTimestamp()
            .setFooter({ text: 'LXND License Management System' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        logger.error('Error in sync-roles command:', error);
        throw error;
    }
}

async function handleSyncLux(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const result = await LuxMonitor.syncAllLuxRoles();

        const embed = new EmbedBuilder()
            .setColor('#ffd700')
            .setTitle('⭐ Lux Role Sync Complete')
            .setDescription('Lux role synchronization has been completed.')
            .addFields(
                { name: 'Expired Users', value: result.expired.toString(), inline: true },
                { name: 'New Lux Users', value: result.new.toString(), inline: true },
                { name: 'Total Processed', value: result.total.toString(), inline: true },
                { name: 'Status', value: 'Role updates will be processed within 30 seconds', inline: false }
            )
            .setTimestamp()
            .setFooter({ text: 'LXND License Management System' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        logger.error('Error in sync-lux command:', error);
        throw error;
    }
}

async function handleStats(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const guild = interaction.guild;
        const verifiedCount = await database.getVerifiedUsersCount();
        const luxCount = await database.getLuxUsersCount();
        
        // Get pending role updates
        const pendingUpdates = await database.getPendingRoleUpdates();

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📊 Bot Statistics')
            .addFields(
                { name: '🤖 Bot Status', value: `Online\nPing: ${interaction.client.ws.ping}ms`, inline: true },
                { name: '🏠 Server Info', value: `${guild.name}\n${guild.memberCount} members`, inline: true },
                { name: '🔗 Verified Users', value: verifiedCount.toString(), inline: true },
                { name: '⭐ Lux Users', value: luxCount.toString(), inline: true },
                { name: '⏳ Pending Updates', value: pendingUpdates.length.toString(), inline: true },
                { name: '📅 Uptime', value: formatUptime(interaction.client.uptime), inline: true }
            )
            .setTimestamp()
            .setFooter({ text: 'LXND License Management System' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        logger.error('Error in stats command:', error);
        throw error;
    }
}

async function handleUserInfo(interaction) {
    const targetUser = interaction.options.getUser('user');
    
    await interaction.deferReply({ ephemeral: true });

    try {
        const lxndUser = await database.getUserByDiscordId(targetUser.id);

        if (!lxndUser) {
            const embed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('❌ User Not Found')
                .setDescription(`Discord user ${targetUser.tag} is not linked to any LXND account.`)
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.editReply({ embeds: [embed] });
        }

        const isLuxActive = lxndUser.is_lux && (!lxndUser.lux_expires_at || new Date(lxndUser.lux_expires_at) > new Date());

        const embed = new EmbedBuilder()
            .setColor(isLuxActive ? '#ffd700' : '#00ff00')
            .setTitle('👤 User Information')
            .setDescription(`Information for ${targetUser.tag}`)
            .addFields(
                { name: 'LXND Username', value: lxndUser.username, inline: true },
                { name: 'Email', value: lxndUser.email, inline: true },
                { name: 'User ID', value: lxndUser.id.toString(), inline: true },
                { name: 'Admin', value: lxndUser.is_admin ? '✅ Yes' : '❌ No', inline: true },
                { name: 'Moderator', value: lxndUser.is_moderator ? '✅ Yes' : '❌ No', inline: true },
                { name: 'Lux Status', value: isLuxActive ? '⭐ Active' : '❌ Inactive', inline: true },
                { name: 'Banned', value: lxndUser.is_banned ? '🚫 Yes' : '✅ No', inline: true },
                { name: 'Linked Since', value: lxndUser.discord_linked_at ? `<t:${Math.floor(new Date(lxndUser.discord_linked_at).getTime() / 1000)}:F>` : 'Unknown', inline: true },
                { name: 'Member Since', value: `<t:${Math.floor(new Date(lxndUser.created_at).getTime() / 1000)}:D>`, inline: true }
            )
            .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
            .setTimestamp()
            .setFooter({ text: 'LXND License Management System' });

        if (isLuxActive && lxndUser.lux_expires_at) {
            embed.addFields({
                name: 'Lux Expires',
                value: `<t:${Math.floor(new Date(lxndUser.lux_expires_at).getTime() / 1000)}:F>`,
                inline: true
            });
        }

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        logger.error('Error in user-info command:', error);
        throw error;
    }
}

async function handleForceVerify(interaction) {
    const discordUser = interaction.options.getUser('discord-user');
    const lxndUsername = interaction.options.getString('lxnd-username');
    
    await interaction.deferReply({ ephemeral: true });

    try {
        // Check if Discord user is already linked
        const existingLink = await database.getUserByDiscordId(discordUser.id);
        if (existingLink) {
            const embed = new EmbedBuilder()
                .setColor('#ffaa00')
                .setTitle('⚠️ Already Linked')
                .setDescription(`Discord user ${discordUser.tag} is already linked to LXND account: ${existingLink.username}`)
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.editReply({ embeds: [embed] });
        }

        // Find LXND user by username
        const lxndUser = await database.query('SELECT * FROM user WHERE username = ?', [lxndUsername]);
        if (lxndUser.length === 0) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ LXND User Not Found')
                .setDescription(`No LXND user found with username: ${lxndUsername}`)
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.editReply({ embeds: [embed] });
        }

        const user = lxndUser[0];

        // Check if LXND user already has Discord linked
        if (user.discord_id) {
            const embed = new EmbedBuilder()
                .setColor('#ffaa00')
                .setTitle('⚠️ LXND User Already Linked')
                .setDescription(`LXND user ${lxndUsername} is already linked to Discord ID: ${user.discord_id}`)
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.editReply({ embeds: [embed] });
        }

        // Link the accounts
        await database.updateUserDiscordInfo(
            user.id, 
            discordUser.id, 
            `${discordUser.username}#${discordUser.discriminator}`, 
            discordUser.avatar
        );

        // Add verification role
        await database.createDiscordRoleUpdate(discordUser.id, 'verification', 'add');

        // Add lux role if applicable
        const isLuxActive = user.is_lux && (!user.lux_expires_at || new Date(user.lux_expires_at) > new Date());
        if (isLuxActive) {
            await database.createDiscordRoleUpdate(discordUser.id, 'lux', 'add');
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Force Verification Complete')
            .setDescription(`Successfully linked Discord user ${discordUser.tag} to LXND account ${lxndUsername}`)
            .addFields(
                { name: 'Discord User', value: `${discordUser.tag} (${discordUser.id})`, inline: false },
                { name: 'LXND User', value: `${user.username} (ID: ${user.id})`, inline: false },
                { name: 'Roles Assigned', value: isLuxActive ? 'Verification, Lux' : 'Verification', inline: false }
            )
            .setTimestamp()
            .setFooter({ text: 'LXND License Management System' });

        await interaction.editReply({ embeds: [embed] });

        logger.info(`Force verification completed by ${interaction.user.tag}: ${discordUser.tag} -> ${lxndUsername}`);

    } catch (error) {
        logger.error('Error in force-verify command:', error);
        throw error;
    }
}

function formatUptime(uptime) {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
}
