const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const VerificationMessage = require('../utils/verificationMessage');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verify')
        .setDescription('Admin: Send or update the verification message')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    
    async execute(interaction) {
        // Check if user has admin permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            return await interaction.reply({ embeds: [embed], ephemeral: true });
        }

        try {
            await interaction.deferReply({ ephemeral: true });

            const success = await VerificationMessage.sendVerificationMessage(interaction.client);

            const embed = new EmbedBuilder()
                .setColor(success ? '#00ff00' : '#ff0000')
                .setTitle(success ? '✅ Verification Message Sent' : '❌ Error')
                .setDescription(success ?
                    `Verification message has been sent to <#${process.env.VERIFICATION_CHANNEL_ID}>` :
                    'Failed to send verification message. Check logs for details.'
                )
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            await interaction.editReply({ embeds: [embed] });

            logger.info(`Verification message ${success ? 'sent' : 'failed'} by ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Error in verify admin command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while sending the verification message.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },
};
