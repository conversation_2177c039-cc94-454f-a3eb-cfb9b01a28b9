const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const database = require('../config/database');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verify')
        .setDescription('Start the verification process to link your Discord account with LXND'),
    
    async execute(interaction) {
        try {
            // Check if user is already verified
            const existingUser = await database.getUserByDiscordId(interaction.user.id);
            
            if (existingUser) {
                const embed = new EmbedBuilder()
                    .setColor('#ffaa00')
                    .setTitle('⚠️ Already Verified')
                    .setDescription(`Your Discord account is already linked to LXND account: **${existingUser.username}**`)
                    .addFields({
                        name: 'Linked since',
                        value: existingUser.discord_linked_at ? 
                            `<t:${Math.floor(new Date(existingUser.discord_linked_at).getTime() / 1000)}:F>` : 
                            'Unknown',
                        inline: true
                    })
                    .setTimestamp()
                    .setFooter({ text: 'LXND License Management System' });

                return await interaction.reply({ 
                    embeds: [embed], 
                    ephemeral: true 
                });
            }

            // Create verification embed with button
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🔗 Account Verification')
                .setDescription('Link your Discord account with your LXND account to access exclusive features and roles.')
                .addFields(
                    {
                        name: '📋 What you need:',
                        value: '• An active LXND account\n• Access to the email associated with your LXND account',
                        inline: false
                    },
                    {
                        name: '🎁 Benefits:',
                        value: '• Access to verified user channels\n• Automatic role assignment based on your LXND status\n• Integration with LXND dashboard features',
                        inline: false
                    },
                    {
                        name: '🔒 Privacy:',
                        value: 'We only store your Discord ID, username, and avatar. Your LXND account data remains secure.',
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            const button = new ButtonBuilder()
                .setLabel('Start Verification')
                .setStyle(ButtonStyle.Link)
                .setEmoji('🔗')
                .setURL(`https://lxnd.cloud/oauth/discord/auth?discord_user_id=${interaction.user.id}`);

            const row = new ActionRowBuilder()
                .addComponents(button);

            await interaction.reply({ 
                embeds: [embed], 
                components: [row], 
                ephemeral: true 
            });

            logger.info(`Verification process initiated for user ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            logger.error('Error in verify command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing your verification request. Please try again later.')
                .setTimestamp()
                .setFooter({ text: 'LXND License Management System' });

            await interaction.reply({ 
                embeds: [errorEmbed], 
                ephemeral: true 
            });
        }
    },
};
