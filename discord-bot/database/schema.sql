-- Discord Bot Database Schema
-- <PERSON><PERSON> werden für die Discord-Integration benötigt

-- <PERSON><PERSON><PERSON> für Discord Role Updates (falls nicht vorhanden)
CREATE TABLE IF NOT EXISTS `discord_role_updates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `discord_id` varchar(20) NOT NULL,
  `role_type` varchar(50) NOT NULL,
  `action` enum('add','remove') NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `processed` tinyint(1) NOT NULL DEFAULT 0,
  `processed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_discord_id` (`discord_id`),
  KEY `idx_processed` (`processed`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- <PERSON><PERSON><PERSON> für Bot Settings (falls nicht vorhanden)
CREATE TABLE IF NOT EXISTS `bot_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `embed_color` varchar(7) NOT NULL DEFAULT '#00ff00',
  `global_footer` varchar(255) NOT NULL DEFAULT 'LXND License Management System',
  `status` varchar(20) NOT NULL DEFAULT 'online',
  `activity_type` varchar(20) NOT NULL DEFAULT 'watching',
  `activity_text` varchar(128) NOT NULL DEFAULT 'LXND License System',
  `activity_url` varchar(255) DEFAULT NULL,
  `bot_name` varchar(100) DEFAULT NULL,
  `bot_avatar_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabelle für Discord Command Logs
CREATE TABLE IF NOT EXISTS `discord_command_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `discord_user_id` varchar(20) NOT NULL,
  `discord_username` varchar(100) NOT NULL,
  `command_name` varchar(50) NOT NULL,
  `command_options` json DEFAULT NULL,
  `guild_id` varchar(20) DEFAULT NULL,
  `channel_id` varchar(20) DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 1,
  `error_message` text DEFAULT NULL,
  `execution_time_ms` int(11) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_discord_user_id` (`discord_user_id`),
  KEY `idx_command_name` (`command_name`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_guild_id` (`guild_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabelle für Discord Verification Sessions
CREATE TABLE IF NOT EXISTS `discord_verification_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_token` varchar(64) NOT NULL,
  `discord_user_id` varchar(20) NOT NULL,
  `discord_username` varchar(100) NOT NULL,
  `state` varchar(64) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `completed_at` timestamp NULL DEFAULT NULL,
  `linked_user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_token` (`session_token`),
  KEY `idx_discord_user_id` (`discord_user_id`),
  KEY `idx_state` (`state`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_completed` (`completed`),
  FOREIGN KEY (`linked_user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabelle für Discord Server Statistics
CREATE TABLE IF NOT EXISTS `discord_server_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `guild_id` varchar(20) NOT NULL,
  `total_members` int(11) NOT NULL DEFAULT 0,
  `verified_members` int(11) NOT NULL DEFAULT 0,
  `lux_members` int(11) NOT NULL DEFAULT 0,
  `admin_members` int(11) NOT NULL DEFAULT 0,
  `online_members` int(11) NOT NULL DEFAULT 0,
  `recorded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_guild_id` (`guild_id`),
  KEY `idx_recorded_at` (`recorded_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Erweitere die user Tabelle um Discord-Felder (falls nicht vorhanden)
-- Diese werden nur hinzugefügt, wenn sie noch nicht existieren

-- Discord ID Feld
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = DATABASE() 
   AND TABLE_NAME = 'user' 
   AND COLUMN_NAME = 'discord_id') = 0,
  'ALTER TABLE `user` ADD COLUMN `discord_id` varchar(20) UNIQUE DEFAULT NULL',
  'SELECT "discord_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Discord Username Feld
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = DATABASE() 
   AND TABLE_NAME = 'user' 
   AND COLUMN_NAME = 'discord_username') = 0,
  'ALTER TABLE `user` ADD COLUMN `discord_username` varchar(100) DEFAULT NULL',
  'SELECT "discord_username column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Discord Avatar Feld
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = DATABASE() 
   AND TABLE_NAME = 'user' 
   AND COLUMN_NAME = 'discord_avatar') = 0,
  'ALTER TABLE `user` ADD COLUMN `discord_avatar` varchar(255) DEFAULT NULL',
  'SELECT "discord_avatar column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Discord Linked At Feld
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = DATABASE() 
   AND TABLE_NAME = 'user' 
   AND COLUMN_NAME = 'discord_linked_at') = 0,
  'ALTER TABLE `user` ADD COLUMN `discord_linked_at` timestamp NULL DEFAULT NULL',
  'SELECT "discord_linked_at column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Erstelle Indizes für bessere Performance
CREATE INDEX IF NOT EXISTS `idx_user_discord_id` ON `user` (`discord_id`);
CREATE INDEX IF NOT EXISTS `idx_user_discord_linked_at` ON `user` (`discord_linked_at`);

-- Füge Standard Bot Settings hinzu (falls nicht vorhanden)
INSERT IGNORE INTO `bot_settings` (`id`, `embed_color`, `global_footer`, `status`, `activity_type`, `activity_text`) 
VALUES (1, '#00ff00', 'LXND License Management System', 'online', 'watching', 'LXND License System');
