require('dotenv').config();
const { Client, GatewayIntentBits, Collection, Events, ActivityType } = require('discord.js');
const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');
const database = require('./config/database');
const CommandLogger = require('./utils/commandLogger');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages
    ]
});

// Collections for commands and events
client.commands = new Collection();
client.events = new Collection();

// Load commands
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            logger.info(`Loaded command: ${command.data.name}`);
        } else {
            logger.warn(`Command at ${filePath} is missing required "data" or "execute" property.`);
        }
    }
}

// Load events
const eventsPath = path.join(__dirname, 'events');
if (fs.existsSync(eventsPath)) {
    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));
    
    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        const event = require(filePath);
        
        if (event.once) {
            client.once(event.name, (...args) => event.execute(...args));
        } else {
            client.on(event.name, (...args) => event.execute(...args));
        }
        logger.info(`Loaded event: ${event.name}`);
    }
}

// Basic ready event
client.once(Events.ClientReady, async (readyClient) => {
    logger.info(`Bot is ready! Logged in as ${readyClient.user.tag}`);
    
    // Set bot activity
    client.user.setActivity('LXND License System', { type: ActivityType.Watching });
    
    // Connect to database
    const dbConnected = await database.connect();
    if (!dbConnected) {
        logger.error('Failed to connect to database. Bot will continue but database features will not work.');
    }
    
    // Start role update processor
    setInterval(processRoleUpdates, 30000); // Check every 30 seconds

    // Start statistics updater
    setInterval(updateServerStats, 300000); // Update every 5 minutes

    // Clean old logs daily
    setInterval(async () => {
        await CommandLogger.cleanOldLogs(30); // Keep logs for 30 days
    }, 24 * 60 * 60 * 1000); // Run daily
});

// Interaction handler
client.on(Events.InteractionCreate, async interaction => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.commands.get(interaction.commandName);

    if (!command) {
        logger.error(`No command matching ${interaction.commandName} was found.`);
        await CommandLogger.logCommand(interaction, false, `Command not found: ${interaction.commandName}`);
        return;
    }

    const startTime = Date.now();

    try {
        await command.execute(interaction);
        const executionTime = Date.now() - startTime;
        await CommandLogger.logCommand(interaction, true, null, executionTime);
    } catch (error) {
        const executionTime = Date.now() - startTime;
        logger.error('Error executing command:', error);
        await CommandLogger.logCommand(interaction, false, error.message, executionTime);

        const errorMessage = 'There was an error while executing this command!';

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true });
        }
    }
});

// Role update processor
async function processRoleUpdates() {
    try {
        const pendingUpdates = await database.getPendingRoleUpdates();
        
        for (const update of pendingUpdates) {
            await processRoleUpdate(update);
            await database.markRoleUpdateProcessed(update.id);
        }
    } catch (error) {
        logger.error('Error processing role updates:', error);
    }
}

async function processRoleUpdate(update) {
    try {
        const guild = client.guilds.cache.get(process.env.GUILD_ID);
        if (!guild) {
            logger.error('Guild not found');
            return;
        }

        const member = await guild.members.fetch(update.discord_id).catch(() => null);
        if (!member) {
            logger.warn(`Member ${update.discord_id} not found in guild`);
            return;
        }

        let roleId;
        switch (update.role_type) {
            case 'lux':
            case 'verification':
                roleId = process.env.VERIFICATION_ROLE_ID;
                break;
            default:
                logger.warn(`Unknown role type: ${update.role_type}`);
                return;
        }

        const role = guild.roles.cache.get(roleId);
        if (!role) {
            logger.error(`Role ${roleId} not found`);
            return;
        }

        if (update.action === 'add') {
            if (!member.roles.cache.has(roleId)) {
                await member.roles.add(role);
                logger.info(`Added ${update.role_type} role to ${member.user.tag}`);
            }
        } else if (update.action === 'remove') {
            if (member.roles.cache.has(roleId)) {
                await member.roles.remove(role);
                logger.info(`Removed ${update.role_type} role from ${member.user.tag}`);
            }
        }
    } catch (error) {
        logger.error('Error processing individual role update:', error);
    }
}

// Server statistics updater
async function updateServerStats() {
    try {
        const guild = client.guilds.cache.get(process.env.GUILD_ID);
        if (!guild) {
            return;
        }

        // Fetch all members to get accurate counts
        await guild.members.fetch();

        const totalMembers = guild.memberCount;
        const verifiedMembers = await database.getVerifiedUsersCount();
        const luxMembers = await database.getLuxUsersCount();

        // Count admin members (users with admin role or permissions)
        const adminMembers = guild.members.cache.filter(member =>
            member.permissions.has('Administrator') && !member.user.bot
        ).size;

        // Count online members
        const onlineMembers = guild.members.cache.filter(member =>
            member.presence?.status === 'online' && !member.user.bot
        ).size;

        // Update database
        await database.updateServerStats(
            guild.id,
            totalMembers,
            verifiedMembers,
            luxMembers,
            adminMembers,
            onlineMembers
        );

        logger.info(`Server stats updated: ${totalMembers} total, ${verifiedMembers} verified, ${luxMembers} lux, ${onlineMembers} online`);

    } catch (error) {
        logger.error('Error updating server stats:', error);
    }
}

// Express server for OAuth2 and API endpoints
const app = express();
const https = require('https');
const fs = require('fs');

app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Simple session storage (in production, use Redis or similar)
app.use((req, res, next) => {
    req.session = req.session || {};
    next();
});

// Note: OAuth routes are now handled by the main LXND dashboard

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        bot: client.isReady() ? 'online' : 'offline',
        timestamp: new Date().toISOString()
    });
});

// Bot stats endpoint
app.get('/stats', async (req, res) => {
    try {
        const guild = client.guilds.cache.get(process.env.GUILD_ID);
        const verifiedCount = await database.getVerifiedUsersCount();
        const luxCount = await database.getLuxUsersCount();

        res.json({
            bot: {
                status: client.isReady() ? 'online' : 'offline',
                uptime: client.uptime,
                ping: client.ws.ping
            },
            guild: guild ? {
                name: guild.name,
                memberCount: guild.memberCount,
                verifiedUsers: verifiedCount,
                luxUsers: luxCount
            } : null,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting stats:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Start Express server
const PORT = process.env.PORT || 3001;

if (PORT == 443) {
    // HTTPS server for port 443
    try {
        const sslOptions = {
            key: fs.readFileSync('../ssl/key.pem'),
            cert: fs.readFileSync('../ssl/cert.pem')
        };

        https.createServer(sslOptions, app).listen(PORT, () => {
            logger.info(`HTTPS Express server running on port ${PORT}`);
        });
    } catch (error) {
        logger.error('Failed to start HTTPS server, falling back to HTTP:', error);
        app.listen(PORT, () => {
            logger.info(`HTTP Express server running on port ${PORT} (HTTPS failed)`);
        });
    }
} else {
    // HTTP server for other ports
    app.listen(PORT, () => {
        logger.info(`HTTP Express server running on port ${PORT}`);
    });
}

// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await database.close();
    client.destroy();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await database.close();
    client.destroy();
    process.exit(0);
});

// Login to Discord
client.login(process.env.DISCORD_TOKEN);
