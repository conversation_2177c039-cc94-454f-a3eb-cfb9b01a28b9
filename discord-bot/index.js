require('dotenv').config();
const { Client, GatewayIntentBits, Collection, Events, ActivityType, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');
const database = require('./config/database');
const CommandLogger = require('./utils/commandLogger');
const VerificationMessage = require('./utils/verificationMessage');
const LuxMonitor = require('./utils/luxMonitor');
const TicketSystem = require('./utils/ticketSystem');
// Express dependencies removed - OAuth2 handled by LXND Panel

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages
    ]
});

// Collections for commands and events
client.commands = new Collection();
client.events = new Collection();

// Load commands
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            logger.info(`Loaded command: ${command.data.name}`);
        } else {
            logger.warn(`Command at ${filePath} is missing required "data" or "execute" property.`);
        }
    }
}

// Load events
const eventsPath = path.join(__dirname, 'events');
if (fs.existsSync(eventsPath)) {
    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));
    
    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        const event = require(filePath);
        
        if (event.once) {
            client.once(event.name, (...args) => event.execute(...args));
        } else {
            client.on(event.name, (...args) => event.execute(...args));
        }
        logger.info(`Loaded event: ${event.name}`);
    }
}

// Basic ready event
client.once(Events.ClientReady, async (readyClient) => {
    logger.info(`🤖 Bot is ready! Logged in as ${readyClient.user.tag}`);
    logger.info(`🏠 Bot is in ${readyClient.guilds.cache.size} guilds`);
    logger.info(`🔧 Role sync interval: 25 seconds`);

    // Set bot activity
    client.user.setActivity('LXND License Management', { type: ActivityType.Watching });
    
    // Connect to database
    const dbConnected = await database.connect();
    if (!dbConnected) {
        logger.error('Failed to connect to database. Bot will continue but database features will not work.');
    }
    
    // Start role update processor
    setInterval(processRoleUpdates, 25000); // Check every 25 seconds

    // Start statistics updater
    setInterval(updateServerStats, 300000); // Update every 5 minutes

    // Clean old logs daily
    setInterval(async () => {
        await CommandLogger.cleanOldLogs(30); // Keep logs for 30 days
    }, 24 * 60 * 60 * 1000); // Run daily

    // Send/update verification message
    await VerificationMessage.sendVerificationMessage(client);

    // Send/update ticket message
    await TicketSystem.sendTicketMessage(client);

    // Update verification message daily
    setInterval(async () => {
        await VerificationMessage.updateVerificationMessage(client);
    }, 24 * 60 * 60 * 1000); // Update daily

    // Update ticket message daily
    setInterval(async () => {
        await TicketSystem.sendTicketMessage(client);
    }, 24 * 60 * 60 * 1000); // Update daily

    // Start Lux monitoring
    LuxMonitor.startMonitoring();
});

// Interaction handler
client.on(Events.InteractionCreate, async interaction => {
    // Handle button interactions
    if (interaction.isButton()) {
        switch (interaction.customId) {
            case 'verify_account':
                await handleVerificationButton(interaction);
                break;
            case 'create_ticket':
                await TicketSystem.createTicket(interaction);
                break;
            case 'ticket_faq':
                await TicketSystem.showFAQ(interaction);
                break;
            case 'close_ticket':
                await TicketSystem.closeTicket(interaction);
                break;
            case 'confirm_close_ticket':
                await TicketSystem.confirmCloseTicket(interaction);
                break;
            case 'cancel_close_ticket':
                await TicketSystem.cancelCloseTicket(interaction);
                break;
            default:
                logger.warn(`Unknown button interaction: ${interaction.customId}`);
        }
        return;
    }

    if (!interaction.isChatInputCommand()) return;

    const command = client.commands.get(interaction.commandName);

    if (!command) {
        logger.error(`No command matching ${interaction.commandName} was found.`);
        await CommandLogger.logCommand(interaction, false, `Command not found: ${interaction.commandName}`);
        return;
    }

    const startTime = Date.now();

    try {
        await command.execute(interaction);
        const executionTime = Date.now() - startTime;
        await CommandLogger.logCommand(interaction, true, null, executionTime);
    } catch (error) {
        const executionTime = Date.now() - startTime;
        logger.error('Error executing command:', error);
        await CommandLogger.logCommand(interaction, false, error.message, executionTime);

        const errorMessage = 'There was an error while executing this command!';

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true });
        }
    }
});

// Role update processor
async function processRoleUpdates() {
    try {
        const pendingUpdates = await database.getPendingRoleUpdates();

        if (pendingUpdates.length > 0) {
            logger.info(`🔄 Processing ${pendingUpdates.length} pending role updates`);
        } else {
            logger.debug('No pending role updates found');
        }

        for (const update of pendingUpdates) {
            logger.info(`⚙️ Processing role update: ${update.role_type} ${update.action} for Discord ID ${update.discord_id}`);
            await processRoleUpdate(update);
            await database.markRoleUpdateProcessed(update.id);
            logger.info(`✅ Completed role update for Discord ID ${update.discord_id}`);
        }

        if (pendingUpdates.length > 0) {
            logger.info(`🎯 Successfully processed ${pendingUpdates.length} role updates`);
        }
    } catch (error) {
        logger.error('❌ Error processing role updates:', error);
    }
}

async function processRoleUpdate(update) {
    try {
        logger.info(`Starting role update: ${update.role_type} ${update.action} for ${update.discord_id}`);

        const guild = client.guilds.cache.get(process.env.GUILD_ID);
        if (!guild) {
            logger.error('Guild not found');
            return;
        }

        const member = await guild.members.fetch(update.discord_id).catch(() => null);
        if (!member) {
            logger.warn(`Member ${update.discord_id} not found in guild`);
            return;
        }

        let roleId;
        switch (update.role_type) {
            case 'verification':
                roleId = process.env.VERIFICATION_ROLE_ID;
                logger.info(`Using verification role ID: ${roleId}`);
                break;
            case 'lux':
                roleId = process.env.LUX_ROLE_ID;
                logger.info(`Using lux role ID: ${roleId}`);
                break;
            default:
                logger.warn(`Unknown role type: ${update.role_type}`);
                return;
        }

        const role = guild.roles.cache.get(roleId);
        if (!role) {
            logger.error(`Role ${roleId} not found in guild`);
            return;
        }

        logger.info(`Found role: ${role.name} (${role.id})`);
        logger.info(`Member: ${member.user.tag} currently has role: ${member.roles.cache.has(roleId)}`);

        if (update.action === 'add') {
            if (!member.roles.cache.has(roleId)) {
                await member.roles.add(role);
                logger.info(`✅ Added ${update.role_type} role to ${member.user.tag}`);

                // Send DM confirmation for verification role
                if (update.role_type === 'verification') {
                    logger.info(`📩 Sending verification success DM to ${member.user.tag}`);
                    await sendVerificationSuccessDM(member.user);
                }
            } else {
                logger.info(`ℹ️ User ${member.user.tag} already has ${update.role_type} role`);
            }
        } else if (update.action === 'remove') {
            if (member.roles.cache.has(roleId)) {
                await member.roles.remove(role);
                logger.info(`🗑️ Removed ${update.role_type} role from ${member.user.tag}`);
            } else {
                logger.info(`ℹ️ User ${member.user.tag} doesn't have ${update.role_type} role to remove`);
            }
        }
    } catch (error) {
        logger.error('Error processing individual role update:', error);
    }
}

// Send verification success DM
async function sendVerificationSuccessDM(user) {
    try {
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Verification Successful!')
            .setDescription('Your Discord account has been successfully linked to your LXND account.')
            .addFields(
                {
                    name: '🎉 Welcome!',
                    value: 'You now have access to all verified member channels and features.',
                    inline: false
                },
                {
                    name: '🔗 What\'s Next?',
                    value: '• Explore the server channels\n• Check out your LXND dashboard\n• Join community discussions\n• Get support when needed',
                    inline: false
                },
                {
                    name: '📋 Your Benefits',
                    value: '• Access to verified channels\n• Automatic role updates based on LXND status\n• Priority support\n• Integration with LXND features',
                    inline: false
                },
                {
                    name: '🌐 Useful Links',
                    value: '[LXND Dashboard](https://lxnd.cloud)\n[Support](https://lxnd.cloud/support)\n[Documentation](https://lxnd.cloud/docs)',
                    inline: false
                }
            )
            .setThumbnail('https://lxnd.cloud/static/images/logo.png')
            .setTimestamp()
            .setFooter({
                text: 'lxnd.cloud',
                iconURL: 'https://lxnd.cloud/static/images/logo.png'
            });

        await user.send({ embeds: [embed] });
        logger.info(`Verification success DM sent to ${user.tag}`);

    } catch (error) {
        logger.warn(`Could not send verification DM to ${user.tag}: ${error.message}`);
    }
}

// Server statistics updater
async function updateServerStats() {
    try {
        const guild = client.guilds.cache.get(process.env.GUILD_ID);
        if (!guild) {
            return;
        }

        // Fetch all members to get accurate counts
        await guild.members.fetch();

        const totalMembers = guild.memberCount;
        const verifiedMembers = await database.getVerifiedUsersCount();
        const luxMembers = await database.getLuxUsersCount();

        // Count admin members (users with admin role or permissions)
        const adminMembers = guild.members.cache.filter(member =>
            member.permissions.has('Administrator') && !member.user.bot
        ).size;

        // Count online members
        const onlineMembers = guild.members.cache.filter(member =>
            member.presence?.status === 'online' && !member.user.bot
        ).size;

        // Update database
        await database.updateServerStats(
            guild.id,
            totalMembers,
            verifiedMembers,
            luxMembers,
            adminMembers,
            onlineMembers
        );

        logger.info(`Server stats updated: ${totalMembers} total, ${verifiedMembers} verified, ${luxMembers} lux, ${onlineMembers} online`);

    } catch (error) {
        logger.error('Error updating server stats:', error);
    }
}

// Note: OAuth2 and web endpoints are handled by the main LXND Python panel
// The bot only handles Discord interactions and database operations

// Handle verification button clicks
async function handleVerificationButton(interaction) {
    try {
        // Check if user is already verified
        const existingUser = await database.getUserByDiscordId(interaction.user.id);

        if (existingUser) {
            const embed = new EmbedBuilder()
                .setColor('#ffaa00')
                .setTitle('⚠️ Already Verified')
                .setDescription(`Your Discord account is already linked to LXND account: **${existingUser.username}**`)
                .addFields({
                    name: 'Linked since',
                    value: existingUser.discord_linked_at ?
                        `<t:${Math.floor(new Date(existingUser.discord_linked_at).getTime() / 1000)}:F>` :
                        'Unknown',
                    inline: true
                })
                .setTimestamp()
                .setFooter({ text: 'lxnd.cloud' });

            return await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
        }

        // Create verification embed with link
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🔗 Start Verification')
            .setDescription('Click the link below to verify your Discord account with your LXND account.')
            .addFields(
                {
                    name: '📋 What you need:',
                    value: '• An active LXND account\n• Access to the email associated with your LXND account',
                    inline: false
                },
                {
                    name: '🔗 Verification Link',
                    value: `[Click here to verify your account](https://lxnd.cloud/oauth/discord/auth?discord_user_id=${interaction.user.id})`,
                    inline: false
                },
                {
                    name: '🔒 Privacy',
                    value: 'We only store your Discord ID, username, and avatar. Your LXND account data remains secure.',
                    inline: false
                }
            )
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        logger.info(`Verification process initiated for user ${interaction.user.tag} (${interaction.user.id})`);

    } catch (error) {
        logger.error('Error in verification button handler:', error);

        const errorEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('❌ Error')
            .setDescription('An error occurred while processing your verification request. Please try again later.')
            .setTimestamp()
            .setFooter({ text: 'lxnd.cloud' });

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

logger.info('Discord bot initialized - OAuth2 handled by LXND Panel');

// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    console.error('Unhandled Rejection:', reason);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await database.close();
    client.destroy();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await database.close();
    client.destroy();
    process.exit(0);
});

// Login to Discord
client.login(process.env.DISCORD_TOKEN);
