{"name": "lxnd-discord-bot", "version": "1.0.0", "description": "LXND Discord Bot with MySQL integration and OAuth2 verification", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "deploy": "node deploy-commands.js", "setup": "node setup-database.js", "test": "node test-setup.js", "test:full": "npm run setup && npm run test && npm run deploy"}, "keywords": ["discord", "bot", "mysql", "oauth2", "verification"], "author": "LXND", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "mysql2": "^3.6.5", "dotenv": "^16.3.1", "axios": "^1.6.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}