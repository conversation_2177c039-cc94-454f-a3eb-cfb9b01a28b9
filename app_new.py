#!/usr/bin/env python3
"""
LXND - All-in-One Platform
Main application entry point
"""

import os
import sys
import ssl
import json
import logging
from datetime import datetime

from flask import Flask, request, jsonify

# Import configuration and database
from config import config
from database import db, init_extensions, create_tables

# Import utilities
from utils.language import init_language_support
from utils.ssl_manager import SSLManager
from utils.startup import startup_menu, detect_environment

def create_app(config_name=None):
    """Application factory"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize configuration
    config[config_name].init_app(app)
    
    # Initialize extensions
    init_extensions(app)
    
    # Initialize language support
    init_language_support(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register template filters
    register_template_filters(app)
    
    # Create database tables
    create_tables(app)
    
    return app

def register_blueprints(app):
    """Register all blueprints"""
    
    # Main routes
    from routes.main import main_bp
    app.register_blueprint(main_bp)
    
    # Authentication routes
    from routes.auth import auth_bp
    app.register_blueprint(auth_bp)
    
    # Register new blueprints
    try:
        from routes.projects import projects_bp
        app.register_blueprint(projects_bp, url_prefix='/projects')
        print("  ✓ Projects blueprint registered")
    except ImportError as e:
        print(f"  ❌ Projects blueprint failed: {e}")

    try:
        from routes.files import files_bp
        app.register_blueprint(files_bp, url_prefix='/files')
        print("  ✓ Files blueprint registered")
    except ImportError as e:
        print(f"  ❌ Files blueprint failed: {e}")

    try:
        from routes.email import email_bp
        app.register_blueprint(email_bp, url_prefix='/email')
        print("  ✓ Email blueprint registered")
    except ImportError as e:
        print(f"  ❌ Email blueprint failed: {e}")

    try:
        from routes.admin import admin_bp
        app.register_blueprint(admin_bp, url_prefix='/admin')
        print("  ✓ Admin blueprint registered")
    except ImportError as e:
        print(f"  ❌ Admin blueprint failed: {e}")

    try:
        from routes.shortener import shortener_bp
        app.register_blueprint(shortener_bp, url_prefix='/shortener')
        print("  ✓ Shortener blueprint registered")
    except ImportError as e:
        print(f"  ❌ Shortener blueprint failed: {e}")

    try:
        from routes.websites import websites_bp
        app.register_blueprint(websites_bp, url_prefix='/websites')
        print("  ✓ Websites blueprint registered")
    except ImportError as e:
        print(f"  ❌ Websites blueprint failed: {e}")

    try:
        from routes.discord import discord_bp
        app.register_blueprint(discord_bp, url_prefix='/auth/discord')
        print("  ✓ Discord blueprint registered")
    except ImportError as e:
        print(f"  ❌ Discord blueprint failed: {e}")

    # Import remaining routes from original app.py temporarily
    # These will be gradually moved to separate blueprint files
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("original_app", "app.py")
        if spec and spec.loader:
            original_app = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(original_app)

            # Copy remaining routes that haven't been migrated yet
            # This is a temporary solution during migration
            print("  ✓ Legacy routes loaded")
    except Exception as e:
        print(f"  ❌ Warning: Could not load legacy routes: {e}")
        print("    Some routes may not be available until migration is complete.")

def register_error_handlers(app):
    """Register error handlers"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return jsonify({'error': 'Forbidden'}), 403

def register_template_filters(app):
    """Register custom template filters"""
    
    @app.template_filter('from_json')
    def from_json_filter(value):
        """Convert JSON string to Python object"""
        try:
            return json.loads(value) if value else []
        except:
            return []
    
    @app.template_filter('regex_replace')
    def regex_replace_filter(value, pattern, replacement):
        """Replace text using regex pattern"""
        import re
        try:
            return re.sub(pattern, replacement, str(value))
        except:
            return value

def main():
    """Main entry point"""
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Detect environment and show startup menu
    env = detect_environment()
    
    if env == 'development':
        # Development mode - run with Flask dev server
        app = create_app('development')
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    elif env == 'production':
        # Production mode - run with SSL
        app = create_app('production')
        
        # Initialize SSL manager
        ssl_manager = SSLManager(app.config['SSL_CERT_PATH'], app.config['SSL_DOMAINS'])
        ssl_context = ssl_manager.get_ssl_context()
        
        if ssl_context:
            print("🔒 Starting LXND with SSL encryption...")
            print("🌐 Server running at: https://lxnd.cloud")
            app.run(host='0.0.0.0', port=443, ssl_context=ssl_context)
        else:
            print("❌ SSL certificates not found. Starting without SSL...")
            app.run(host='0.0.0.0', port=5000)
    
    else:
        # Interactive mode
        choice = startup_menu()
        
        if choice == '1':
            # HTTP only
            app = create_app('development')
            print("🌐 Starting LXND in HTTP mode...")
            print("🔗 Server running at: http://localhost:5000")
            app.run(debug=True, host='0.0.0.0', port=5000)
            
        elif choice == '2':
            # HTTPS only
            app = create_app('production')
            ssl_manager = SSLManager(app.config['SSL_CERT_PATH'], app.config['SSL_DOMAINS'])
            ssl_context = ssl_manager.get_ssl_context()
            
            if ssl_context:
                print("🔒 Starting LXND with SSL encryption...")
                print("🌐 Server running at: https://lxnd.cloud")
                app.run(host='0.0.0.0', port=443, ssl_context=ssl_context)
            else:
                print("❌ SSL certificates not found!")
                sys.exit(1)
                
        elif choice == '3':
            # Auto-detect
            app = create_app('production')
            ssl_manager = SSLManager(app.config['SSL_CERT_PATH'], app.config['SSL_DOMAINS'])
            ssl_context = ssl_manager.get_ssl_context()
            
            if ssl_context:
                print("🔒 SSL certificates found. Starting with HTTPS...")
                print("🌐 Server running at: https://lxnd.cloud")
                app.run(host='0.0.0.0', port=443, ssl_context=ssl_context)
            else:
                print("⚠️  SSL certificates not found. Starting with HTTP...")
                print("🔗 Server running at: http://localhost:5000")
                app.run(debug=True, host='0.0.0.0', port=5000)
        
        else:
            print("❌ Invalid choice. Exiting...")
            sys.exit(1)

if __name__ == '__main__':
    main()
