INFO:mailserver:System hostname set to vserver2.zuchtbude.de
INFO:mailserver:Creating mailserver database schema...
INFO:mailserver:Mailserver database schema created/verified successfully.
INFO:mailserver:TLS context created successfully
INFO:mailserver:SMTP server will bind to 0.0.0.0:25
INFO:mailserver:Starting SMTP server on 0.0.0.0:25 for domain lxnd.cloud
INFO:mail.log:Available AUTH mechanisms: LOGIN(builtin) PLAIN(builtin)
INFO:mail.log:Peer: ('127.0.0.1', 35496)
INFO:mail.log:('127.0.0.1', 35496) handling connection
INFO:mailserver:SMTP server started successfully
INFO:mail.log:('127.0.0.1', 35496) EOF received
INFO:mail.log:('127.0.0.1', 35496) Connection lost during _handle_client()
INFO:mail.log:('127.0.0.1', 35496) connection lost
INFO:mailserver:Integrated mailserver started successfully
✓ Integrated mailserver started on port 25 (production)
✓ IMAP server started on port 993 (SSL)
Do you want to start the Discord bot? (Y/N): Background mode: Discord bot disabled, SSL disabled
 * Serving Flask app 'app'
 * Debug mode: off
INFO:imap_server:IMAP server started on 0.0.0.0:993
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:80
 * Running on http://**************:80
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug:************** - - [16/Jul/2025 00:53:37] "GET / HTTP/1.1" 200 -
INFO:mail.log:Available AUTH mechanisms: LOGIN(builtin) PLAIN(builtin)
INFO:mail.log:Peer: ('**************', 47533)
INFO:mail.log:('**************', 47533) handling connection
INFO:mail.log:('**************', 47533) >> b'EHLO mail-il1-f201.google.com'
INFO:mail.log:('**************', 47533) >> b'STARTTLS'
INFO:mail.log:('**************', 47533) >> b'EHLO mail-il1-f201.google.com'
INFO:mail.log:('**************', 47533) >> b'MAIL FROM:<<EMAIL>> SIZE=9032'
INFO:mail.log:('**************', 47533) sender: <EMAIL>
INFO:mail.log:('**************', 47533) >> b'RCPT TO:<<EMAIL>>'
INFO:mailserver:SMTP: Connection from ('**************', 47533) checking recipient '<EMAIL>'
INFO:mailserver:SMTP: Recipient '<EMAIL>' accepted from ('**************', 47533)
INFO:mail.log:('**************', 47533) recip: <EMAIL>
INFO:mail.log:('**************', 47533) >> b'DATA'
INFO:mailserver:SMTP: Processing <NAME_EMAIL> to ['<EMAIL>'] (from ('**************', 47533))
INFO:mailserver:Received email stored for '<EMAIL>' from 'Google <<EMAIL>>' (ID: <<EMAIL>>)
INFO:mailserver:SMTP: Email stored for '<EMAIL>'
INFO:mailserver:SMTP: Successfully processed email from Google <<EMAIL>> to 1 recipient(s)
INFO:mail.log:('**************', 47533) >> b'QUIT'
INFO:mail.log:('**************', 47533) EOF received
INFO:mail.log:('**************', 47533) Connection lost during _handle_client()
INFO:mail.log:('**************', 47533) connection lost
INFO:werkzeug:************** - - [16/Jul/2025 00:55:46] "GET / HTTP/1.1" 200 -
